/**
 * Application Configuration
 * 
 * Consolidated configuration constants including app info, defaults, and financial settings
 */

// Import version from package.json to maintain single source of truth
import packageJson from '../../package.json';

export const APP_INFO = {
  NAME: 'Salesmate',
  VERSION: packageJson.version,
  BUILD: '1',
};

export const DEFAULTS = {
  PAGINATION_LIMIT: 10,
  DATE_FORMAT: 'YYYY-MM-DD',
  CURRENCY: 'UGX',
  CURRENCY_SYMBOL: 'UGX',
  DECIMAL_PLACES: 0,
};

// Financial display and formatting constants
export const FINANCIAL = {
  CURRENCY_REPLACE: 'UGX ',
  PLACEHOLDER: '--',
  LABELS: {
    TIN: 'TIN: ',
    RATE: 'Rate',
    AMOUNT: 'Amount',
    LAST_UPDATED: 'Last updated: ',
  },
};

// Date and time formatting
export const DATE_TIME = {
  LOCALE: 'en-US',
  DISPLAY_OPTIONS: {
    month: 'short' as const,
    day: 'numeric' as const,
    hour: 'numeric' as const,
    minute: 'numeric' as const,
    hour12: true as const,
  },
};

// Business rules and validation
export const BUSINESS_RULES = {
  MIN_QUANTITY: 0,
  MIN_CART_ITEMS: 1,
  CART_BADGE_WIDE_THRESHOLD: 9,
  EMPTY_ARRAY_LENGTH: 0,
};

// Filter defaults
export const FILTER_DEFAULTS = {
  ORDER_CREATION_FILTER_TYPE: 'brand' as const,
};
