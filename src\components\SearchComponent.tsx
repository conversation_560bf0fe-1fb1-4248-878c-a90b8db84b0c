import React, { forwardRef, memo, useCallback, useRef, useEffect, useState } from 'react';
import { View, Keyboard, BackHandler } from 'react-native';
import { TextInput, useTheme } from 'react-native-paper';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';
import { searchComponentStyles as styles } from '../styles/SearchComponent';

interface SearchComponentProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  onSubmitEditing?: () => void;
  onClear?: () => void;
  fullWidth?: boolean;
}


const SearchComponent = memo(forwardRef<any, SearchComponentProps>(({
  value,
  onChangeText,
  placeholder = 'Search',
  autoFocus = false,
  onSubmitEditing,
  onClear
}, ref) => {
  const theme = useTheme();
  const inputRef = useRef<any>(null);
  const [localValue, setLocalValue] = useState(value || '');
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Only update local value from props when value changes and is different
  useEffect(() => {
    if (isMountedRef.current && value !== undefined && value !== localValue) {
      setLocalValue(value);
    }
  }, [value]);

  const handleChangeText = useCallback((text: string) => {
    setLocalValue(text);
    onChangeText(text);
  }, [onChangeText]);

  const handleClear = useCallback(() => {
    // Immediate UI feedback - update local state first
    setLocalValue('');

    // Use requestAnimationFrame to defer heavy operations
    requestAnimationFrame(() => {
      onChangeText('');

      if (inputRef.current) {
        inputRef.current.blur();
      }

      Keyboard.dismiss();

      if (onClear) {
        onClear();
      }
    });
  }, [onChangeText, onClear]);

  const handleSubmitEditing = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.blur();
    }
    Keyboard.dismiss();

    if (onSubmitEditing) {
      onSubmitEditing();
    }
  }, [onSubmitEditing]);

  useEffect(() => {
    if (autoFocus) {
      const focusTimeout = setTimeout(() => {
        const inputToFocus = ref && typeof ref !== 'function' ? ref.current : inputRef.current;
        if (inputToFocus) {
          inputToFocus.focus();
        }
      }, 100);

      return () => clearTimeout(focusTimeout);
    }
  }, [autoFocus, ref]);

  const handleKeyboardDismiss = useCallback(() => {
    if (inputRef.current && inputRef.current.isFocused()) {
      inputRef.current.blur();
    }
  }, []);

  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      handleKeyboardDismiss
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, [handleKeyboardDismiss]);

  const handleBackPress = useCallback(() => {
    if (inputRef.current && inputRef.current.isFocused()) {
      inputRef.current.blur();
      Keyboard.dismiss();
      return true; // Prevent default back action
    }
    return false;
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [handleBackPress]);

  return (
    <View style={styles.container}>
      <TextInput
        ref={ref || inputRef}
        value={localValue}
        onChangeText={handleChangeText}
        placeholder={placeholder}
        placeholderTextColor={NEUTRAL.TEXT_SECONDARY}
        mode="outlined"
        outlineColor={MD.DIVIDER}
        activeOutlineColor={MD.DIVIDER}
        selectionColor={theme.colors.primary}
        outlineStyle={styles.outlineStyle}
        style={styles.input}
        theme={{
          roundness: 28,
          colors: {
            placeholder: NEUTRAL.TEXT_SECONDARY,
            text: NEUTRAL.TEXT_PRIMARY,
          }
        }}
        dense={true}
        autoCapitalize="none"
        autoCorrect={false}
        returnKeyType="search"
        autoFocus={autoFocus}
        onSubmitEditing={handleSubmitEditing}
        left={
          <TextInput.Icon
            icon="magnify"
            color={localValue ? theme.colors.primary : NEUTRAL.TEXT_SECONDARY}
            style={styles.searchIcon}
            disabled={true} // Make the icon non-clickable
          />
        }
        right={
          localValue ?
            <TextInput.Icon
              icon="close"
              color={theme.colors.primary}
              size={20}
              onPress={handleClear}
              style={styles.clearIcon}
            /> : null
        }
      />
    </View>
  );
}));

export default SearchComponent;
