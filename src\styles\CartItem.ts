import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const cartItemStyles = StyleSheet.create({
  itemContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL,
    marginHorizontal: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
    position: 'relative',
    elevation: MD.ELEVATION.LIGHT,
  },
  deleteButton: {
    position: 'absolute',
    top: 2,
    right: 2,
    zIndex: 1,
    padding: 4,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    flex: 1,
    marginBottom: MD.SPACING.XSMALL / 2,
    lineHeight: 18,
    minHeight: 36,
    paddingRight: 22,
  },
  itemDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: MD.SPACING.XSMALL,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
});
