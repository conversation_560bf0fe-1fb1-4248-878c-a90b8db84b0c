/**
 * Storage Constants
 * 
 * Contains all storage-related constants including keys and cache configuration
 */

export const STORAGE_KEYS = {
  // Authentication (using obscure names for security)
  SID: '_a1x9t',  // Obscure name for SID
  SID_EXPIRES_AT: '_v4l1d',  // Obscure name for expiration
  LAST_EMAIL: '_u1d3nt',  // Obscure name for email
  FIRST_NAME: '_d1sp',  // Obscure name for first name
  SAVED_PASSWORD: '_p4ss',  // Obscure name for saved password
  REMEMBER_ME: '_r3m3m',  // Obscure name for remember me setting

  // App settings
  THEME: 'app_theme',
  LANGUAGE: 'app_language',

  // Cache keys
  CART_PREFIX: 'cart_', // Prefix for cart storage keys
  LAST_SYNC: 'last_sync_time',

  // Quantity cache keys
  ACTUAL_QUANTITIES_CACHE: 'actual_quantities_cache',
  ACTUAL_QUANTITIES_TIMESTAMP: 'actual_quantities_timestamp',
};

export const DATABASE = {
  NAME: 'salesmate.db',
  VERSION: 1,
  TABLES: {
    ITEMS: 'items',
    ITEM_PRICES: 'item_prices'
  },
  OPERATION: {
    RETRY_COUNT: 2,
    RETRY_DELAY: 200, // ms
    CLOSE_DELAY: 200, // ms to wait after closing database
  },
};

export const CACHE = {
  // Database cache settings
  DATABASE_TTL: 600000, // 10 minute cache TTL
  DATABASE_MAX_SIZE: 20, // Maximum cache size

  // General cache settings
  DEFAULT_EXPIRY: 5 * 60 * 1000, // 5 minutes in milliseconds

  // Sync settings
  SYNC_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  SYNC_TIMEOUT: 60 * 1000, // 60 second timeout for sync operations

  // Quantity cache settings
  QUANTITY_CACHE_DURATION: 5 * 60 * 1000, // 5 minutes in milliseconds
  QUANTITY_BACKGROUND_SYNC_INTERVAL: 3 * 60 * 1000, // 3 minutes in milliseconds
};
