import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const customerCardStyles = StyleSheet.create({
  listItem: {
    backgroundColor: NEUTRAL.WHITE,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  selectedItem: {
    borderLeftColor: BRAND.PRIMARY,
    borderLeftWidth: 3,
  },
  itemContent: {
    width: '100%',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 0,
  },
  nameContainer: {
    flex: 1,
    marginRight: MD.SPACING.XSMALL,
    flexShrink: 1,
    minHeight: 72,
  },
  customerName: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '400',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  customerDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: MD.SPACING.XSMALL / 2,
    minHeight: MD.SPACING.LARGE,
  },
  customerDetailsVertical: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginTop: MD.SPACING.XSMALL / 2,
    minHeight: MD.SPACING.LARGE + MD.SPACING.XSMALL,
  },
  customerTerritory: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: BRAND.PRIMARY,
    fontWeight: '500',
    marginRight: MD.SPACING.SMALL,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  customerId: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  customerIdVertical: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    marginTop: MD.SPACING.XSMALL / 4,
  },
  headerFinancialContainer: {
    alignItems: 'flex-end',
    marginLeft: MD.SPACING.XSMALL / 2,
    minWidth: 90,
    flexShrink: 0,
  },
  headerFinancialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: MD.SPACING.XSMALL / 4,
    justifyContent: 'flex-end',
    flexWrap: 'nowrap',
  },
  headerFinancialIcon: {
    marginRight: 0,
    width: 12,
    alignSelf: 'center',
  },
  valueContainer: {
    minWidth: 75,
    alignItems: 'flex-end',
    flexShrink: 0,
  },
  headerFinancialValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_PRIMARY,
    fontWeight: '500',
    textAlign: 'right',
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  outstandingValue: {
    color: SEMANTIC.ERROR,
  },
  emptyFinancialPlaceholder: {
    height: 36,
  },
  addressContainer: {
    marginTop: MD.SPACING.XSMALL / 2,
    height: 32,
  },
  addressText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    lineHeight: 16,
  },
});
