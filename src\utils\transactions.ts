/**
 * Transaction Utilities
 * 
 * Consolidated transaction-related utilities combining functionality from:
 * - transactionFormatters.ts
 * - transactionHelpers.ts
 */

import { SalesOrder, SalesInvoice, Transaction, TransactionItem } from '../types/business';

// ===== Transaction Type Guards =====

/**
 * Type guard to check if a transaction is a SalesOrder
 */
export const isSalesOrder = (transaction: SalesOrder | SalesInvoice): transaction is SalesOrder => {
  return 'transaction_date' in transaction;
};

/**
 * Type guard to check if a transaction is a SalesInvoice
 */
export const isSalesInvoice = (transaction: SalesOrder | SalesInvoice): transaction is SalesInvoice => {
  return 'posting_date' in transaction;
};

/**
 * Get transaction type from transaction object
 */
export const getTransactionType = (transaction: Transaction): 'order' | 'invoice' => {
  return isSalesOrder(transaction) ? 'order' : 'invoice';
};

// ===== Transaction Date Utilities =====

/**
 * Get the date field from a transaction based on its type
 */
export const getTransactionDate = (transaction: SalesOrder | SalesInvoice): string => {
  if (isSalesOrder(transaction)) {
    return transaction.transaction_date;
  } else {
    return transaction.posting_date;
  }
};

/**
 * Get the appropriate date label for a transaction type
 */
export const getDateLabel = (transactionType: 'order' | 'invoice'): string => {
  return transactionType === 'order' ? 'Transaction Date' : 'Posting Date';
};

/**
 * Get additional date label for invoices (due date)
 */
export const getAdditionalDateLabel = (transactionType: 'order' | 'invoice'): string | null => {
  return transactionType === 'invoice' ? 'Due Date' : null;
};

/**
 * Get the additional date value for invoices
 */
export const getAdditionalDateValue = (transaction: Transaction): string | null => {
  if (isSalesInvoice(transaction)) {
    return transaction.due_date;
  }
  return null;
};

// ===== Transaction Display Utilities =====

/**
 * Get the appropriate title for a transaction type
 */
export const getTransactionTitle = (transactionType: 'order' | 'invoice'): string => {
  return transactionType === 'order' ? 'Order Details' : 'Invoice Details';
};

/**
 * Get the appropriate icon for a transaction type
 */
export const getTransactionIcon = (transactionType: 'order' | 'invoice'): string => {
  return transactionType === 'order' ? 'receipt' : 'file-document';
};

/**
 * Get the status color for a transaction (optimized with memoization)
 */
const statusColorCache = new Map<string, string>();
export const getStatusColor = (status: string, statusColors: Record<string, string>): string => {
  const cacheKey = status;
  if (statusColorCache.has(cacheKey)) {
    return statusColorCache.get(cacheKey)!;
  }

  const color = statusColors[status] || '#9E9E9E';
  statusColorCache.set(cacheKey, color);
  return color;
};

/**
 * Get transaction status color key (optimized)
 */
export const getStatusColorKey = (status: string): string => status;

/**
 * Format transaction ID for display (optimized)
 */
export const formatTransactionId = (transactionId: string): string => transactionId;

// ===== Transaction Financial Information =====

/**
 * Check if transaction has outstanding amount (invoices only)
 */
export const hasOutstandingAmount = (transaction: Transaction): boolean => {
  return isSalesInvoice(transaction) && transaction.outstanding_amount > 0;
};

/**
 * Get outstanding amount for invoices
 */
export const getOutstandingAmount = (transaction: Transaction): number => {
  if (isSalesInvoice(transaction)) {
    return transaction.outstanding_amount;
  }
  return 0;
};

/**
 * Get payment terms for transaction
 */
export const getPaymentTerms = (transaction: Transaction): string | null => {
  return transaction.payment_terms_template || null;
};

/**
 * Calculate transaction summary
 */
export const getTransactionSummary = (transaction: Transaction) => {
  const summary = {
    subtotal: transaction.base_net_total,
    taxes: transaction.base_total_taxes_and_charges,
    discount: transaction.discount_amount || 0,
    total: transaction.base_grand_total,
  };
  
  // Add outstanding amount for invoices
  if (isSalesInvoice(transaction)) {
    return {
      ...summary,
      outstanding: transaction.outstanding_amount,
    };
  }
  
  return summary;
};

// ===== Transaction Items =====

/**
 * Get transaction items count
 */
export const getItemsCount = (transaction: Transaction): number => {
  return transaction.items?.length || 0;
};

/**
 * Check if transaction has items
 */
export const hasItems = (transaction: Transaction): boolean => {
  return getItemsCount(transaction) > 0;
};

// ===== Transaction Additional Fields =====

/**
 * Get additional transaction fields based on type
 */
export const getAdditionalFields = (transaction: Transaction): Record<string, any> => {
  const fields: Record<string, any> = {};
  
  if (isSalesOrder(transaction)) {
    if (transaction.po_no) {
      fields['PO Number'] = transaction.po_no;
    }
  } else if (isSalesInvoice(transaction)) {
    if (transaction.fiscal_document_number) {
      fields['Fiscal Document'] = transaction.fiscal_document_number;
    }
  }
  
  return fields;
};

// ===== Transaction Data Processing =====

/**
 * Format transaction filters for API request
 */
export const buildTransactionFilters = (
  searchQuery: string,
  selectedStatuses: string[]
): any[] => {
  const filters = [];

  // Add status filter if selected
  if (selectedStatuses.length > 0) {
    filters.push(['status', 'in', selectedStatuses]);
  }

  // Add search filter if query exists
  if (searchQuery && searchQuery.trim()) {
    filters.push(['customer_name', 'like', `%${searchQuery.trim()}%`]);
  }

  return filters;
};

/**
 * Convert selected filter values to FilterOption format for FilterBar component
 */
export const convertFiltersToFilterOptions = (
  selectedFilters: string[],
  transactionType: 'order' | 'invoice' // Used for future filter type customization
): Array<{ id: string; label: string; type: 'filter' }> => {
  const filterOptions: Array<{ id: string; label: string; type: 'filter' }> = [];

  selectedFilters.forEach(filterId => {
    // Handle status filters (format: "status:Draft" or just "Draft")
    if (filterId.startsWith('status:')) {
      const status = filterId.replace('status:', '');
      filterOptions.push({
        id: filterId,
        label: status,
        type: 'filter'
      });
    } else if (filterId.includes(':')) {
      // Handle other filter types (format: "type:value")
      const [filterType, filterValue] = filterId.split(':');
      const label = filterType === 'customer' ? `Customer: ${filterValue}` :
                   filterType === 'amount' ? `Amount: ${filterValue}` :
                   filterType === 'date' ? `Date: ${filterValue}` :
                   `${filterType}: ${filterValue}`;

      filterOptions.push({
        id: filterId,
        label,
        type: 'filter'
      });
    } else {
      // Handle simple status filters (just the status name)
      filterOptions.push({
        id: filterId,
        label: filterId,
        type: 'filter'
      });
    }
  });

  return filterOptions;
};

/**
 * Remove duplicate transactions when appending new data
 */
export const mergeTransactionsWithoutDuplicates = <T extends { name: string }>(
  existingTransactions: T[],
  newTransactions: T[]
): T[] => {
  // Create a Set of existing transaction IDs for fast lookup
  const existingIds = new Set(existingTransactions.map(item => item.name));

  // Filter out any transactions that already exist in the list
  const uniqueNewTransactions = newTransactions.filter(item => !existingIds.has(item.name));

  // Return combined array with unique transactions
  return [...existingTransactions, ...uniqueNewTransactions];
};

// ===== Reorder Functionality =====

/**
 * Check if a transaction can be reordered
 */
export const canBeReordered = (transaction: Transaction): boolean => {
  // Support both sales orders and sales invoices
  if (!isSalesOrder(transaction) && !isSalesInvoice(transaction)) {
    return false;
  }

  // Check if transaction has items
  if (!transaction.items || transaction.items.length === 0) {
    return false;
  }

  // Check if all items are valid
  if (!areValidTransactionItems(transaction.items)) {
    return false;
  }

  // Check if transaction is not cancelled
  if (transaction.status === 'Cancelled') {
    return false;
  }

  // For invoices, additional validation can be added here if needed

  return true;
};

/**
 * @deprecated Use canBeReordered instead for more comprehensive validation
 */
export const canReorderTransaction = (transaction: Transaction): boolean => {
  return canBeReordered(transaction);
};

/**
 * Get reorder button text
 */
export const getReorderButtonText = (): string => {
  return 'Reorder';
};

/**
 * Get reorder confirmation message
 */
export const getReorderConfirmationMessage = (transactionId: string): string => {
  return `Do you want to reorder all items from ${transactionId}?`;
};

// ===== Transaction Validation =====

/**
 * Validate if a transaction object is valid
 */
export const isValidTransaction = (transaction: any): transaction is Transaction => {
  if (!transaction || typeof transaction !== 'object') {
    return false;
  }

  // Check required base fields
  const requiredFields = ['name', 'customer_name', 'status', 'base_grand_total'];
  for (const field of requiredFields) {
    if (!(field in transaction)) {
      return false;
    }
  }

  // Check type-specific fields
  if (isSalesOrder(transaction)) {
    return 'transaction_date' in transaction;
  } else if (isSalesInvoice(transaction)) {
    return 'posting_date' in transaction && 'due_date' in transaction && 'outstanding_amount' in transaction;
  }

  return false;
};

/**
 * Validate transaction item
 */
export const isValidTransactionItem = (item: any): item is TransactionItem => {
  if (!item || typeof item !== 'object') {
    return false;
  }

  const requiredFields = ['name', 'item_code', 'item_name', 'qty', 'rate', 'amount', 'uom'];
  return requiredFields.every(field => field in item);
};

/**
 * Validate transaction items array
 */
export const areValidTransactionItems = (items: any[]): items is TransactionItem[] => {
  if (!Array.isArray(items)) {
    return false;
  }

  return items.every(isValidTransactionItem);
};

/**
 * Validate transaction ID format
 */
export const isValidTransactionId = (id: string): boolean => {
  return typeof id === 'string' && id.trim().length > 0;
};

/**
 * Validate transaction type
 */
export const isValidTransactionType = (type: string): type is 'order' | 'invoice' => {
  return type === 'order' || type === 'invoice';
};

/**
 * Validate transaction status
 */
export const isValidTransactionStatus = (status: string): boolean => {
  const validStatuses = [
    'Draft', 'On Hold', 'To Deliver and Bill', 'To Bill', 'To Deliver',
    'Completed', 'Cancelled', 'Closed', 'Partly Delivered', 'Partly Billed',
    'Paid', 'Unpaid', 'Overdue', 'Return', 'Credit Note Issued', 'Submitted',
    'Partly Paid', 'Unpaid and Discounted', 'Partly Paid and Discounted'
  ];

  return validStatuses.includes(status);
};

/**
 * Validate transaction amounts
 */
export const hasValidAmounts = (transaction: Transaction): boolean => {
  const amounts = [
    transaction.base_total,
    transaction.base_net_total,
    transaction.base_total_taxes_and_charges,
    transaction.base_grand_total
  ];

  return amounts.every(amount => typeof amount === 'number' && !isNaN(amount) && amount >= 0);
};

/**
 * Validate transaction dates
 */
export const hasValidDates = (transaction: Transaction): boolean => {
  if (isSalesOrder(transaction)) {
    return isValidDateString(transaction.transaction_date);
  } else if (isSalesInvoice(transaction)) {
    return isValidDateString(transaction.posting_date) && isValidDateString(transaction.due_date);
  }

  return false;
};

/**
 * Validate date string format
 */
export const isValidDateString = (dateString: string): boolean => {
  if (typeof dateString !== 'string') {
    return false;
  }

  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * Validate customer name
 */
export const isValidCustomerName = (customerName: string): boolean => {
  return typeof customerName === 'string' && customerName.trim().length > 0;
};

/**
 * Validate search query
 */
export const isValidSearchQuery = (query: string): boolean => {
  return typeof query === 'string' && query.length <= 100; // Reasonable length limit
};

/**
 * Validate filter statuses
 */
export const areValidFilterStatuses = (statuses: string[]): boolean => {
  if (!Array.isArray(statuses)) {
    return false;
  }

  return statuses.every(isValidTransactionStatus);
};

/**
 * Validate pagination parameters
 */
export const areValidPaginationParams = (page: number, pageSize: number): boolean => {
  return (
    typeof page === 'number' &&
    typeof pageSize === 'number' &&
    page > 0 &&
    pageSize > 0 &&
    pageSize <= 100 // Reasonable page size limit
  );
};

/**
 * Sanitize search query
 */
export const sanitizeSearchQuery = (query: string): string => {
  if (typeof query !== 'string') {
    return '';
  }

  return query.trim().substring(0, 100); // Limit length and trim whitespace
};

/**
 * Sanitize transaction ID
 */
export const sanitizeTransactionId = (id: string): string => {
  if (typeof id !== 'string') {
    return '';
  }

  return id.trim();
};
