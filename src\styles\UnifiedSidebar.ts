import { StyleSheet } from 'react-native';
import { BRAND, NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const unifiedSidebarStyles = StyleSheet.create({
  sidebarContainer: {
    width: 65, // Same width as original SupplierSidebar
    backgroundColor: NEUTRAL.WHITE,
    borderRightWidth: 1,
    borderRightColor: MD.DIVIDER,
    flexDirection: 'column',
  },
  
  // Header styles for simplified sidebar
  headerContainer: {
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.XXSMALL,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
    textAlign: 'center',
    lineHeight: 12,
  },
  
  // Scrollable content styles
  sidebarContent: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: MD.SPACING.XSMALL,
  },
  
  // Item tab styles (similar to original SupplierSidebar)
  itemTab: {
    position: 'relative',
    paddingVertical: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.XXSMALL / 2,
    marginHorizontal: MD.SPACING.XXSMALL / 2,
    marginVertical: MD.SPACING.XXSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    backgroundColor: 'transparent',
    minHeight: 50,
    justifyContent: 'center',
  },
  selectedItemTab: {
    backgroundColor: BRAND.PRIMARY_50,
  },
  pressedItemTab: {
    backgroundColor: BRAND.PRIMARY_10,
    transform: [{ scale: 0.95 }],
  },
  tabContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: MD.SPACING.XXSMALL / 2,
  },
  itemTabText: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px for compact design
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    lineHeight: 12,
    marginBottom: MD.SPACING.XXSMALL / 2,
  },
  selectedItemTabText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },
  itemCount: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px
    fontWeight: '500',
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 12,
  },
  selectedItemCount: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },
  
  // Active indicator
  activeIndicator: {
    position: 'absolute',
    right: 0,
    top: '50%',
    marginTop: -8,
    width: 3,
    height: 16,
    backgroundColor: BRAND.PRIMARY,
    borderTopLeftRadius: 2,
    borderBottomLeftRadius: 2,
  },
  
  // Divider
  itemDivider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginHorizontal: MD.SPACING.XSMALL,
    opacity: 0.5,
  },
});
