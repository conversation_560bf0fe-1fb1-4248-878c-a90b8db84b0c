{"name": "salesmate", "main": "expo-router/entry", "version": "1.1.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "test": "jest --watchAll", "lint": "expo lint", "sync-version": "node ./scripts/sync-version.js"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@shopify/flash-list": "1.7.6", "axios": "^1.8.4", "expo": "53.0.20", "expo-blur": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sqlite": "~15.2.14", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.9", "typescript": "~5.8.3"}, "private": true}