import React from 'react';
import { View, Text } from 'react-native';
import { Avatar, Button } from 'react-native-paper';
import { transactionEmptyStateStyles } from '../styles/TransactionEmptyState';
import { generateAccessibilityLabel, getAccessibilityHint } from '../utils/ui';

interface TransactionEmptyStateProps {
  transactionType: 'order' | 'invoice';
  searchQuery: string;
  selectedStatuses: string[];
  loading?: boolean;
  onClearFilters?: () => void;
}

const TransactionEmptyState: React.FC<TransactionEmptyStateProps> = ({
  transactionType,
  searchQuery,
  selectedStatuses,
  loading = false,
  onClearFilters,
}) => {
  // Never render empty state during loading
  if (loading) {
    return null;
  }

  // Determine the appropriate message based on filters
  const getEmptyMessage = () => {
    const hasFilters = searchQuery.trim() || selectedStatuses.length > 0;
    const typeName = transactionType === 'order' ? 'orders' : 'invoices';

    if (hasFilters) {
      return {
        title: `No ${typeName} found`,
        subtitle: 'Try adjusting your search or filters',
        showClearButton: true,
      };
    }

    return {
      title: `No ${typeName} yet`,
      subtitle: `${typeName === 'orders' ? 'Orders' : 'Invoices'} will appear here once created`,
      showClearButton: false,
    };
  };

  const { title, subtitle, showClearButton } = getEmptyMessage();
  const iconName = transactionType === 'order' ? 'receipt' : 'file-document';

  // Generate accessibility label for the empty state
  const emptyStateAccessibilityLabel = generateAccessibilityLabel(
    title,
    [subtitle, showClearButton ? 'Clear filters button available' : ''].filter(Boolean)
  );

  return (
    <View
      style={transactionEmptyStateStyles.emptyContainer}
      accessible={true}
      accessibilityRole="text"
      accessibilityLabel={emptyStateAccessibilityLabel}
    >
      <Avatar.Icon
        size={64}
        icon={iconName}
        style={transactionEmptyStateStyles.emptyIcon}
        accessible={false}
      />
      <Text
        style={transactionEmptyStateStyles.emptyText}
        accessible={false} // Handled by parent container
      >
        {title}
      </Text>
      <Text
        style={transactionEmptyStateStyles.emptySubText}
        accessible={false} // Handled by parent container
      >
        {subtitle}
      </Text>

      {showClearButton && onClearFilters && (
        <Button
          mode="outlined"
          onPress={onClearFilters}
          style={transactionEmptyStateStyles.clearButton}
          icon="filter-remove"
          accessible={true}
          accessibilityLabel="Clear all filters"
          accessibilityHint={getAccessibilityHint('clear all active filters and show all transactions')}
        >
          Clear Filters
        </Button>
      )}
    </View>
  );
};

export default TransactionEmptyState;
