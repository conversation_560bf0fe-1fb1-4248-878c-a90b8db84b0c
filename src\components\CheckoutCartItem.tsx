import React, { useCallback } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CartItem } from '../types/business';
import { SEMANTIC } from '../constants/colors';
import { checkoutScreenStyles } from '../styles/CheckoutScreen';
import { getCartItemFinancialValues } from '../orchestrators/cart';

interface CartItemComponentProps {
  item: CartItem;
  index: number;
  updateQuantity: (itemCode: string, quantity: number) => Promise<void>;
  changeUom: (itemCode: string) => void;
}
const CartItemComponent: React.FC<CartItemComponentProps> = React.memo(({
  item,
  index,
  updateQuantity,
  changeUom
}) => {
  const { item_code: itemCode, item_name: itemName, quantity, selectedUom: displayUom } = item;

  const { rate: numericRate, amount } = getCartItemFinancialValues(item);
  const rateText = numericRate ? Math.floor(numericRate).toLocaleString() : '0';
  const amountText = amount ? Math.floor(amount).toLocaleString() : '0';
  const handleIncrement = useCallback(() => {
    updateQuantity(itemCode, quantity + 1);
  }, [updateQuantity, itemCode, quantity]);

  const handleDecrement = useCallback(() => {
    if (quantity > 0) {
      updateQuantity(itemCode, quantity - 1);
    }
  }, [updateQuantity, itemCode, quantity]);

  const handleDelete = useCallback(() => {
    updateQuantity(itemCode, 0);
  }, [updateQuantity, itemCode]);

  const handleUomChange = useCallback(() => {
    changeUom(itemCode);
  }, [changeUom, itemCode]);

  return (
    <View style={checkoutScreenStyles.itemCard}>
      {/* Delete button */}
      <TouchableOpacity onPress={handleDelete} style={checkoutScreenStyles.deleteButton}>
        <MaterialCommunityIcons name="delete-outline" size={16} color={SEMANTIC.ERROR} />
      </TouchableOpacity>

      {/* Header with index and item name */}
      <View style={checkoutScreenStyles.itemHeader}>
        <View style={checkoutScreenStyles.indexContainer}>
          <Text style={checkoutScreenStyles.index}>{index}</Text>
        </View>
        <View style={checkoutScreenStyles.nameContainer}>
          <Text style={checkoutScreenStyles.itemName} numberOfLines={2}>
            {itemName}
          </Text>
        </View>
      </View>

      {/* Clean single row layout */}
      <View style={checkoutScreenStyles.itemRow}>
        {/* Left side: UOM and Quantity */}
        <View style={checkoutScreenStyles.leftControlsSection}>
          <TouchableOpacity onPress={handleUomChange} style={checkoutScreenStyles.uomButton}>
            <Text style={checkoutScreenStyles.uomText} numberOfLines={1}>{displayUom}</Text>
          </TouchableOpacity>

          <View style={[checkoutScreenStyles.quantityControls, checkoutScreenStyles.uomQuantitySpacing]}>
            <TouchableOpacity onPress={handleDecrement} style={checkoutScreenStyles.quantityButton}>
              <Text style={checkoutScreenStyles.quantityText}>-</Text>
            </TouchableOpacity>
            <Text style={checkoutScreenStyles.quantityValue}>{quantity}</Text>
            <TouchableOpacity onPress={handleIncrement} style={checkoutScreenStyles.quantityButton}>
              <Text style={checkoutScreenStyles.quantityText}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Right side: Rate and Amount with fixed alignment */}
        <View style={checkoutScreenStyles.rightInfoSection}>
          <View style={checkoutScreenStyles.rateColumn}>
            <Text style={checkoutScreenStyles.priceLabel}>Rate</Text>
            <Text style={[checkoutScreenStyles.priceValue, checkoutScreenStyles.rateValue]}>
              {rateText}
            </Text>
          </View>
          <View style={checkoutScreenStyles.amountColumn}>
            <Text style={checkoutScreenStyles.priceLabel}>Amount</Text>
            <Text style={[checkoutScreenStyles.priceValue, checkoutScreenStyles.amountValue]}>
              {amountText}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}, (prevProps, nextProps) => {
  // Only re-render if these specific properties change
  return (
    prevProps.item.item_code === nextProps.item.item_code &&
    prevProps.item.quantity === nextProps.item.quantity &&
    prevProps.item.selectedUom === nextProps.item.selectedUom &&
    prevProps.index === nextProps.index
  );
});

export default CartItemComponent;
