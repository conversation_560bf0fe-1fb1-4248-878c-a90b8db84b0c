import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const orderPriceDisplayStyles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.XSMALL, // Fixed padding instead of margin
    minWidth: 60, // Minimum width to prevent shifting
  },
  priceText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - uniform text size
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
  },
  discountedPrice: {
    color: BRAND.PRIMARY,
  },
  originalPrice: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textDecorationLine: 'line-through',
    marginTop: 2,
  },
});
