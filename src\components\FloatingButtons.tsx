import React, { useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { floatingButtonsStyles } from '../styles/FloatingButtons';

interface FloatingButtonsProps {
  onScrollToTop: () => void;
  scrollY: any;
  scrollThreshold?: number;
}

const FloatingButtons: React.FC<FloatingButtonsProps> = ({
  onScrollToTop,
  scrollY,
  scrollThreshold = 400,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const listener = scrollY.addListener(({ value }: { value: number }) => {
      setIsVisible(value > scrollThreshold);
    });

    return () => scrollY.removeListener(listener);
  }, [scrollY, scrollThreshold]);

  if (!isVisible) return null;

  return (
    <View style={floatingButtonsStyles.container}>
      <TouchableOpacity style={floatingButtonsStyles.button} onPress={onScrollToTop}>
        <MaterialCommunityIcons name="chevron-up" size={18} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

export default FloatingButtons;
