/**
 * User Data Orchestrator
 * 
 * Centralized user data caching and management
 * Extracted from app/(auth)/login.tsx and authService.ts
 */

import * as SecureStorage from '../utils/auth';
import * as SecureStore from 'expo-secure-store';
import { STORAGE_KEYS } from '../constants/storage';
import { CachedEmailResult } from '../types/auth';

/**
 * Load cached email from storage
 * Tries primary location first, then backup
 */
export const loadCachedEmail = async (): Promise<CachedEmailResult> => {
  try {
    // Try to get email from memory cache first (fastest)
    let cachedEmail = await SecureStorage.getSecureItem(STORAGE_KEYS.LAST_EMAIL);

    if (cachedEmail) {
      return {
        email: cachedEmail,
        source: 'primary'
      };
    }

    // If not in memory cache, try backup location
    try {
      cachedEmail = await SecureStore.getItemAsync('_backup_email');
      if (cachedEmail) {
        // Store in primary location for next time
        SecureStorage.setSecureItem(STORAGE_KEYS.LAST_EMAIL, cachedEmail)
          .catch(() => {/* Silently fail */});
        
        return {
          email: cachedEmail,
          source: 'backup'
        };
      }
    } catch (backupErr) {
      // Silently fail - non-critical
    }
  } catch (err) {
    // Silently fail - non-critical
  }

  return {
    email: null,
    source: 'none'
  };
};

/**
 * Cache user email for future logins
 */
export const cacheUserEmail = async (email: string): Promise<void> => {
  try {
    const trimmedEmail = email.trim();
    await SecureStorage.setSecureItem(STORAGE_KEYS.LAST_EMAIL, trimmedEmail);
  } catch (error) {
    // Silently fail - non-critical
    if (__DEV__) {
      console.warn('Error caching user email:', error);
    }
  }
};

/**
 * Cache user's first name
 */
export const cacheUserFirstName = async (firstName: string): Promise<void> => {
  try {
    if (firstName) {
      await SecureStorage.setSecureItem(STORAGE_KEYS.FIRST_NAME, firstName);
    }
  } catch (error) {
    // Silently fail - non-critical
    if (__DEV__) {
      console.warn('Error caching user first name:', error);
    }
  }
};

/**
 * Cache user data after successful login
 */
export const cacheUserData = async (email: string, firstName: string): Promise<void> => {
  try {
    // Cache both email and first name in parallel
    await Promise.all([
      cacheUserEmail(email),
      cacheUserFirstName(firstName)
    ]);
  } catch (error) {
    // Silently fail - non-critical
    if (__DEV__) {
      console.warn('Error caching user data:', error);
    }
  }
};
