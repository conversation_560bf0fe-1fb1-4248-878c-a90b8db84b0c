/**
 * Data Preloading Orchestrator
 * 
 * Handles background data preloading for improved app performance.
 */

import { InteractionManager } from 'react-native';
import { syncAllData, getDatabaseStats, closeDatabase } from '../utils/sqliteService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { backgroundOperationsManager, BackgroundOperationType } from './backgroundOperations';

// Constants
const LAST_SYNC_KEY = 'last_data_sync';
const SYNC_RETRY = {
  MAX_ATTEMPTS: 2,
  DELAY_BASE: 200, // ms - reduced from 500ms for faster retries
  DB_CLOSE_DELAY: 100 // ms - reduced from 300ms for faster cleanup
};

// Preloading state flags (prevent concurrent operations)
let isDataPreloading = false;

/**
 * Preload all data (items and item prices) in the background
 * @returns Promise resolving to true if preloading succeeded, false otherwise
 */
export const preloadItemPrices = async (): Promise<boolean> => {
  // Prevent concurrent preloading operations
  if (isDataPreloading) return true;
  isDataPreloading = true;

  backgroundOperationsManager.startOperation(BackgroundOperationType.PRELOAD);

  try {
    if (__DEV__) console.log('Starting background data preload...');

    // Wait for interactions to complete before starting heavy operations
    await new Promise(resolve => {
      InteractionManager.runAfterInteractions(() => resolve(undefined));
    });

    // Attempt sync with retry logic
    let success = false;
    for (let attempt = 1; attempt <= SYNC_RETRY.MAX_ATTEMPTS; attempt++) {
      try {
        if (__DEV__) console.log(`Preload attempt ${attempt}/${SYNC_RETRY.MAX_ATTEMPTS}`);

        await syncAllData();
        success = true;
        break;
      } catch (error) {
        if (__DEV__) {
          if (__DEV__) console.warn(`Preload attempt ${attempt} failed:`, error);
        }

        // If this is the last attempt and we're not using in-memory fallback yet,
        // the syncAllData function will handle the fallback automatically

        // Wait before retry (except on last attempt)
        if (attempt < SYNC_RETRY.MAX_ATTEMPTS) {
          await new Promise(resolve =>
            setTimeout(resolve, SYNC_RETRY.DELAY_BASE * attempt)
          );
        }
      }
    }

    if (success) {
      // Store successful sync timestamp
      try {
        await AsyncStorage.setItem(LAST_SYNC_KEY, new Date().toISOString());
      } catch (storageError) {
        if (__DEV__) console.warn('Failed to store sync timestamp:', storageError);
      }

      // Log database stats in development
      if (__DEV__) {
        try {
          const stats = await getDatabaseStats();
          console.log('Preload completed. Database stats:', stats);
        } catch (statsError) {
          console.log('Preload completed (stats unavailable)');
        }
      }

      backgroundOperationsManager.completeOperation();
    } else {
      if (__DEV__) {
        console.warn('Preload failed after all attempts, but app will continue with cached/empty data');
      }
      backgroundOperationsManager.failOperation();
    }

    return success;
  } catch (error) {
    backgroundOperationsManager.failOperation();
    return false;
  } finally {
    isDataPreloading = false;

    // Close database after a minimal delay to ensure all operations complete
    setTimeout(() => {
      closeDatabase().catch(() => {
        // Ignore errors when closing database
      });
    }, SYNC_RETRY.DB_CLOSE_DELAY);
  }
};

/**
 * Check if data preloading is currently in progress
 */
export const isPreloadingInProgress = (): boolean => {
  return isDataPreloading;
};

/**
 * Get last preload/sync timestamp
 */
export const getLastPreloadTime = async (): Promise<Date | null> => {
  try {
    const lastSyncStr = await AsyncStorage.getItem(LAST_SYNC_KEY);
    return lastSyncStr ? new Date(lastSyncStr) : null;
  } catch (error) {
    if (__DEV__) console.warn('Error getting last preload time:', error);
    return null;
  }
};

/**
 * Check if preload is needed based on last sync time
 */
export const isPreloadNeeded = async (maxAgeHours: number = 24): Promise<boolean> => {
  try {
    const lastPreload = await getLastPreloadTime();
    if (!lastPreload) return true;

    const now = new Date();
    const hoursSincePreload = (now.getTime() - lastPreload.getTime()) / (1000 * 60 * 60);
    
    return hoursSincePreload >= maxAgeHours;
  } catch (error) {
    if (__DEV__) console.warn('Error checking preload status:', error);
    return true; // Default to needing preload if we can't check
  }
};

/**
 * Preload data only if needed (based on age)
 */
export const preloadIfNeeded = async (maxAgeHours: number = 24): Promise<boolean> => {
  try {
    const needed = await isPreloadNeeded(maxAgeHours);
    if (needed) {
      if (__DEV__) console.log('Preload needed, starting...');
      return await preloadItemPrices();
    } else {
      if (__DEV__) console.log('Preload not needed, data is fresh');
      return true;
    }
  } catch (error) {
    if (__DEV__) console.warn('Error in preload check:', error);
    return false;
  }
};

/**
 * Force preload regardless of last sync time
 */
export const forcePreload = async (): Promise<boolean> => {
  if (__DEV__) console.log('Force preload requested');
  return await preloadItemPrices();
};

/**
 * Clear preload timestamp to force next preload
 */
export const clearPreloadData = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(LAST_SYNC_KEY);
    if (__DEV__) console.log('Preload data cleared');
  } catch (error) {
    if (__DEV__) console.warn('Error clearing preload data:', error);
  }
};

/**
 * Get preload status information
 */
export const getPreloadStatus = async () => {
  const lastPreloadTime = await getLastPreloadTime();
  const isNeeded = await isPreloadNeeded();
  
  return {
    isPreloading: isDataPreloading,
    lastPreloadTime,
    isNeeded,
    hasData: !!lastPreloadTime
  };
};

/**
 * Preload with progress callback
 */
export const preloadWithProgress = async (
  onProgress?: (stage: string) => void
): Promise<boolean> => {
  if (isDataPreloading) return true;
  
  try {
    onProgress?.('Starting preload...');
    
    // Wait for interactions
    onProgress?.('Waiting for UI interactions...');
    await new Promise(resolve => {
      InteractionManager.runAfterInteractions(() => resolve(undefined));
    });
    
    onProgress?.('Syncing data...');
    const result = await preloadItemPrices();
    
    onProgress?.(result ? 'Preload completed' : 'Preload failed');
    return result;
  } catch (error) {
    onProgress?.('Preload error');
    return false;
  }
};

/**
 * Schedule preload for later execution
 */
export const schedulePreload = (delayMs: number = 1000): Promise<boolean> => {
  return new Promise((resolve) => {
    setTimeout(async () => {
      const result = await preloadItemPrices();
      resolve(result);
    }, delayMs);
  });
};

/**
 * Preload in background without blocking UI
 */
export const backgroundPreload = (): void => {
  // Run after all interactions complete
  InteractionManager.runAfterInteractions(() => {
    // Minimal delay for optimal performance
    setTimeout(() => {
      preloadItemPrices().catch(() => {
        // Silently fail for background operations
      });
    }, 50); // Further reduced from 200ms to 50ms for faster startup
  });
};

/**
 * Cancel ongoing preload operation (if possible)
 */
export const cancelPreload = (): void => {
  // Note: This is a simple flag-based cancellation
  // The actual sync operation may continue but won't update the timestamp
  if (isDataPreloading) {
    isDataPreloading = false;
    if (__DEV__) console.log('Preload cancellation requested');
  }
};
