/**
 * API Constants
 * 
 * Contains all API-related constants including endpoints, timeouts, and configuration
 */

export const API = {
  BASE_URL: 'https://vitiuganda.frappe.cloud',// kikuubo.frappe.cloud
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  COMPANY: 'ViTi Distributors Ltd', //Pusha Online Limited (Distribution)
  TAXES_AND_CHARGES: 'Standard - VT', // Default taxes and charges template
  PRICE_LIST: 'Distribution Selling', // Default price list for sales orders
  ENDPOINTS: {
    LOGIN: '/api/method/login',
    LOGOUT: '/api/method/logout',
    LOGGED_USER: '/api/method/frappe.auth.get_logged_user',
    CUSTOMERS: '/api/method/v1.customer',
    ITEMS: '/api/method/v1.item',
    ITEM_PRICES: '/api/method/v1.item_price',
    // Resource-based endpoints for CRUD operations
    SALES_ORDERS: '/api/resource/Sales Order',
    SALES_INVOICES: '/api/resource/Sales Invoice',
    // Additional resource endpoints
    BIN: '/api/resource/Bin',
    // Version check endpoint
    VERSION_CHECK: '/api/method/v1.version',
  }
};

/**
 * Extract hostname from BASE_URL for display purposes
 */
export const getHostname = (): string => {
  try {
    const url = new URL(API.BASE_URL);
    const hostname = url.hostname;
    return hostname.split('.')[0]; // Return the first part before the dot
  } catch (error) {
    // Fallback to BASE_URL without protocol if URL parsing fails
    const hostname = API.BASE_URL.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
    return hostname.split('.')[0]; // Return the first part before the dot
  }
};
