import { fetchCustomers } from '../services/customerService';
import { Customer } from '../types/business';

export const loadCustomers = async (): Promise<Customer[]> => {
  return await fetchCustomers();
};

export const refreshCustomers = async (): Promise<Customer[]> => {
  return await fetchCustomers(true);
};

export const getCustomerByName = (customers: Customer[], name: string): Customer | undefined => {
  return customers.find(customer => customer.name === name);
};

export const getCustomerStats = (customers: Customer[]) => {
  const totalCustomers = customers.length;
  const customersWithOutstanding = customers.filter(c => (c.outstanding || 0) > 0).length;
  const totalOutstanding = customers.reduce((sum, c) => sum + (c.outstanding || 0), 0);

  return {
    totalCustomers,
    customersWithOutstanding,
    totalOutstanding
  };
};
