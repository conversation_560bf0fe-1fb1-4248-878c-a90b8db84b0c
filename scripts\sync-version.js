#!/usr/bin/env node

/**
 * Sync Version Script
 *
 * Reads version from package.json and updates all other files
 * Usage: npm run sync-version
 */

const fs = require('fs');
const path = require('path');

// Read version from package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const version = packageJson.version;

console.log(`🔄 Syncing version ${version} to app.json...`);

// Update app.json (constants reads from package.json automatically)
const appJsonPath = 'app.json';
const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
appJson.expo.version = version;
fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2) + '\n');
console.log(`✅ Updated ${appJsonPath}`);

console.log(`🎉 Version ${version} synced successfully!`);
console.log(`📝 Files updated:`);
console.log(`   ✅ package.json (source of truth)`);
console.log(`   ✅ app.json (synced)`);
console.log(`   ✅ constants/index.ts (reads from package.json)`);
console.log(`   ✅ eas.json (auto-increments build)`);
console.log(`📝 Next steps:`);
console.log(`   1. Commit your changes`);
console.log(`   2. Run: eas build --platform android`);
