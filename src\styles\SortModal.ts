import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const sortModalStyles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  backdropTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    backgroundColor: NEUTRAL.WHITE,
    borderTopLeftRadius: MD.BORDER_RADIUS.LARGE,
    borderTopRightRadius: MD.BORDER_RADIUS.LARGE,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.LARGE,
  },
  modalTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: 'bold',
    letterSpacing: MD.TYPOGRAPHY.H2.letterSpacing,
    color: NEUTRAL.TEXT_PRIMARY,
  },
  closeButton: {
    margin: 0,
  },
  twoColumnContainer: {
    flexDirection: 'row',
    height: 350,
  },
  tabColumn: {
    width: 120,
    borderRightWidth: 1,
    borderRightColor: MD.DIVIDER,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  tabItem: {
    paddingVertical: MD.SPACING.LARGE,
    paddingHorizontal: MD.SPACING.LARGE,
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeTabItem: {
    backgroundColor: NEUTRAL.WHITE,
    borderLeftColor: BRAND.PRIMARY,
  },
  tabItemText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_SECONDARY,
    flex: 1,
  },
  activeTabItemText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },
  tabItemWithSelection: {
    backgroundColor: `${BRAND.PRIMARY}08`,
  },
  tabItemTextWithSelection: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },
  optionsColumn: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
  },
  optionsScrollContainer: {
    flex: 1,
  },
  optionsContainer: {
    padding: 0,
    paddingBottom: MD.SPACING.SMALL,
  },
  optionCard: {
    borderRadius: 0,
    marginBottom: 0,
    overflow: 'hidden',
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  selectedOptionCard: {
    backgroundColor: `${BRAND.PRIMARY}08`,
  },
  optionTouchable: {
    width: '100%',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: MD.SPACING.LARGE,
  },
  optionIconContainer: {
    width: 24,
    height: 24,
    borderRadius: MD.BORDER_RADIUS.LARGE - 4,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: MD.SPACING.LARGE,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    marginBottom: MD.SPACING.XSMALL / 2,
  },
  selectedOptionText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: MD.SPACING.LARGE,
    marginBottom: MD.SPACING.LARGE,
    marginTop: MD.SPACING.SMALL,
  },
  applyButton: {
    flex: 1,
    marginLeft: MD.SPACING.SMALL,
    paddingVertical: MD.SPACING.SMALL + 2,
    borderRadius: MD.BORDER_RADIUS.LARGE + 4,
    backgroundColor: BRAND.PRIMARY,
    alignItems: 'center',
    elevation: 0,
  },
  applyButtonText: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    textTransform: 'uppercase',
  },
  clearButton: {
    flex: 1,
    marginRight: MD.SPACING.SMALL,
    paddingVertical: MD.SPACING.SMALL + 2,
    borderRadius: MD.BORDER_RADIUS.LARGE + 4,
    backgroundColor: 'transparent',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: BRAND.PRIMARY,
  },
  clearButtonText: {
    color: BRAND.PRIMARY,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    textTransform: 'uppercase',
  },
  disabledClearButton: {
    borderColor: 'rgba(0, 0, 0, 0.12)',
  },
  disabledClearButtonText: {
    color: 'rgba(0, 0, 0, 0.38)',
  },
  noOptionsContainer: {
    padding: MD.SPACING.LARGE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noOptionsText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
});
