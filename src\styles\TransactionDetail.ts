import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const transactionDetailStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.H2.letterSpacing,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    alignSelf: 'center',
    marginLeft: MD.SPACING.SMALL,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: MD.SPACING.MEDIUM,
    paddingBottom: MD.SPACING.LARGE,
  },
  section: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
    marginBottom: MD.SPACING.MEDIUM,
    padding: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.XSMALL,
  },
  cardTitle: {
    fontSize: 15,
    letterSpacing: 0.15,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  statusChip: {
    paddingHorizontal: MD.SPACING.SMALL,
    paddingVertical: MD.SPACING.SMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: MD.SPACING.XXSMALL,
  },
  statusText: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    fontWeight: '600',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XXSMALL,
  },
  detailLabel: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    flex: 1,
  },
  detailValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    color: NEUTRAL.TEXT_PRIMARY,
    fontWeight: '500',
    textAlign: 'right',
    flex: 1,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XSMALL,
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    marginTop: MD.SPACING.XSMALL,
  },
  totalLabel: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  totalValue: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    fontWeight: '700',
    color: BRAND.PRIMARY,
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  itemsTitle: {
    fontSize: 15,
    letterSpacing: 0.15,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  itemsCount: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    backgroundColor: NEUTRAL.BACKGROUND,
    paddingHorizontal: MD.SPACING.SMALL,
    paddingVertical: MD.SPACING.XXSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  noItemsText: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    padding: MD.SPACING.SMALL,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    paddingVertical: MD.SPACING.XXSMALL,
    paddingHorizontal: MD.SPACING.SMALL,
    minWidth: 80,
    height: 28,
  },
  actionButtonText: {
    color: BRAND.PRIMARY,
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    fontWeight: '500',
    marginLeft: MD.SPACING.XXSMALL,
  },
  dateText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
  },
  amountValue: {
    fontWeight: '700',
  },
  itemsContainer: {
    marginLeft: -MD.SPACING.MEDIUM,
    marginRight: -MD.SPACING.MEDIUM,
    width: 'auto',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: MD.SPACING.LARGE,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  errorText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: SEMANTIC.ERROR,
    textAlign: 'center',
    marginBottom: MD.SPACING.MEDIUM,
  },
  retryButton: {
    backgroundColor: BRAND.PRIMARY,
  },
});
