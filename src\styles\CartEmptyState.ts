import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const cartEmptyStateStyles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginTop: MD.SPACING.MEDIUM,
    marginBottom: MD.SPACING.SMALL,
  },
  emptySubtext: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.LARGE,
    lineHeight: 20,
  },
  emptyButton: {
    marginTop: MD.SPACING.MEDIUM,
  },
});
