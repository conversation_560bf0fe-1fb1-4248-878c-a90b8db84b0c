import React, { memo, useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { filterTabsStyles } from '../styles/FilterTabs';

export type FilterType = 'supplier' | 'brand' | 'itemGroup';

interface FilterTabsProps {
  selectedFilterType: FilterType;
  onFilterTypeChange: (filterType: FilterType) => void;
  supplierCount?: number;
  brandCount?: number;
  itemGroupCount?: number;
}

const FilterTabs: React.FC<FilterTabsProps> = ({
  selectedFilterType,
  onFilterTypeChange,
  supplierCount = 0,
  brandCount = 0,
  itemGroupCount = 0
}) => {
  const styles = filterTabsStyles;
  const [pressedTab, setPressedTab] = useState<FilterType | null>(null);

  const tabs = [
    {
      id: 'supplier' as FilterType,
      label: 'Supplier',
      count: supplierCount
    },
    {
      id: 'brand' as FilterType,
      label: 'Brand',
      count: brandCount
    },
    {
      id: 'itemGroup' as FilterType,
      label: 'Group',
      count: itemGroupCount
    }
  ];

  const handleTabPress = (tabId: FilterType) => {
    // AGGRESSIVE: Immediate visual feedback with zero delay
    setPressedTab(tabId);

    // Call the actual filter change immediately
    onFilterTypeChange(tabId);

    // Clear pressed state faster for more responsive feel
    requestAnimationFrame(() => {
      setTimeout(() => setPressedTab(null), 100);
    });
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabsContainer}>
        {tabs.map((tab) => {
          const isSelected = selectedFilterType === tab.id;
          const isPressed = pressedTab === tab.id;
          const isDisabled = false; // Removed transition-based disabling for instant feedback

          return (
            <TouchableOpacity
              key={tab.id}
              style={[
                styles.tab,
                isSelected && styles.selectedTab,
                isPressed && styles.pressedTab,
                isDisabled && styles.disabledTab
              ]}
              onPress={() => handleTabPress(tab.id)}
              disabled={isDisabled}
              accessible={true}
              accessibilityRole="tab"
              accessibilityState={{
                selected: isSelected,
                disabled: isDisabled
              }}
              accessibilityLabel={`${tab.label} filter, ${tab.count} available`}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.tabText,
                isSelected && styles.selectedTabText
              ]}>
                {tab.label}
              </Text>
              {tab.count > 0 && (
                <Text style={[
                  styles.countText,
                  isSelected && styles.selectedCountText
                ]}>
                  ({tab.count})
                </Text>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default memo(FilterTabs, (prevProps, nextProps) => {
  // Only re-render if relevant props have changed
  return (
    prevProps.selectedFilterType === nextProps.selectedFilterType &&
    prevProps.supplierCount === nextProps.supplierCount &&
    prevProps.brandCount === nextProps.brandCount &&
    prevProps.itemGroupCount === nextProps.itemGroupCount
  );
});
