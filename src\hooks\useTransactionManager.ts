import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { FlashList } from '@shopify/flash-list';
import { useFocusEffect } from 'expo-router';
import { SalesOrder, SalesInvoice, FetchTransactionsParams } from '../types/business';
import { useSearchFilter } from './useSearchFilter';
import useErrorHandler from './useErrorHandler';
import { TRANSACTION } from '../constants/business';

// Define a union type for transactions
type Transaction = SalesOrder | SalesInvoice;

interface UseTransactionManagerParams {
  transactionType: 'order' | 'invoice';
  fetchTransactions: (params: FetchTransactionsParams) => Promise<{ data: Transaction[]; total: number }>;
}

export const useTransactionManager = ({ transactionType, fetchTransactions }: UseTransactionManagerParams) => {
  // State management
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [detailsModalVisible, setDetailsModalVisible] = useState<boolean>(false);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string>('');
  const [filterModalVisible, setFilterModalVisible] = useState<boolean>(false);

  const { handleError } = useErrorHandler();
  const flashListRef = useRef<FlashList<Transaction>>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // Get available statuses based on transaction type
  const availableStatuses = useMemo(() => {
    const allStatuses = Object.keys(TRANSACTION.STATUS_COLORS);
    
    if (transactionType === 'order') {
      return allStatuses.filter(status => 
        ['Draft', 'On Hold', 'To Deliver and Bill', 'To Bill', 'To Deliver', 
         'Completed', 'Cancelled', 'Closed', 'Partly Delivered', 'Partly Billed'].includes(status)
      );
    } else {
      return allStatuses.filter(status => 
        ['Draft', 'Submitted', 'Paid', 'Unpaid', 'Overdue', 'Cancelled', 
         'Return', 'Credit Note Issued', 'Partly Paid', 'Unpaid and Discounted', 
         'Partly Paid and Discounted'].includes(status)
      );
    }
  }, [transactionType]);

  // Create filter groups for the filter modal
  const filterGroups = useMemo(() => [
    {
      id: 'status',
      title: 'Status',
      icon: 'tag-outline',
      options: availableStatuses.map(status => ({
        id: status,
        label: status,
      })),
    },
  ], [availableStatuses]);

  // State for error handling and refreshing
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Optimized fetch function with better error handling
  const fetchFunction = useCallback(async (params: any) => {
    try {
      setError(null);
      setRefreshing(params.page === 1 && !params.isLoadingMore);

      const result = await fetchTransactions({
        page: params.page,
        pageSize: params.pageSize,
        filters: params.filters,
      });

      return result;
    } catch (error) {
      const errorMessage = `Failed to fetch ${transactionType}s`;
      setError(errorMessage);
      handleError(error, {
        context: errorMessage,
      });
      throw error;
    } finally {
      setRefreshing(false);
    }
  }, [fetchTransactions, transactionType, handleError]);

  // Use the search filter hook for data management
  const {
    filteredData: transactions,
    loading,
    searchQuery,
    setSearchQuery,
    hasMore,
    loadMore: loadMoreFromHook,
    refresh,
    handleApplyFilters: applySearchFilters,
    performSearch,
  } = useSearchFilter<Transaction>({
    mode: 'api',
    fetchFunction,
    getDefaultFilters: useCallback(() => [], []),
    searchField: 'customer_name',
    searchOperator: 'like',
    pageSize: 20,
  });

  // Modal handlers
  const openDetailsModal = useCallback((transactionId: string) => {
    setSelectedTransactionId(transactionId);
    setDetailsModalVisible(true);
  }, []);

  const closeDetailsModal = useCallback(() => {
    setDetailsModalVisible(false);
    setSelectedTransactionId('');
  }, []);

  const openFilterModal = useCallback(() => {
    setFilterModalVisible(true);
  }, []);

  const closeFilterModal = useCallback(() => {
    setFilterModalVisible(false);
  }, []);

  // Filter handlers
  const handleApplyStatusFilters = useCallback((statuses: string[]) => {
    setSelectedStatuses(statuses);
    const statusFilters = statuses.map(status => `status:${status}`);
    applySearchFilters(statusFilters);
    closeFilterModal();
  }, [applySearchFilters, closeFilterModal]);

  const handleClearFilters = useCallback(() => {
    setSelectedStatuses([]);
    applySearchFilters([]);
    closeFilterModal();
  }, [applySearchFilters, closeFilterModal]);

  const handleQuickStatusFilter = useCallback((status: string) => {
    const newStatuses = [status];
    setSelectedStatuses(newStatuses);
    const statusFilters = newStatuses.map(s => `status:${s}`);
    applySearchFilters(statusFilters);
  }, [applySearchFilters]);

  const handleScrollToTop = useCallback(() => {
    flashListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  // Enhanced refresh function that clears errors
  const handleRefresh = useCallback(() => {
    setError(null);
    if (refresh) {
      refresh(1);
    }
  }, [refresh]);

  // Retry function for failed requests
  const handleRetry = useCallback(() => {
    setError(null);
    if (refresh) {
      refresh(1);
    }
  }, [refresh]);

  // Create a proper loadMore function that tracks pages
  const loadMore = useCallback(() => {
    if (!hasMore || loading || !loadMoreFromHook) {
      return;
    }

    const nextPage = currentPage + 1;
    setCurrentPage(nextPage);
    loadMoreFromHook(nextPage);
  }, [hasMore, loading, loadMoreFromHook, currentPage]);

  // Reset page when search or filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedStatuses]);

  // Initial data fetch on mount
  useEffect(() => {
    if (performSearch) {
      performSearch(1, false, false);
    }
  }, [performSearch, transactionType]);

  // Controlled focus effect - only refresh if no data exists
  useFocusEffect(
    useCallback(() => {
      if (transactions.length === 0 && !loading && performSearch) {
        performSearch(1, false, false);
      }
    }, [transactions.length, loading, performSearch, transactionType])
  );

  // Expose scrollToTop function globally for tab-to-scroll-to-top
  useEffect(() => {
    const functionName = `${transactionType}sScreenScrollToTop`;
    (global as any)[functionName] = handleScrollToTop;

    return () => {
      (global as any)[functionName] = null;
    };
  }, [handleScrollToTop, transactionType]);

  // Computed values
  const currentFilterValues = useMemo(() =>
    selectedStatuses.map(status => `status:${status}`),
    [selectedStatuses]
  );
  const hasActiveFilters = useMemo(() => selectedStatuses.length > 0, [selectedStatuses]);
  const filterCount = useMemo(() => selectedStatuses.length, [selectedStatuses]);

  return {
    // Data
    transactions,
    loading,
    refreshing,
    hasMore,
    total: transactions.length,
    error,

    // Search
    searchQuery,
    setSearchQuery,

    // Filters
    selectedStatuses,
    availableStatuses,
    filterGroups,
    currentFilterValues,
    hasActiveFilters,
    filterCount,
    handleApplyStatusFilters,
    handleClearFilters,
    handleQuickStatusFilter,

    // Modal state
    detailsModalVisible,
    selectedTransactionId,
    openDetailsModal,
    closeDetailsModal,
    filterModalVisible,
    openFilterModal,
    closeFilterModal,

    // Actions
    loadMore,
    refresh,
    handleRefresh,
    handleScrollToTop,
    handleRetry,

    // Refs
    flashListRef,
  };
};
