
export const formatCurrency = (amount: number | string | null | undefined): string => {
  if (amount === null || amount === undefined) {
    return 'UGX 0';
  }

  let numericAmount: number;
  if (typeof amount === 'string') {
    numericAmount = isNaN(parseFloat(amount)) ? 0 : parseFloat(amount);
  } else {
    numericAmount = amount;
  }
  return `UGX ${numericAmount.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    useGrouping: true,
  })}`;
};

export const formatNumber = (value: number): string => {
  if (isNaN(value) || value === undefined) return '0';
  if (value === 0) return '0';
  return formatCurrency(Math.floor(value)).replace('UGX ', '');
};

export const formatCartItemRate = (rate: number | string | undefined): string => {
  const numericRate = typeof rate === 'number' ? rate :
                     typeof rate === 'string' ? parseFloat(rate) : 0;
  return numericRate === 0 ? '-' : formatNumber(numericRate);
};

export const formatCartItemAmount = (rate: number | string | undefined, quantity: number): string => {
  const numericRate = typeof rate === 'number' ? rate :
                     typeof rate === 'string' ? parseFloat(rate) : 0;
  const amount = quantity > 0 ? numericRate * quantity : 0;
  return amount === 0 ? '-' : formatNumber(amount);
};
export const getCartItemAmountColor = (rate: number | string | undefined, quantity: number, colors: any): string => {
  const numericRate = typeof rate === 'number' ? rate :
                     typeof rate === 'string' ? parseFloat(rate) : 0;
  const amount = quantity > 0 ? numericRate * quantity : 0;
  return amount > 0 ? colors.SEMANTIC.SUCCESS : colors.NEUTRAL.TEXT_SECONDARY;
};

// ===== Date Formatting =====

/**
 * Format a date string to a readable format (DD-MM-YYYY)
 * @param dateString - ISO date string
 * @returns Formatted date string
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

/**
 * Format a date and time string to a readable format (DD-MM-YYYY HH:MM)
 * @param dateString - ISO date string
 * @returns Formatted date and time string
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${day}-${month}-${year} ${hours}:${minutes}`;
};

/**
 * Format time only (HH:MM)
 * @param dateString - ISO date string
 * @returns Formatted time string
 */
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
};

/**
 * Format relative time (e.g., "2 hours ago", "yesterday")
 * @param dateString - ISO date string
 * @returns Relative time string
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else {
    return formatDate(dateString);
  }
};

// ===== Text Formatting =====

/**
 * Truncate a string to a specified length and add ellipsis if needed
 * @param str String to truncate
 * @param maxLength Maximum length of the string
 * @returns Truncated string
 */
export const truncateString = (str: string, maxLength: number): string => {
  if (!str || str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
};

/**
 * Convert a string to title case (capitalize first letter of each word)
 * @param str String to convert
 * @returns Title case string
 */
export const toTitleCase = (str: string): string => {
  if (!str) return '';
  return str.replace(
    /\w\S*/g,
    txt => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
  );
};

/**
 * Convert string to sentence case (capitalize first letter only)
 * @param str String to convert
 * @returns Sentence case string
 */
export const toSentenceCase = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Format phone number for display
 * @param phoneNumber Raw phone number
 * @returns Formatted phone number
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Format based on length (assuming Uganda format)
  if (cleaned.length === 10 && cleaned.startsWith('0')) {
    // 0701234567 -> 0701 234 567
    return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
  } else if (cleaned.length === 12 && cleaned.startsWith('256')) {
    // 256701234567 -> +256 701 234 567
    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;
  }
  
  return phoneNumber; // Return original if format not recognized
};

// ===== Percentage and Ratio Formatting =====

/**
 * Format percentage with specified decimal places
 * @param value Decimal value (0.15 for 15%)
 * @param decimals Number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (value: number, decimals: number = 1): string => {
  if (isNaN(value)) return '0%';
  return `${(value * 100).toFixed(decimals)}%`;
};

/**
 * Format file size in human readable format
 * @param bytes File size in bytes
 * @returns Formatted file size string
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
};
