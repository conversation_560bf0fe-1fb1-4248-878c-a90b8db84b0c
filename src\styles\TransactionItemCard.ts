import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const transactionItemCardStyles = StyleSheet.create({
  // Simple container without elevation or border radius for slick appearance
  container: {
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    backgroundColor: NEUTRAL.WHITE,
    width: '100%',
    alignSelf: 'stretch',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    marginBottom: MD.SPACING.XSMALL,
  },
  indexContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: `${BRAND.PRIMARY}20`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: MD.SPACING.SMALL,
  },
  index: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    fontWeight: '600',
    color: BRAND.PRIMARY,
  },
  nameContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  detailsRow: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingLeft: 24 + MD.SPACING.SMALL, // circle width + margin
  },
  detailItem: {
    flex: 1,
  },
  detailLabel: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: MD.SPACING.XSMALL,
  },
  detailValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  // Simple divider without margins for clean separation
  divider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginTop: MD.SPACING.SMALL,
    marginHorizontal: 0,
    width: '100%',
  },
});
