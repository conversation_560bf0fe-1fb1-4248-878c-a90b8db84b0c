import { useCallback } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { ErrorType } from '../utils/errors';
import { ROUTES } from '../constants/routes';
import { logout } from '../services/authService';

interface ErrorOptions {
  context?: string;
  silent?: boolean;
  onRetry?: () => void;
}

const useErrorHandler = () => {
  const getErrorMessage = useCallback((error: any): string => {
    if (error?.type === ErrorType.AUTHENTICATION) {
      return 'Invalid username or password. Please try again.';
    }
    if (error?.type === ErrorType.SESSION_EXPIRED) {
      return 'Your session has expired. Please sign in again.';
    }
    if (error?.type === ErrorType.NETWORK || error?.type === ErrorType.TIMEOUT) {
      return 'Unable to connect. Please check your internet connection.';
    }
    if (error instanceof Error) {
      return error.message;
    }
    if (typeof error === 'string') return error;
    return 'Something went wrong. Please try again.';
  }, []);

  const handleSessionExpired = useCallback(() => {
    Alert.alert('Session Expired', 'Your session has expired. Please sign in again.', [{ text: 'OK' }]);
    setTimeout(() => {
      logout(true).catch(() => {});
      router.replace(ROUTES.LOGIN);
    }, 100);
  }, []);

  const handleError = useCallback((error: any, options?: ErrorOptions) => {
    const { silent, onRetry } = options || {};
    if (silent) return;
    if (error?.type === ErrorType.SESSION_EXPIRED) return handleSessionExpired();

    const message = getErrorMessage(error);
    const buttons = onRetry ? [{ text: 'Retry', onPress: onRetry }, { text: 'OK' }] : [{ text: 'OK' }];
    Alert.alert('Error', message, buttons);
  }, [getErrorMessage, handleSessionExpired]);

  return { handleError, handleSessionExpired, getErrorMessage };
};

export default useErrorHandler;
