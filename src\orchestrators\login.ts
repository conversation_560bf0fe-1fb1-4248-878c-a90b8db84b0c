/**
 * Login Orchestrator
 *
 * Central orchestrator for login process
 * Extracted from app/(auth)/login.tsx
 */

import { login } from '../services/authService';
import { ROUTES } from '../constants/routes';
import { LoginFormData, LoginResult } from '../types/auth';
import { validateLoginForm, isLoginResponseValid } from '../utils/auth';
import { getUserFriendlyErrorMessage } from '../utils/errors';

/**
 * Perform login with validation and user data caching
 */
export const performLogin = async (formData: LoginFormData): Promise<LoginResult> => {
  // Validate inputs first
  const validation = validateLoginForm(formData);
  if (!validation.isValid) {
    return {
      success: false,
      shouldNavigate: false,
      error: Object.values(validation.errors)[0] || 'Invalid input'
    };
  }

  try {
    const trimmedEmail = formData.email.trim();

    // Perform authentication - this already handles session storage internally
    const response = await login(trimmedEmail, formData.password);

    if (isLoginResponseValid(response)) {
      return {
        success: true,
        shouldNavigate: true,
        redirectTo: ROUTES.HOME_FULL
      };
    } else {
      return {
        success: false,
        shouldNavigate: false,
        error: 'Something went wrong. Please try again.'
      };
    }
  } catch (error) {
    return {
      success: false,
      shouldNavigate: false,
      error: getUserFriendlyErrorMessage(error)
    };
  }
};
