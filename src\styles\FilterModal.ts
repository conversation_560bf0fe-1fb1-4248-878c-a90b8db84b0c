import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const filterModalStyles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  backdropTouchable: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContent: {
    backgroundColor: NEUTRAL.WHITE,
    borderTopLeftRadius: MD.BORDER_RADIUS.LARGE,
    borderTopRightRadius: MD.BORDER_RADIUS.LARGE,
    maxHeight: '70%',
    overflow: 'hidden',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.LARGE,
    backgroundColor: NEUTRAL.WHITE,
    borderTopLeftRadius: MD.BORDER_RADIUS.LARGE,
    borderTopRightRadius: MD.BORDER_RADIUS.LARGE,
  },
  modalTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: 'bold',
    letterSpacing: MD.TYPOGRAPHY.H2.letterSpacing,
    color: NEUTRAL.TEXT_PRIMARY,
  },
  closeButton: {
    margin: 0,
  },

  twoColumnContainer: {
    flexDirection: 'row',
    height: 350,
  },
  tabColumn: {
    width: 120,
    borderRightWidth: 1,
    borderRightColor: MD.DIVIDER,
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
  },
  tabItem: {
    paddingVertical: MD.SPACING.LARGE,
    paddingHorizontal: MD.SPACING.LARGE,
    borderLeftWidth: 3,
    borderLeftColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  activeTabItem: {
    backgroundColor: NEUTRAL.WHITE,
    borderLeftColor: BRAND.PRIMARY,
  },
  tabItemText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_SECONDARY,
    flex: 1,
  },
  activeTabItemText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },
  tabItemWithSelection: {
    backgroundColor: `${BRAND.PRIMARY}08`,
  },
  tabItemTextWithSelection: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },

  // Right column - Options
  optionsColumn: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
  },

  // Options container - Material Design
  optionsScrollContainer: {
    flex: 1, // Take full height of parent
  },
  optionsContainer: {
    padding: 0, // No padding for a full-width list
    paddingBottom: MD.SPACING.SMALL,
  },

  // Option card styles - Material Design
  optionCard: {
    borderRadius: 0, // No rounded corners for a cleaner list look
    marginBottom: 0, // No margin between items
    overflow: 'hidden',
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER, // Light divider for Material Design
  },
  selectedOptionCard: {
    backgroundColor: `${BRAND.PRIMARY}08`, // Very subtle background for selected items
  },
  optionTouchable: {
    width: '100%',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: MD.SPACING.LARGE, // More padding for better touch targets
  },
  optionIconContainer: {
    width: 24,
    height: 24,
    borderRadius: MD.BORDER_RADIUS.LARGE - 4,
    backgroundColor: 'transparent', // No background for cleaner look
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: MD.SPACING.LARGE, // More spacing between icon and text
  },
  optionTextContainer: {
    flex: 1,
  },
  optionText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    marginBottom: MD.SPACING.XSMALL / 2,
  },
  selectedOptionText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing, // Slightly increased letter spacing for better readability
  },

  // Buttons container - Material Design
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: MD.SPACING.LARGE,
    marginBottom: MD.SPACING.LARGE,
    marginTop: MD.SPACING.SMALL,
  },

  // Apply button - Material Design
  applyButton: {
    flex: 1,
    marginLeft: MD.SPACING.SMALL, // Now on the right side
    paddingVertical: MD.SPACING.SMALL + 2,
    borderRadius: MD.BORDER_RADIUS.LARGE + 4, // More rounded corners
    backgroundColor: BRAND.PRIMARY,
    alignItems: 'center',
    elevation: 0, // No elevation for flatter look
  },
  applyButtonText: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    textTransform: 'uppercase', // Material Design button text
  },

  // Clear button - Material Design
  clearButton: {
    flex: 1,
    marginRight: MD.SPACING.SMALL, // Now on the left side
    paddingVertical: MD.SPACING.SMALL + 2,
    borderRadius: MD.BORDER_RADIUS.LARGE + 4, // More rounded corners
    backgroundColor: 'transparent', // No background for cleaner look
    alignItems: 'center',
    borderWidth: 1,
    borderColor: BRAND.PRIMARY, // Use primary color instead of error for better consistency
  },
  clearButtonText: {
    color: BRAND.PRIMARY, // Use primary color instead of error for better consistency
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    textTransform: 'uppercase', // Material Design button text
  },
  disabledClearButton: {
    borderColor: 'rgba(0, 0, 0, 0.12)',
  },
  disabledClearButtonText: {
    color: 'rgba(0, 0, 0, 0.38)',
  },

  // Empty state
  noOptionsContainer: {
    padding: MD.SPACING.LARGE,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noOptionsText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
});
