import { useEffect, useState, useCallback } from 'react';
import { loadCachedEmail } from '../orchestrators/userData';

export const useCachedEmail = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  const loadEmail = useCallback(async () => {
    try {
      const result = await loadCachedEmail();
      if (result.email) {
        setEmail(result.email);
      }
    } catch (error) {
      // Silently fail - non-critical
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Use immediate execution for faster loading
    loadEmail();
  }, [loadEmail]);

  return {
    email,
    setEmail,
    isLoading
  };
};
