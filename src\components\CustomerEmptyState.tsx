import React from 'react';
import { View, Text } from 'react-native';
import { Avatar, Button } from 'react-native-paper';
import { customerEmptyStateStyles } from '../styles/CustomerEmptyState';

interface CustomerEmptyStateProps {
  searchQuery?: string;
  selectedFilters?: string[];
  selectedSort?: string;
  onClearFilters?: () => void;
  loading?: boolean;
}

const CustomerEmptyState: React.FC<CustomerEmptyStateProps> = ({
  searchQuery,
  selectedFilters = [],
  selectedSort,
  onClearFilters,
  loading = false,
}) => {
  if (loading) {
    return null;
  }
  // Determine the appropriate message based on filters
  const getEmptyMessage = () => {
    const hasSearch = searchQuery?.trim();
    const hasFilters = selectedFilters.length > 0;
    const hasSort = selectedSort && selectedSort !== '';
    const hasAnyFilter = hasSearch || hasFilters || hasSort;
    
    if (hasAnyFilter) {
      return {
        title: 'No customers found',
        subtitle: 'Try adjusting your search or filters to find customers',
        showClearButton: true,
      };
    }
    
    return {
      title: 'No customers yet',
      subtitle: 'Customers will appear here once they are added to the system',
      showClearButton: false,
    };
  };

  const { title, subtitle, showClearButton } = getEmptyMessage();

  return (
    <View style={customerEmptyStateStyles.emptyContainer}>
      <Avatar.Icon
        size={64}
        icon="account-group"
        style={customerEmptyStateStyles.emptyIcon}
      />
      <Text style={customerEmptyStateStyles.emptyText}>{title}</Text>
      <Text style={customerEmptyStateStyles.emptySubText}>{subtitle}</Text>
      
      {showClearButton && onClearFilters && (
        <Button
          mode="outlined"
          onPress={onClearFilters}
          style={customerEmptyStateStyles.clearButton}
          icon="filter-remove"
        >
          Clear Filters
        </Button>
      )}
    </View>
  );
};

export default React.memo(CustomerEmptyState);
