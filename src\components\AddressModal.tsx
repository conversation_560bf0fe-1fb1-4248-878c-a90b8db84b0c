import React from 'react';
import { View, Modal, TouchableOpacity, TouchableWithoutFeedback, FlatList } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Address } from '../types/business';
import { addressModalStyles } from '../styles/AddressModal';

interface AddressModalProps {
  visible: boolean;
  onClose: () => void;
  addresses: Address[];
  customerName: string;
}

interface AddressListItemProps {
  address: Address;
}

const AddressListItem: React.FC<AddressListItemProps> = ({ address }) => {
  return (
    <View style={addressModalStyles.addressItem}>
      <View style={addressModalStyles.addressContent}>
        <View style={addressModalStyles.addressDetails}>
          <Text style={addressModalStyles.addressLine} numberOfLines={2}>
            {address.address_line1}
          </Text>
          <Text style={addressModalStyles.addressCity} numberOfLines={1}>
            {address.city}
          </Text>
        </View>
      </View>
    </View>
  );
};

const AddressModal: React.FC<AddressModalProps> = ({
  visible,
  onClose,
  addresses,
  customerName
}) => {
  const theme = useTheme();

  const renderAddressItem = ({ item }: { item: Address }) => (
    <AddressListItem address={item} />
  );

  const keyExtractor = (item: Address) => item.name;

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={addressModalStyles.modalOverlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={addressModalStyles.modalContainer}>

              <View style={addressModalStyles.modalHeader}>
                <View style={addressModalStyles.headerLeft}>
                  <MaterialCommunityIcons 
                    name="map-marker-multiple" 
                    size={24} 
                    color={theme.colors.primary} 
                  />
                  <View style={addressModalStyles.headerTextContainer}>
                    <Text style={addressModalStyles.modalTitle}>Addresses</Text>
                    <Text style={addressModalStyles.customerName} numberOfLines={1}>
                      {customerName}
                    </Text>
                  </View>
                </View>
                <TouchableOpacity onPress={onClose} style={addressModalStyles.closeButton}>
                  <MaterialCommunityIcons 
                    name="close" 
                    size={24} 
                    color="#666" 
                  />
                </TouchableOpacity>
              </View>


              <View style={addressModalStyles.listContainer}>
                {addresses && addresses.length > 0 ? (
                  <FlatList
                    data={addresses}
                    renderItem={renderAddressItem}
                    keyExtractor={keyExtractor}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={addressModalStyles.listContent}
                  />
                ) : (
                  <View style={addressModalStyles.emptyContainer}>
                    <MaterialCommunityIcons 
                      name="map-marker-off" 
                      size={48} 
                      color="#ccc" 
                    />
                    <Text style={addressModalStyles.emptyText}>No addresses found</Text>
                  </View>
                )}
              </View>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default AddressModal;
