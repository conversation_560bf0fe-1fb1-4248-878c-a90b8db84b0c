import React, { memo, useCallback, useMemo } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useTheme } from 'react-native-paper';
import SearchComponent from './SearchComponent';
import { NEUTRAL } from '../constants/colors';
import { pageHeaderStyles as styles } from '../styles/PageHeader';

export interface PageHeaderProps {
  title: string;
  features: {
    search?: {
      isActive?: boolean;
      value?: string;
      onChangeText?: (text: string) => void;
      onClear?: () => void;
      placeholder?: string;
      onSubmitEditing?: () => void;
      autoFocus?: boolean;
      ref?: React.RefObject<any>;
    };
    filter?: {
      onPress: () => void;
      isActive?: boolean;
    };
    sort?: {
      onPress: () => void;
      isActive?: boolean;
    };
    backButton?: {
      onPress: () => void;
    };
    rightElements?: React.ReactNode;
  };
}


const PageHeader = memo(({ title, features }: PageHeaderProps) => {
  const theme = useTheme();

  const handleFilterPress = useCallback(() => {
    if (features.filter?.onPress) {
      features.filter.onPress();
    }
  }, [features.filter]);

  const handleSortPress = useCallback(() => {
    if (features.sort?.onPress) {
      features.sort.onPress();
    }
  }, [features.sort]);

  // Memoize icon colors to prevent unnecessary re-calculations
  const sortIconColor = useMemo(() =>
    features.sort?.isActive ? theme.colors.primary : NEUTRAL.TEXT_SECONDARY,
    [features.sort?.isActive, theme.colors.primary]
  );

  const filterIconColor = useMemo(() =>
    features.filter?.isActive ? theme.colors.primary : NEUTRAL.TEXT_SECONDARY,
    [features.filter?.isActive, theme.colors.primary]
  );

  return (
    <View style={styles.header}>
      <View style={styles.titleRow}>
        {features.backButton && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={features.backButton.onPress}
            activeOpacity={0.7}
            accessibilityLabel="Back"
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <MaterialCommunityIcons
              name="arrow-left"
              size={22}
              color={NEUTRAL.TEXT_PRIMARY}
            />
          </TouchableOpacity>
        )}

        <Text
          style={[
            styles.headerTitle,
            features.backButton ? styles.headerTitleWithBack : null
          ]}
          numberOfLines={1}
          ellipsizeMode="tail"
        >
          {title}
        </Text>

        <View style={styles.rightElementsContainer}>
          {features.sort && (
            <TouchableOpacity
              style={styles.iconButton}
              onPress={handleSortPress}
              activeOpacity={0.7}
              accessibilityLabel="Sort"
            >
              <MaterialCommunityIcons
                name="sort"
                size={20}
                color={sortIconColor}
              />
            </TouchableOpacity>
          )}

          {features.filter && (
            <TouchableOpacity
              style={styles.iconButton}
              onPress={handleFilterPress}
              activeOpacity={0.7}
              accessibilityLabel="Filter"
            >
              <MaterialCommunityIcons
                name="filter-variant"
                size={20}
                color={filterIconColor}
              />
            </TouchableOpacity>
          )}

          {features.rightElements}
        </View>
      </View>

      <View style={styles.searchRow}>
        {features.search && (
          <View style={styles.searchContainer}>
            <SearchComponent
              ref={features.search.ref}
              value={features.search.value || ''}
              onChangeText={features.search.onChangeText || (() => {})}
              onClear={features.search.onClear}
              placeholder={features.search.placeholder || 'Search'}
              autoFocus={features.search.autoFocus ?? false}
              onSubmitEditing={features.search.onSubmitEditing}
              fullWidth={true}
            />
          </View>
        )}
      </View>
    </View>
  );
});

export default PageHeader;
