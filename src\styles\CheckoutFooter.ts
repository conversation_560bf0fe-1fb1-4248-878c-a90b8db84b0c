import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const checkoutFooterStyles = StyleSheet.create({
  footer: {
    backgroundColor: NEUTRAL.WHITE,
    padding: MD.SPACING.LARGE, // Match create-order footer exactly
    paddingBottom: 16, // Match create-order footer exactly
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    elevation: 4, // Match create-order footer elevation
  },
  summaryContainer: {
    marginBottom: MD.SPACING.MEDIUM,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  summaryLabel: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
  },
  summaryValue: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: MD.SPACING.SMALL,
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
  },
  totalLabel: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  totalValue: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
  },
  placeOrderButton: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    height: 48, // Match create-order button height exactly
    width: '100%',
    flexDirection: 'row', // Single row layout like create-order
    justifyContent: 'space-between', // Text left, amount right like create-order
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE, // Match create-order button padding
    elevation: 2, // Match create-order button elevation
  },
  disabledButton: {
    backgroundColor: NEUTRAL.TEXT_DISABLED,
    elevation: 0,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  placeOrderButtonText: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.SUBTITLE2.fontSize, // Match create-order button text exactly
    fontWeight: '600', // Match create-order button text exactly
    letterSpacing: MD.TYPOGRAPHY.SUBTITLE2.letterSpacing, // Match create-order button text exactly
  },
  placeOrderButtonAmount: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.H2.fontSize, // Match create-order button amount exactly
    fontWeight: '600', // Match create-order button amount exactly
  },
});
