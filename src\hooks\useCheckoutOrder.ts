import { useState, useCallback } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { createSalesOrder } from '../services/transactionService';
import { SalesOrderPayload, CartItem } from '../types/business';
import { UseCheckoutOrderReturn } from '../types/business';
import { ROUTES } from '../constants/routes';
import { CHECKOUT_MESSAGES } from '../constants/messages';
import { API } from '../constants/api';

import { getUserFriendlyErrorMessage } from '../utils/errors';

export const useCheckoutOrder = (
  cartItems: CartItem[],
  customerId: string,
  customerName: string,
  cartCacheKey: string,
  customerPriceList?: string,
  onCartCleared?: () => void
): UseCheckoutOrderReturn => {
  const [loading, setLoading] = useState(false);
  const [orderPlaced, setOrderPlaced] = useState(false);
  const [placedOrderName, setPlacedOrderName] = useState<string>('');

  // Navigate after successful order creation
  const navigateAfterOrder = useCallback((orderName: string) => {
    Alert.alert(
      CHECKOUT_MESSAGES.SUCCESS.TITLE,
      CHECKOUT_MESSAGES.SUCCESS.MESSAGE(orderName, customerName),
      [
        {
          text: CHECKOUT_MESSAGES.SUCCESS.VIEW_ORDERS,
          onPress: () => {
            // Reset success state before navigation
            setOrderPlaced(false);
            setPlacedOrderName('');
            router.push(ROUTES.ORDERS);
          }
        },
        {
          text: CHECKOUT_MESSAGES.SUCCESS.NEW_ORDER,
          onPress: () => {
            // Reset success state before navigation
            setOrderPlaced(false);
            setPlacedOrderName('');
            router.push(ROUTES.CUSTOMERS);
          },
          style: 'cancel'
        }
      ]
    );
  }, [customerName]);

  // Clear cart from storage after successful order
  const clearCartFromStorage = useCallback(async () => {
    try {
      await AsyncStorage.removeItem(cartCacheKey);
    } catch (clearError) {
      if (__DEV__) console.error('Error clearing cart after order placement:', clearError);
    }
  }, [cartCacheKey]);

  // Create the order
  const createOrder = useCallback(async () => {
    setLoading(true);

    try {
      // Ensure we only have items with quantity > 0
      const validCartItems = cartItems.filter(item => item.quantity > 0);

      // Format cart items for API payload
      const formattedItems = validCartItems.map(item => ({
        item_code: item.item_code,
        qty: item.quantity,
        uom: item.selectedUom
      }));

      // Create order payload
      const orderPayload: SalesOrderPayload = {
        customer: customerId,
        company: API.COMPANY,
        items: formattedItems,
        transaction_date: new Date().toISOString().split('T')[0],
        delivery_date: new Date().toISOString().split('T')[0],
        taxes_and_charges: API.TAXES_AND_CHARGES,
        selling_price_list: customerPriceList || API.PRICE_LIST,
      };

      // Call API to create the order
      const createdOrder = await createSalesOrder(orderPayload);

      // Clear the cart from storage immediately after successful order creation
      await clearCartFromStorage();

      // Clear the local cart state if callback is provided
      if (onCartCleared) {
        onCartCleared();
      }

      // Set success state before showing alert
      setOrderPlaced(true);
      setPlacedOrderName(createdOrder.name);
      setLoading(false);

      // Navigate with success message
      navigateAfterOrder(createdOrder.name);
    } catch (error: any) {
      setLoading(false);

      // Show error alert WITHOUT retry option - preserve cart data
      Alert.alert(
        CHECKOUT_MESSAGES.ERROR.TITLE,
        getUserFriendlyErrorMessage(error),
        [
          {
            text: CHECKOUT_MESSAGES.ERROR.OK
          }
        ]
      );

      // Cart data is preserved - no clearing on error
    }
  }, [cartItems, customerId, customerPriceList, clearCartFromStorage, navigateAfterOrder]);

  // Handle place order confirmation
  const confirmPlaceOrder = useCallback(() => {
    Alert.alert(
      CHECKOUT_MESSAGES.CONFIRM_ORDER.TITLE,
      CHECKOUT_MESSAGES.CONFIRM_ORDER.MESSAGE(customerName),
      [
        {
          text: CHECKOUT_MESSAGES.CONFIRM_ORDER.CANCEL,
          style: 'cancel'
        },
        {
          text: CHECKOUT_MESSAGES.CONFIRM_ORDER.CONFIRM,
          onPress: createOrder
        }
      ]
    );
  }, [customerName, createOrder]);

  return {
    createOrder,
    loading,
    confirmPlaceOrder,
    orderPlaced,
    placedOrderName,
  };
};
