import { apiClient } from './apiClient';
import { API } from '../constants/api';
import { withErrorHandling } from '../utils/errors';
import { BinData } from '../types/business';
import { BinResponse } from '../types/api';

/**
 * Fetch bin data from the API for items with actual quantity > 0
 *
 * This function retrieves bin data with item codes and their actual quantities
 * from the ERP system. It filters for bins where actual_qty > 0.
 *
 * @returns Promise resolving to an array of BinData objects
 * @throws Error if the API call fails
 */
export const fetchBinData = async (): Promise<BinData[]> => {
  return withErrorHandling(async () => {
    // Define the fields we want to retrieve
    const fields = ["item_code", "actual_qty"];

    // Define the filter to only get bins with actual_qty > 0
    const filters = [["actual_qty", ">", 0]];

    // Build the URL with query parameters
    // Add limit_page_length=999999999 to get all results in one go
    const url = `${API.ENDPOINTS.BIN}?fields=${JSON.stringify(fields)}&filters=${JSON.stringify(filters)}&limit_page_length=999999999`;

    const response = await apiClient.get<BinResponse>(url);
    return response?.data?.data || [];
  });
};

/**
 * Get a map of item codes to their actual quantities
 *
 * This function fetches bin data and transforms it into a map for easy lookup
 * of actual quantities by item code. If multiple bins exist for the same item,
 * the quantities are summed.
 *
 * @returns Promise resolving to a Map with item_code as key and total actual_qty as value
 * @throws Error if the API call fails
 */
export const getActualQuantityMap = async (): Promise<Map<string, number>> => {
  return withErrorHandling(async () => {
    const binData = await fetchBinData();
    const quantityMap = new Map<string, number>();

    // Sum quantities for each item_code
    binData.forEach(bin => {
      const currentQty = quantityMap.get(bin.item_code) || 0;
      quantityMap.set(bin.item_code, currentQty + bin.actual_qty);
    });

    return quantityMap;
  });
};
