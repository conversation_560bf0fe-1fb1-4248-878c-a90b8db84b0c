import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const orderUomButtonStyles = StyleSheet.create({
  uomButton: {
    backgroundColor: `${BRAND.PRIMARY}10`,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    alignItems: 'center',
    justifyContent: 'center',
    width: 70,
  },
  uomButtonText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance
    fontWeight: '600', // Increased from 500 to 600 for better prominence
    color: BRAND.PRIMARY,
  },
});
