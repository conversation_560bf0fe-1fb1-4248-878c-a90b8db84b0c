import React, { useCallback, useEffect, useRef } from 'react';
import { View, Animated, Easing } from 'react-native';
import { customerSkeletonStyles } from '../styles/CustomerSkeleton';

interface CustomerSkeletonProps {
  count?: number;
  shimmerEnabled?: boolean;
}

const ShimmerView: React.FC<{ style: any; enabled: boolean }> = React.memo(({ style, enabled }) => {
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (!enabled) return;

    const animation = Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
        easing: Easing.bezier(0.4, 0.0, 0.2, 1),
      })
    );

    animation.start();
    return () => animation.stop();
  }, [animatedValue, enabled]);

  if (!enabled) {
    return <View style={style} />;
  }

  const opacity = animatedValue.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.4, 0.8, 0.4],
  });

  return (
    <Animated.View
      style={[
        style,
        {
          opacity,
          backgroundColor: '#f0f0f0'
        }
      ]}
    />
  );
});

export const CustomerSkeleton: React.FC<CustomerSkeletonProps> = React.memo(({ count = 12, shimmerEnabled = true }) => {
  const renderSkeletonItem = useCallback((index: number) => (
    <View key={index} style={customerSkeletonStyles.skeletonListItem}>
      <View style={customerSkeletonStyles.skeletonCardHeader}>
        <View style={customerSkeletonStyles.skeletonNameContainer}>
          <ShimmerView style={customerSkeletonStyles.skeletonName} enabled={shimmerEnabled} />
          <View style={customerSkeletonStyles.skeletonDetailsRow}>
            <ShimmerView style={customerSkeletonStyles.skeletonTerritory} enabled={shimmerEnabled} />
            <ShimmerView style={customerSkeletonStyles.skeletonId} enabled={shimmerEnabled} />
          </View>
          {/* Add address skeleton to match the real customer card */}
          <View style={customerSkeletonStyles.skeletonAddressContainer}>
            <ShimmerView style={customerSkeletonStyles.skeletonAddressLine1} enabled={shimmerEnabled} />
            <ShimmerView style={customerSkeletonStyles.skeletonAddressLine2} enabled={shimmerEnabled} />
          </View>
        </View>
        <View style={customerSkeletonStyles.skeletonFinancialContainer}>
          <View style={customerSkeletonStyles.skeletonFinancialRow}>
            <ShimmerView style={customerSkeletonStyles.skeletonIconPlaceholder} enabled={shimmerEnabled} />
            <ShimmerView style={customerSkeletonStyles.skeletonValue} enabled={shimmerEnabled} />
          </View>
          <View style={customerSkeletonStyles.skeletonFinancialRow}>
            <ShimmerView style={customerSkeletonStyles.skeletonIconPlaceholder} enabled={shimmerEnabled} />
            <ShimmerView style={customerSkeletonStyles.skeletonValue} enabled={shimmerEnabled} />
          </View>
        </View>
      </View>
    </View>
  ), []);

  return (
    <View>
      {Array.from({ length: count }, (_, index) => renderSkeletonItem(index))}
    </View>
  );
});
