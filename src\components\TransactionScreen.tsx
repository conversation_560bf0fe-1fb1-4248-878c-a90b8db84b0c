import React, { useMemo, useCallback, memo } from 'react';
import { View, Text } from 'react-native';
import { Button } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { SalesOrder, SalesInvoice, FetchTransactionsParams } from '../types/business';
import TransactionDetailScreen from './TransactionDetailScreen';
import FilterModal from './FilterModal';
import FilterBar from './FilterBar';
import PageHeader from './PageHeader';
import TransactionList from './TransactionList';
import { useTransactionManager } from '../hooks/useTransactionManager';
import { transactionScreenStyles } from '../styles/TransactionScreen';
import { SEARCH } from '../constants/ui';
import { convertFiltersToFilterOptions } from '../utils/transactions';
import { generateAccessibilityLabel, getAccessibilityHint } from '../utils/ui';

// Define transaction type locally
type Transaction = SalesOrder | SalesInvoice;

interface TransactionScreenProps {
  transactionType: 'order' | 'invoice';
  fetchTransactions: (params: FetchTransactionsParams) => Promise<{ data: Transaction[]; total: number }>;
  title: string;
}

const TransactionScreenBase: React.FC<TransactionScreenProps> = ({
  transactionType,
  fetchTransactions,
  title,
}) => {
  const insets = useSafeAreaInsets();

  // Use consolidated transaction manager hook
  const {
    transactions,
    loading,
    refreshing,
    hasMore,
    total,
    error,
    searchQuery,
    setSearchQuery,
    selectedStatuses,
    handleApplyStatusFilters,
    detailsModalVisible,
    selectedTransactionId,
    openDetailsModal,
    closeDetailsModal,
    filterModalVisible,
    openFilterModal,
    closeFilterModal,
    filterGroups,
    currentFilterValues,
    handleClearFilters,
    handleQuickStatusFilter,
    filterCount,
    loadMore,
    handleRefresh,
    handleRetry,
    flashListRef,
  } = useTransactionManager({ transactionType, fetchTransactions });

  // Convert current filter values to FilterOption format for FilterBar
  const activeFilters = useMemo(() => {
    return convertFiltersToFilterOptions(currentFilterValues, transactionType);
  }, [currentFilterValues, transactionType]);

  // Handle individual filter removal
  const handleRemoveFilter = useCallback((filterId: string) => {
    // Handle status filters
    if (filterId.startsWith('status:')) {
      const statusToRemove = filterId.replace('status:', '');
      const updatedStatuses = selectedStatuses.filter(status => status !== statusToRemove);
      handleApplyStatusFilters(updatedStatuses);
    } else {
      // Handle other filter types (for future expansion)
      if (__DEV__) {
        console.log('Removing filter:', filterId);
      }
      // For now, just handle status filters since that's what we have
      const updatedStatuses = selectedStatuses.filter(status => status !== filterId);
      handleApplyStatusFilters(updatedStatuses);
    }
  }, [selectedStatuses, handleApplyStatusFilters]);

  // Show error state if there's an error and no data
  if (error && (!transactions || transactions.length === 0)) {
    return (
      <View style={[
        transactionScreenStyles.container,
        { paddingTop: insets.top }
      ]}>
        {/* Header */}
        <PageHeader
          title={title}
          features={{
            search: {
              value: searchQuery,
              onChangeText: setSearchQuery,
              placeholder: SEARCH.PLACEHOLDER.TRANSACTIONS,
            },
            filter: {
              onPress: openFilterModal,
              isActive: filterCount > 0,
            },
          }}
        />

        {/* Error State */}
        <View
          style={transactionScreenStyles.errorContainer}
          accessible={true}
          accessibilityRole="alert"
          accessibilityLabel={generateAccessibilityLabel(
            'Error loading transactions',
            [error || 'Unknown error occurred']
          )}
        >
          <MaterialCommunityIcons
            name="alert-circle-outline"
            size={64}
            color="#E53E3E"
            accessible={false}
          />
          <Text
            style={transactionScreenStyles.errorText}
            accessible={true}
            accessibilityRole="text"
          >
            {error}
          </Text>
          <Button
            mode="contained"
            onPress={handleRetry}
            style={transactionScreenStyles.retryButton}
            accessible={true}
            accessibilityLabel="Retry loading transactions"
            accessibilityHint={getAccessibilityHint('retry loading the transactions')}
          >
            Retry
          </Button>
        </View>

        {/* Modals */}
        <TransactionDetailScreen
          visible={detailsModalVisible}
          onClose={closeDetailsModal}
          transactionId={selectedTransactionId}
          transactionType={transactionType}
          isModal={true}
        />
        <FilterModal
          visible={filterModalVisible}
          onClose={closeFilterModal}
          filterGroups={filterGroups}
          selectedFilters={selectedStatuses}
          onApplyFilters={handleApplyStatusFilters}
          onClearFilters={handleClearFilters}
        />
      </View>
    );
  }

  // Generate accessibility label for the main screen
  const screenAccessibilityLabel = generateAccessibilityLabel(
    `${title} screen`,
    [
      `${transactions?.length || 0} ${transactionType}s`,
      loading ? 'Loading' : '',
      error ? 'Error occurred' : '',
      activeFilters.length > 0 ? `${activeFilters.length} filter${activeFilters.length === 1 ? '' : 's'} active` : ''
    ].filter(Boolean)
  );

  return (
    <View
      style={[
        transactionScreenStyles.container,
        { paddingTop: insets.top }
      ]}
      accessible={true}
      accessibilityLabel={screenAccessibilityLabel}
    >
      {/* Transaction Details Modal */}
      <TransactionDetailScreen
        visible={detailsModalVisible}
        onClose={closeDetailsModal}
        transactionId={selectedTransactionId}
        transactionType={transactionType}
        isModal={true}
      />

      {/* Filter Modal */}
      <FilterModal
        visible={filterModalVisible}
        onClose={closeFilterModal}
        filterGroups={filterGroups}
        selectedFilters={selectedStatuses}
        onApplyFilters={handleApplyStatusFilters}
        onClearFilters={handleClearFilters}
      />

      {/* Header */}
      <PageHeader
        title={title}
        features={{
          search: {
            value: searchQuery,
            onChangeText: setSearchQuery,
            placeholder: SEARCH.PLACEHOLDER.TRANSACTIONS,
          },
          filter: {
            onPress: openFilterModal,
            isActive: filterCount > 0,
          },
        }}
      />

      {/* Filter Bar - Show active filters as chips */}
      {activeFilters.length > 0 && (
        <FilterBar
          filters={activeFilters}
          onRemoveFilter={handleRemoveFilter}
        />
      )}

      {/* Transaction List */}
      <TransactionList
        transactions={transactions}
        transactionType={transactionType}
        loading={loading}
        refreshing={refreshing}
        hasMore={hasMore}
        total={total}
        searchQuery={searchQuery}
        selectedStatuses={selectedStatuses}
        flashListRef={flashListRef}
        onTransactionPress={openDetailsModal}
        onStatusPress={handleQuickStatusFilter}
        onRefresh={handleRefresh}
        onLoadMore={loadMore}
        onClearFilters={handleClearFilters}
      />
    </View>
  );
};

// Memoize the TransactionScreen component
const TransactionScreen = memo(TransactionScreenBase, (prevProps, nextProps) => {
  // Only re-render if critical props change
  return (
    prevProps.transactionType === nextProps.transactionType &&
    prevProps.fetchTransactions === nextProps.fetchTransactions &&
    prevProps.title === nextProps.title
  );
});

export default TransactionScreen;
