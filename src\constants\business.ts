/**
 * Business Constants
 * 
 * Contains business logic constants including transaction statuses and options
 */

import { STATUS } from './colors';

export const TRANSACTION = {
  // Status colors mapping
  STATUS_COLORS: {
    'Draft': STATUS.DRAFT,
    'On Hold': STATUS.ON_HOLD,
    'To Deliver and Bill': STATUS.TO_DELIVER_AND_BILL,
    'To Bill': STATUS.TO_BILL,
    'To Deliver': STATUS.TO_DELIVER,
    'Completed': STATUS.COMPLETED,
    'Cancelled': STATUS.CANCELLED,
    'Closed': STATUS.CLOSED,
    'Partly Delivered': STATUS.PARTLY_DELIVERED,
    'Partly Billed': STATUS.PARTLY_BILLED,
    'Paid': STATUS.PAID,
    'Unpaid': STATUS.UNPAID,
    'Overdue': STATUS.OVERDUE,
    'Return': STATUS.RETURN,
    'Credit Note Issued': STATUS.CREDIT_NOTE_ISSUED,
    'Submitted': STATUS.SUBMITTED,
    'Partly Paid': STATUS.PARTLY_PAID,
    'Unpaid and Discounted': STATUS.UNPAID_AND_DISCOUNTED,
    'Partly Paid and Discounted': STATUS.PARTLY_PAID_AND_DISCOUNTED,
    'Overdue and Discounted': STATUS.OVERDUE_AND_DISCOUNTED,
    'Internal Transfer': STATUS.INTERNAL_TRANSFER,
  },

  // Order status options
  ORDER_STATUS_OPTIONS: [
    { label: 'Draft', value: 'Draft' },
    { label: 'On Hold', value: 'On Hold' },
    { label: 'To Deliver and Bill', value: 'To Deliver and Bill' },
    { label: 'To Bill', value: 'To Bill' },
    { label: 'To Deliver', value: 'To Deliver' },
    { label: 'Completed', value: 'Completed' },
    { label: 'Cancelled', value: 'Cancelled' },
    { label: 'Closed', value: 'Closed' },
  ],

  // Invoice status options
  INVOICE_STATUS_OPTIONS: [
    { label: 'Draft', value: 'Draft' },
    { label: 'Return', value: 'Return' },
    { label: 'Credit Note Issued', value: 'Credit Note Issued' },
    { label: 'Submitted', value: 'Submitted' },
    { label: 'Paid', value: 'Paid' },
    { label: 'Partly Paid', value: 'Partly Paid' },
    { label: 'Unpaid', value: 'Unpaid' },
    { label: 'Unpaid and Discounted', value: 'Unpaid and Discounted' },
    { label: 'Partly Paid and Discounted', value: 'Partly Paid and Discounted' },
    { label: 'Overdue and Discounted', value: 'Overdue and Discounted' },
    { label: 'Overdue', value: 'Overdue' },
    { label: 'Cancelled', value: 'Cancelled' },
    { label: 'Internal Transfer', value: 'Internal Transfer' },
  ],
};
