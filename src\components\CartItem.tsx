import React, { useMemo } from 'react';
import { View } from 'react-native';
import { CartData } from '../types/business';
import { calculateCartTotal } from '../orchestrators/cart';
import CartHeader from './CartHeader';
import CartContent from './CartContent';

interface CartItemProps {
  cart: CartData;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onCheckout: () => void;
  onContinueShopping: () => void;
  onRemove: () => void;
  styles: any;
}

/**
 * Main cart item component that combines header and expandable content
 */
const CartItem: React.FC<CartItemProps> = ({
  cart,
  isExpanded,
  onToggleExpand,
  onCheckout,
  onContinueShopping,
  onRemove,
  styles
}) => {
  // 🚀 PERFORMANCE FIX: Memoize cart total calculation to prevent recalculation on every render
  const totalAmount = useMemo(() => calculateCartTotal(cart.items), [cart.items]);

  return (
    <View style={styles.cartContainer}>
      <CartHeader
        cart={cart}
        isExpanded={isExpanded}
        onToggleExpand={onToggleExpand}
        totalAmount={totalAmount}
        styles={styles}
      />

      {isExpanded && (
        <CartContent
          cart={cart}
          onCheckout={onCheckout}
          onContinueShopping={onContinueShopping}
          onRemove={onRemove}
          styles={styles}
        />
      )}
    </View>
  );
};

export default React.memo(CartItem);
