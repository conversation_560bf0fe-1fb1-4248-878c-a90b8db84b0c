import { View, StyleSheet } from 'react-native';
import { Text } from 'react-native-paper';

// Not found screen for invalid routes
const NotFoundScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      <Text variant="headlineMedium" style={styles.title}>
        Page Not Found
      </Text>
      <Text variant="bodyLarge" style={styles.message}>
        The page you are looking for does not exist.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, justifyContent: 'center', alignItems: 'center', padding: 16, backgroundColor: '#f5f5f5' },
  title: { fontWeight: 'bold', marginBottom: 16, color: '#333' },
  message: { color: '#333' },
});

export default NotFoundScreen;