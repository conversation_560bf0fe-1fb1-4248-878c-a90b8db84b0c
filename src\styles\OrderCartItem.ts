import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const orderCartItemStyles = StyleSheet.create({
  itemContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL / 2, // Reduced spacing between cards
    marginHorizontal: MD.SPACING.XSMALL / 2, // Reduced horizontal margin to close gap with sidebar
    paddingHorizontal: MD.SPACING.SMALL, // Reduced from MEDIUM to SMALL
    paddingVertical: MD.SPACING.XSMALL, // Reduced from SMALL to XSMALL
    position: 'relative',
    elevation: MD.ELEVATION.LIGHT,
  },
  deleteButton: {
    position: 'absolute',
    top: 2,
    right: 2,
    zIndex: 1,
    padding: 4,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - uniform text size
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    flex: 1,
    marginBottom: MD.SPACING.XSMALL / 4, // Reduced spacing
    lineHeight: 16,
    height: 32, // Fixed height for exactly 2 lines (16px line height * 2)
    paddingRight: 22,
  },
  itemDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: MD.SPACING.XSMALL / 2, // Reduced spacing
  },
  itemDetailColumn: {
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: 1, // Reduced from 2 to 1
    flex: 1,
  },
  itemDetailLabel: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - uniform with other text
    color: NEUTRAL.TEXT_SECONDARY,
    fontWeight: '500',
    marginBottom: 1, // Reduced from 2 to 1
    letterSpacing: 0.25,
  },
  itemDetailValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - uniform text size
    color: NEUTRAL.TEXT_PRIMARY,
    fontWeight: '600',
    letterSpacing: 0.25,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: MD.SPACING.XSMALL / 2, // Reduced from 8 to smaller spacing
  },
});
