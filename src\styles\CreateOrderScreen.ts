import { StyleSheet } from 'react-native';
import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';
// Extracted shared styles locally

// Local itemCardStyles for create order screen
const itemCardStyles = StyleSheet.create({
  itemContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL,
    marginHorizontal: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
    position: 'relative',
    elevation: MD.ELEVATION.LIGHT,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - uniform text size
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    flex: 1,
    marginBottom: MD.SPACING.XSMALL / 4, // Reduced spacing
    lineHeight: 16,
    minHeight: 24, // Further reduced for compactness
    paddingRight: 22,
  },
  itemDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start', // Changed from space-between to flex-start
    alignItems: 'center',
    marginBottom: MD.SPACING.XSMALL,
    paddingRight: 30, // Add padding to avoid overlap with delete button
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  centerControls: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: MD.SPACING.SMALL, // Add horizontal margin for better visual balance
  },
  deleteButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    padding: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
});

// Local actionButtonStyles for create order screen
const actionButtonStyles = StyleSheet.create({
  primaryButton: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    height: 48,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    elevation: 2,
  },
  disabledButton: {
    backgroundColor: BRAND.PRIMARY_100,
    opacity: 0.7,
  },
  buttonText: {
    fontSize: MD.TYPOGRAPHY.SUBTITLE2.fontSize, // Increased from 14px to 14px but using SUBTITLE2
    fontWeight: '600', // Increased from 500 to 600 for better prominence
    color: NEUTRAL.WHITE,
    letterSpacing: MD.TYPOGRAPHY.SUBTITLE2.letterSpacing,
  },
  buttonAmount: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize, // Increased from 15px to 16px for better visibility
    color: NEUTRAL.WHITE,
    fontWeight: '600', // Increased from 500 to 600 for consistency
  },
});

export const createOrderScreenStyles = StyleSheet.create({
  // Main container - consistent with customer and transaction screens
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  headerContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  headerRightElements: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 88, // Reserve space for both clear cart icon (40px) + cart button (40px) + spacing (8px)
    justifyContent: 'flex-end', // Align items to the right
  },
  content: {
    flex: 1,
  },
  modernLayout: {
    flex: 1,
    flexDirection: 'row',
  },
  mainContent: {
    flex: 1,
    flexDirection: 'column',
  },
  listContainer: {
    flex: 1,
    position: 'relative',
  },
  // FlatList container with defined height for optimal performance
  flashListContainer: {
    flex: 1,
    height: '100%',
  },
  // Actual Qty display styles
  actualQtyContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 80,
  },
  actualQtyLabel: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px - smaller for labels
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: 4,
    textAlign: 'center',
    fontWeight: '500', // Added weight for better visibility
  },
  actualQtyValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance for values
    fontWeight: '700', // Increased from 600 to 700 for better prominence
    color: SEMANTIC.SUCCESS,
    textAlign: 'center',
  },
  // Loading and error states - standard pattern
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: NEUTRAL.WHITE,
  },
  loadingText: {
    marginTop: MD.SPACING.LARGE,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize, // Match other screens
    color: NEUTRAL.TEXT_SECONDARY,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: MD.SPACING.XLARGE,
  },
  errorText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance
    color: SEMANTIC.ERROR,
    marginBottom: MD.SPACING.LARGE,
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.SMALL,
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  retryButtonText: {
    color: NEUTRAL.WHITE,
    fontWeight: '500',
  },
  // Footer styling consistent with transaction screens
  footer: {
    padding: MD.SPACING.LARGE,
    paddingBottom: 16,
    backgroundColor: NEUTRAL.WHITE,
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    elevation: 4,
  },
  // Filter container styling consistent with transaction screens
  filterContainer: {
    backgroundColor: NEUTRAL.WHITE,
    paddingVertical: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    justifyContent: 'center',
  },
  chipsContainer: {
    paddingVertical: MD.SPACING.XSMALL / 2,
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  filterChip: {
    marginRight: MD.SPACING.SMALL - 2,
    marginBottom: MD.SPACING.XSMALL,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 0,
    borderWidth: 1,
    borderColor: BRAND.PRIMARY,
    backgroundColor: 'transparent',
  },
  selectedFilterChip: {
    backgroundColor: BRAND.PRIMARY_50,
    borderColor: BRAND.PRIMARY,
  },
  filterChipText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance for readability
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '500',
    lineHeight: 16, // Adjusted for better spacing
    textAlignVertical: 'center',
    color: BRAND.PRIMARY,
  },
  selectedFilterChipText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - consistent with unselected
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '600', // Increased weight for selected state
    lineHeight: 16, // Adjusted for better spacing
    textAlignVertical: 'center',
    color: BRAND.PRIMARY,
  },
  itemGroupFilterContainer: {
    marginTop: MD.SPACING.XSMALL / 2,
    paddingTop: MD.SPACING.XSMALL,
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    justifyContent: 'center',
  },
  // Cart and header elements
  cartButton: {
    position: 'relative',
    margin: 0,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: SEMANTIC.ERROR,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    paddingHorizontal: MD.SPACING.XSMALL,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: NEUTRAL.WHITE,
  },
  cartBadgeWide: {
    minWidth: 26,
    paddingHorizontal: MD.SPACING.SMALL - 2,
  },
  cartBadgeText: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px - smaller for badge
    fontWeight: 'bold',
    textAlign: 'center',
  },
  headerIcon: {
    margin: 0,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: MD.SPACING.LARGE,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
  },
  // Navigation overlay for smooth transitions
  navigationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },

  // Item card styles - using shared styles where possible
  itemContainer: itemCardStyles.itemContainer,
  itemName: itemCardStyles.itemName,
  itemDetailsRow: itemCardStyles.itemDetailsRow,
  controlsContainer: itemCardStyles.controlsContainer,
  centerControls: itemCardStyles.centerControls,
  deleteButton: {
    ...itemCardStyles.deleteButton,
    top: 6, // Moved down slightly for better visual balance
    right: 6, // Moved in slightly to avoid edge overlap
    padding: 6, // Increased padding for better touch area
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // Slightly more opaque for better visibility
    borderRadius: 14, // Increased border radius to match new padding
    zIndex: 10, // Ensure it's above other elements
  },

  // Button styles - using shared styles where possible
  checkoutButton: actionButtonStyles.primaryButton,
  disabledButton: actionButtonStyles.disabledButton,
  checkoutButtonText: actionButtonStyles.buttonText,
  checkoutButtonAmount: actionButtonStyles.buttonAmount,
});
