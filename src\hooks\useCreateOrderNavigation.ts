import { useCallback } from 'react';
import { <PERSON><PERSON>, BackHand<PERSON> } from 'react-native';
import { router } from 'expo-router';
import { fetchCustomers } from '../services/customerService';
import { UseCreateOrderNavigationProps, UseCreateOrderNavigationReturn } from '../types/business';
import { prepareCartItemsForNavigation } from '../orchestrators/cart';

export const useCreateOrderNavigation = ({
  customerId,
  customerName,
  cartItems,
  items,
  params,
  saveCartData
}: UseCreateOrderNavigationProps): UseCreateOrderNavigationReturn => {

  // Simplified checkout handler
  const handleCheckout = useCallback(async () => {
    try {
      // Validate cart has items
      const activeCartItems = cartItems.filter(item => item.quantity > 0);
      if (activeCartItems.length === 0) {
        Alert.alert('No Items', 'Please add at least one item to proceed.');
        return;
      }

      // Save cart to AsyncStorage BEFORE navigation to ensure checkout sees the data
      await saveCartData();

      // Get customer details for checkout
      try {
        const customers = await fetchCustomers();
        const customer = customers.find(c => c.name === customerId);

        // Simple address handling
        let primaryAddressText = '';
        if (customer?.addresses && customer.addresses.length > 0) {
          const primaryAddress = customer.addresses.find(addr =>
            addr.name === customer.customer_primary_address
          ) || customer.addresses[0];

          if (primaryAddress) {
            primaryAddressText = primaryAddress.address_line1 +
              (primaryAddress.city && primaryAddress.city !== '-' ? `, ${primaryAddress.city}` : '');
          }
        }

        // Navigate to checkout with customer details - cart loads from AsyncStorage
        router.push({
          pathname: '/checkout',
          params: {
            customerId,
            customerName,
            tax_id: customer?.tax_id || '',
            custom_credit_limit: (customer?.custom_credit_limit || 0).toString(),
            outstanding: (customer?.outstanding || 0).toString(),
            default_price_list: customer?.default_price_list || '',
            mobile_no: customer?.mobile_no || '',
            customer_primary_address: primaryAddressText,
            from: 'create-order',
            originalSource: params?.from === 'cart' ? 'cart' : params?.from === 'reorder' ? 'reorder' : 'customer'
          },
        });
      } catch (customerError) {
        // Fallback navigation without customer details - cart loads from AsyncStorage
        router.push({
          pathname: '/checkout',
          params: {
            customerId,
            customerName,
            from: 'create-order',
            originalSource: params?.from === 'cart' ? 'cart' : params?.from === 'reorder' ? 'reorder' : 'customer'
          },
        });
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to navigate to checkout. Please try again.');
    }
  }, [cartItems, customerId, customerName, items, params]);



  // Simple cart navigation
  const handleGoToCart = useCallback(() => {
    const hasItems = cartItems.some(item => item.quantity > 0);
    if (!hasItems) {
      Alert.alert('Cart Empty', 'Your cart is empty. Add items to view carts.');
      return;
    }
    router.push('/(tabs)/carts');
  }, [cartItems]);

  // Ultra-fast back handler - zero processing delays
  const setupBackHandler = useCallback(() => {
    const handleBackPress = () => {
      // INSTANT navigation - no cart saving or processing
      const source = params?.from;
      if (source === 'cart') {
        router.push('/(tabs)/carts');
      } else if (source === 'reorder') {
        router.push('/(tabs)/orders');
      } else {
        router.push('/(tabs)/customers');
      }
      return true; // Prevent default back behavior
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [params]);

  return {
    isNavigating: false,
    handleCheckout,
    handleGoToCart,
    setupBackHandler
  };
};
