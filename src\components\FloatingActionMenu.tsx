import React, { useCallback } from 'react';
import {
  View,
  TouchableOpacity,
  Modal,
  Alert,
  TouchableWithoutFeedback,
  Linking,
} from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Customer } from '../types/business';
import { floatingActionMenuStyles } from '../styles/FloatingActionMenu';

interface FloatingActionMenuProps {
  visible: boolean;
  onClose: () => void;
  position: { x: number; y: number; width: number; height: number };
  customer: Customer;
  onPlaceOrder: (customer: Customer) => void;
  onShowAddresses?: () => void;
  onNavigateToInvoices?: (customerId: string) => void;
}

// Simplified FloatingActionMenu - removed complex animations and positioning
const FloatingActionMenu: React.FC<FloatingActionMenuProps> = ({
  visible,
  onClose,
  position,
  customer,
  onPlaceOrder,
  onShowAddresses
}) => {
  const theme = useTheme();

  // Simple positioning - center on screen
  const menuStyle = {
    top: position.y + position.height + 10,
    left: Math.max(10, position.x - 100),
  };

  // Simplified call handler
  const handleCall = useCallback(() => {
    if (!customer.mobile_no) {
      Alert.alert('No Mobile Number', 'Mobile number is not available for this customer.');
      return;
    }
    onClose();
    Linking.openURL(`tel:${customer.mobile_no.replace(/\s+/g, '')}`).catch(() => {
      Alert.alert('Error', 'Could not open phone app');
    });
  }, [customer.mobile_no, onClose]);

  if (!visible) return null;

  return (
    <Modal transparent visible={true} animationType="none" onRequestClose={onClose}>
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={floatingActionMenuStyles.modalOverlay}>
          <View style={[floatingActionMenuStyles.menuContainer, menuStyle]}>
            <View style={floatingActionMenuStyles.menuItemsContainer}>
              <View style={floatingActionMenuStyles.menuRow}>
                <TouchableOpacity
                  style={[floatingActionMenuStyles.menuItem, !customer.mobile_no && floatingActionMenuStyles.disabledMenuItem]}
                  onPress={handleCall}
                  disabled={!customer.mobile_no}
                >
                  <MaterialCommunityIcons
                    name="phone"
                    size={24}
                    color={!customer.mobile_no ? '#ccc' : theme.colors.primary}
                  />
                  <Text style={[floatingActionMenuStyles.menuItemText, !customer.mobile_no && floatingActionMenuStyles.disabledMenuItemText]}>Call</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[floatingActionMenuStyles.menuItem, (!customer.addresses?.length) && floatingActionMenuStyles.disabledMenuItem]}
                  onPress={() => {
                    onClose();
                    onShowAddresses?.();
                  }}
                  disabled={!customer.addresses?.length}
                >
                  <MaterialCommunityIcons
                    name="map-marker-multiple"
                    size={24}
                    color={!customer.addresses?.length ? '#ccc' : theme.colors.primary}
                  />
                  <Text style={[floatingActionMenuStyles.menuItemText, !customer.addresses?.length && floatingActionMenuStyles.disabledMenuItemText]}>Address</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={floatingActionMenuStyles.menuItem}
                  onPress={() => {
                    // Immediate navigation - close menu and navigate simultaneously
                    onPlaceOrder(customer);
                    onClose();
                  }}
                >
                  <MaterialCommunityIcons name="cart-plus" size={24} color={theme.colors.primary} />
                  <Text style={floatingActionMenuStyles.menuItemText}>Order</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default FloatingActionMenu;
