import * as SecureStorage from '../utils/auth';
import { STORAGE_KEYS } from '../constants/storage';
import { getLoggedUser } from '../services/loggedUserService';
import { ErrorType } from '../utils/errors';
import { SessionValidationResult } from '../types/initialization';

export const hasStoredSession = async (): Promise<boolean> => {
  try {
    const sid = await SecureStorage.getSecureItem(STORAGE_KEYS.SID);
    return !!sid;
  } catch {
    return false;
  }
};


export const validateSession = async (): Promise<SessionValidationResult> => {
  try {
    const sid = await SecureStorage.getSecureItem(STORAGE_KEYS.SID);
    if (!sid) return { isValid: false, shouldRedirectToLogin: true };

    await getLoggedUser();
    return { isValid: true, shouldRedirectToLogin: false };
  } catch (error: any) {
    if (error?.type === ErrorType.SESSION_EXPIRED) {
      return { isValid: false, shouldRedirectToLogin: true, error };
    }

    if (error?.type === ErrorType.NETWORK || error?.type === ErrorType.TIMEOUT) {
      return { isValid: false, shouldRedirectToLogin: false, error };
    }

    return { isValid: false, shouldRedirectToLogin: true, error };
  }
};


export const validateSessionOnAppStateChange = async (): Promise<SessionValidationResult> => {
  try {
    const sid = await SecureStorage.getSecureItem(STORAGE_KEYS.SID);
    if (!sid) return { isValid: false, shouldRedirectToLogin: false };

    await getLoggedUser();
    return { isValid: true, shouldRedirectToLogin: false };
  } catch (error: any) {
    if (error?.type === ErrorType.SESSION_EXPIRED) {
      return { isValid: false, shouldRedirectToLogin: true, error };
    }
    return { isValid: false, shouldRedirectToLogin: false, error };
  }
};

export const clearSession = async (): Promise<void> => {
  try {
    await Promise.all([
      SecureStorage.removeSecureItem(STORAGE_KEYS.SID),
      SecureStorage.removeSecureItem(STORAGE_KEYS.SID_EXPIRES_AT)
    ]);
  } catch (error) {
    // Silently handle session clearing errors
  }
};
