import { useCallback, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Al<PERSON> } from 'react-native';
import { router } from 'expo-router';

import {
  UseCheckoutNavigationReturn,
  NavigationOptions,
  UomChangeFunction
} from '../types/business';
import { ROUTES } from '../constants/routes';
import { CHECKOUT_MESSAGES } from '../constants/messages';

export const useCheckoutNavigation = (
  customerId: string,
  customerName: string,
  navigationOptions: NavigationOptions
): UseCheckoutNavigationReturn & { changeUom: UomChangeFunction } => {
  
  // Handle back navigation - simplified since AsyncStorage is now always updated
  const handleBackPress = useCallback(() => {
    const { fromCart, originalSource } = navigationOptions;

    if (fromCart) {
      // Navigate back to cart - it will load from AsyncStorage
      router.push(ROUTES.CARTS);
    } else {
      // Navigate back to create-order - it will load from AsyncStorage
      router.push({
        pathname: ROUTES.CREATE_ORDER,
        params: {
          customerId,
          customerName,
          from: originalSource === 'cart' ? 'cart' : originalSource === 'reorder' ? 'reorder' : 'customer',
        },
      });
    }
  }, [customerId, customerName, navigationOptions]);



  // Handle UOM change - restricted in checkout
  const changeUom: UomChangeFunction = useCallback(
    (_itemCode: string) => {
      Alert.alert(
        CHECKOUT_MESSAGES.UOM_CHANGE.TITLE,
        CHECKOUT_MESSAGES.UOM_CHANGE.MESSAGE,
        [{ text: CHECKOUT_MESSAGES.UOM_CHANGE.OK }]
      );
    },
    []
  );

  // Set up hardware back button handler
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleBackPress();
      return true; // Prevent default behavior
    });

    // Clean up function
    return () => {
      backHandler.remove();
    };
  }, [handleBackPress]);

  return {
    handleBackPress,
    changeUom,
  };
};
