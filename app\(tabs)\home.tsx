import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, Alert, ScrollView, TouchableOpacity, ActivityIndicator, StatusBar, InteractionManager } from 'react-native';
import { useTheme, IconButton } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { logout } from '../../src/services/authService';
import * as SecureStorage from '../../src/utils/auth';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { STORAGE_KEYS } from '../../src/constants/storage';
import { syncAllData } from '../../src/orchestrators/sync';
import { sharedStyles, homeStyles } from '../../src/styles/HomeScreen';
import { cacheService, CacheKey } from '../../src/utils/cacheService';
import { useBackgroundOperations } from '../../src/orchestrators/backgroundOperations';
import { checkForUpdates } from '../../src/utils/version';

import { UpdateDialog } from '../../src/components/UpdateDialog';
import { UpdateAction } from '../../src/types/business';


const HomeScreen: React.FC = () => {
  const [userName, setUserName] = useState<string>('');
  const [hasNavigated, setHasNavigated] = useState<boolean>(false);
  const [logoutLoading, setLogoutLoading] = useState<boolean>(false);
  const [syncing, setSyncing] = useState<boolean>(false);
  const [initializing, setInitializing] = useState<boolean>(true);
  const [optionalUpdate, setOptionalUpdate] = useState<UpdateAction | null>(null);
  const [forcedUpdate, setForcedUpdate] = useState<UpdateAction | null>(null);
  const [showOptionalUpdateDialog, setShowOptionalUpdateDialog] = useState<boolean>(false);

  const router = useRouter();
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  const backgroundOps = useBackgroundOperations();
  const isBackgroundOperationRunning = backgroundOps.isBackgroundOperationRunning;



  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const loadUserData = useCallback(async () => {
    if (hasNavigated) return;

    setUserName('User');
    setInitializing(false);

    InteractionManager.runAfterInteractions(async () => {
      try {
        const cachedFirstName = await SecureStorage.getSecureItem(STORAGE_KEYS.FIRST_NAME);

        let displayName = 'User';
        if (cachedFirstName && typeof cachedFirstName === 'string') {
          const cleanName = cachedFirstName.trim();

          if (cleanName.length > 0 &&
              !cleanName.startsWith('{') &&
              !cleanName.startsWith('[') &&
              !cleanName.includes('"') &&
              cleanName.length < 50) {
            displayName = cleanName;
          }
        }

        setUserName(displayName);

        cacheService.warmCache([CacheKey.CUSTOMERS]).catch(() => {});

      } catch (err) {
        setUserName('User');
      }
    });
  }, [hasNavigated]);

  // Load user data on mount
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  // Background version check
  useEffect(() => {
    const checkVersionInBackground = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        const action = await checkForUpdates();
        if (action.type === 'forced') setForcedUpdate(action);
        else if (action.type === 'optional') setOptionalUpdate(action);
      } catch (error) {
        // Silently handle background version check errors
      }
    };
    checkVersionInBackground();
  }, []);



  const handleSync = async () => {
    if (syncing || isBackgroundOperationRunning) return;

    try {
      Alert.alert(
        'Sync Data',
        'This will sync all data from the server. Continue?',
        [
          {
            text: 'Cancel',
            style: 'cancel'
          },
          {
            text: 'Sync',
            onPress: async () => {
              setSyncing(true);

              try {
                const result = await syncAllData();
                Alert.alert(
                  result ? 'Sync Complete' : 'Sync Failed',
                  result ? 'Data synchronization completed successfully' : 'Unknown error occurred during sync'
                );
              } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error during sync';
                Alert.alert('Sync Error', errorMessage);
              } finally {
                setSyncing(false);
              }
            }
          }
        ]
      );
    } catch (error) {
      // Silently handle sync errors
    }
  };

  const handleLogout = async () => {
    if (hasNavigated || isBackgroundOperationRunning) return;
    setLogoutLoading(true);

    try {
      setHasNavigated(true);

      router.replace({
        pathname: '/(auth)/login',
        params: { immediate: 'true' }
      });

      setTimeout(() => {
        logout(true).catch(() => {
          // Silently handle logout errors
        });
      }, 100);
    } catch (err) {
      if (!hasNavigated) {
        setHasNavigated(true);
        router.replace('/(auth)/login');
      }

      const errorMessage = err instanceof Error ? err.message : 'Logout failed';
      Alert.alert('Error', errorMessage);
    } finally {
      setLogoutLoading(false);
    }
  };

  return (
    <View style={[homeStyles.container, { paddingTop: insets.top }]}>
      <StatusBar barStyle="dark-content" backgroundColor="white" />
      <View style={homeStyles.header}>
        <View style={homeStyles.userSection}>
          <Text style={homeStyles.greeting}>{getGreeting()},</Text>
          <Text style={homeStyles.name}>{userName}</Text>

        </View>
        <View style={homeStyles.headerButtons}>
          {optionalUpdate && (
            <IconButton
              icon={optionalUpdate.type === 'forced' ? "alert-circle" : "download"}
              size={24}
              onPress={() => setShowOptionalUpdateDialog(true)}
              iconColor={optionalUpdate.type === 'forced' ? theme.colors.error : theme.colors.primary}
              style={{ padding: 8 }}
            />
          )}

          <TouchableOpacity
            onPress={handleSync}
            disabled={syncing || logoutLoading || initializing || isBackgroundOperationRunning}
            style={homeStyles.iconButton}
            accessibilityLabel="Sync Data"
            activeOpacity={0.7}
          >
            {syncing ? (
              <View style={homeStyles.iconContainer}>
                <ActivityIndicator size={20} color="white" />
              </View>
            ) : (
              <View style={[
                homeStyles.iconContainer,
                (isBackgroundOperationRunning && !syncing) && {
                  backgroundColor: '#CCCCCC',
                  elevation: 1,
                  shadowOpacity: 0.1
                }
              ]}>
                <MaterialCommunityIcons
                  name="sync"
                  size={20}
                  color={(isBackgroundOperationRunning && !syncing) ? '#888888' : 'white'}
                />
              </View>
            )}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleLogout}
            disabled={logoutLoading || syncing || initializing || isBackgroundOperationRunning}
            style={homeStyles.iconButton}
            accessibilityLabel="Logout"
            activeOpacity={0.7}
          >
            {logoutLoading ? (
              <View style={homeStyles.iconContainer}>
                <ActivityIndicator size={20} color="white" />
              </View>
            ) : (
              <View style={[
                homeStyles.iconContainer,
                (isBackgroundOperationRunning && !logoutLoading) && {
                  backgroundColor: '#CCCCCC',
                  elevation: 1,
                  shadowOpacity: 0.1
                }
              ]}>
                <MaterialCommunityIcons
                  name="logout"
                  size={20}
                  color={(isBackgroundOperationRunning && !logoutLoading) ? '#888888' : 'white'}
                />
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={homeStyles.scrollView}>
        {initializing ? (
          <View style={homeStyles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={homeStyles.loadingText}>Loading your workspace...</Text>
          </View>
        ) : (
          <View style={homeStyles.sectionContainer}>
            <View style={homeStyles.sectionTitleContainer}>
              <Text style={[sharedStyles.h2, homeStyles.sectionTitle]}>Dashboard</Text>
              <View style={homeStyles.titleDivider} />
            </View>

            <View style={homeStyles.dashboardContent}>
              <MaterialCommunityIcons
                name="view-dashboard-outline"
                size={64}
                color={theme.colors.primary}
                style={homeStyles.placeholderIcon}
              />
              <Text style={[sharedStyles.h2, homeStyles.placeholderTitle]}>Dashboard Coming Soon</Text>
              <Text style={homeStyles.placeholderText}>
                Your analytics and insights will be available here once the core features are complete.
              </Text>
            </View>
          </View>
        )}
      </ScrollView>

      {forcedUpdate && (
        <UpdateDialog
          visible={true}
          updateAction={forcedUpdate}
        />
      )}

      {optionalUpdate && (
        <UpdateDialog
          visible={showOptionalUpdateDialog}
          updateAction={optionalUpdate}
          onDismiss={() => setShowOptionalUpdateDialog(false)}
        />
      )}
    </View>
  );
};

export default HomeScreen;