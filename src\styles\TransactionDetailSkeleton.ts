import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const transactionDetailSkeletonStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  scrollViewContent: {
    padding: MD.SPACING.MEDIUM,
    paddingBottom: MD.SPACING.LARGE,
  },
  section: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
    marginBottom: MD.SPACING.MEDIUM,
    padding: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: MD.SPACING.XSMALL,
  },
  orderInfo: {
    flex: 1,
  },
  skeletonOrderId: {
    height: 20,
    width: '60%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.SMALL,
  },
  skeletonOrderDate: {
    height: 14,
    width: '40%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },

  skeletonStatusChip: {
    height: 32, // Match actual status chip height with padding
    width: 85,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonReorderButton: {
    height: 28, // Match exact height from actionButton
    width: 80, // Match minWidth from actionButton
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonSectionTitle: {
    height: 16,
    width: '30%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.MEDIUM,
  },
  skeletonCustomerName: {
    height: 16,
    width: '70%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.SMALL,
  },
  skeletonCustomerDetails: {
    height: 14,
    width: '90%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.SMALL,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XXSMALL,
  },
  skeletonDetailLabel: {
    height: 14,
    width: '40%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonDetailValue: {
    height: 14,
    width: '30%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  divider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginVertical: MD.SPACING.XSMALL,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XSMALL,
  },
  skeletonTotalLabel: {
    height: 16,
    width: '25%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonTotalValue: {
    height: 16,
    width: '35%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  itemsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  skeletonItemsCount: {
    height: 14,
    width: 40,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonItemCard: {
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    backgroundColor: NEUTRAL.WHITE,
    width: '100%',
  },
  skeletonItemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    marginBottom: MD.SPACING.XSMALL,
  },
  skeletonItemIndex: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: MD.SKELETON_BG,
    marginRight: MD.SPACING.SMALL,
  },
  skeletonItemName: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '70%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    flex: 1,
  },
  skeletonItemDetails: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingLeft: 24 + MD.SPACING.SMALL, // Match circle width + margin
  },
  skeletonItemDetail: {
    flex: 1,
    marginRight: MD.SPACING.SMALL,
  },
  skeletonItemDetailLabel: {
    height: 11, // Match MD.TYPOGRAPHY.CAPTION.fontSize
    width: '60%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL,
  },
  skeletonItemDetailValue: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '80%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonItemDivider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginTop: MD.SPACING.SMALL,
    marginHorizontal: 0,
    width: '100%',
  },
});
