/**
 * Common utility functions for the application
 *
 * This file contains common utility functions that can be used across the application.
 * Note: Timing utilities (debounce/throttle) moved to performance.ts to avoid duplication.
 */

// ===== String Utilities =====

/**
 * Truncate a string to a specified length and add ellipsis if needed
 * 
 * @param str String to truncate
 * @param maxLength Maximum length of the string
 * @returns Truncated string
 */
export function truncateString(str: string, maxLength: number): string {
  if (!str || str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
}

/**
 * Convert a string to title case (capitalize first letter of each word)
 * 
 * @param str String to convert
 * @returns Title case string
 */
export function toTitleCase(str: string): string {
  if (!str) return '';
  return str.replace(
    /\w\S*/g,
    txt => txt.charAt(0).toUpperCase() + txt.substring(1).toLowerCase()
  );
}

// ===== Array Utilities =====

/**
 * Group an array of objects by a specified key
 * 
 * @param array Array to group
 * @param key Key to group by
 * @returns Object with groups
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce((result, item) => {
    const groupKey = String(item[key]);
    result[groupKey] = result[groupKey] || [];
    result[groupKey].push(item);
    return result;
  }, {} as Record<string, T[]>);
}

/**
 * Remove duplicates from an array based on a key
 * 
 * @param array Array to process
 * @param key Key to use for uniqueness check
 * @returns Array with duplicates removed
 */
export function uniqueBy<T>(array: T[], key: keyof T): T[] {
  const seen = new Set();
  return array.filter(item => {
    const value = item[key];
    if (seen.has(value)) return false;
    seen.add(value);
    return true;
  });
}
