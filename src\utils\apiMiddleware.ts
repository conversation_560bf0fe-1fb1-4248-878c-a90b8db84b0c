import { AxiosInstance, AxiosResponse } from 'axios';
import { parseApiError } from './errors';

export const addErrorHandlingMiddleware = (client: AxiosInstance): AxiosInstance => {
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      if (response.status === 200) {
        return response;
      }

      return Promise.reject({
        response: response,
        isAxiosError: true
      });
    },

    (error) => {
      const parsedError = parseApiError(error);
      return Promise.reject(parsedError);
    }
  );

  return client;
};
