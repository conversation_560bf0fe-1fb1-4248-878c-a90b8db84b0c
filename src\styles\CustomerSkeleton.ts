import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const customerSkeletonStyles = StyleSheet.create({
  skeletonListItem: {
    backgroundColor: NEUTRAL.WHITE,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    elevation: 0,
    shadowOpacity: 0,
  },
  skeletonCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  skeletonNameContainer: {
    flex: 1,
    marginRight: MD.SPACING.XSMALL,
    flexShrink: 1,
    minHeight: 72, // Match the real customer card
  },
  skeletonName: {
    height: 16, // Match MD.TYPOGRAPHY.BODY1.fontSize
    width: '70%',
    marginBottom: MD.SPACING.XSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
  skeletonDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: MD.SPACING.XSMALL / 2,
    minHeight: MD.SPACING.LARGE,
  },
  skeletonTerritory: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '30%',
    marginRight: MD.SPACING.SMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
  skeletonId: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '40%',
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
  skeletonFinancialContainer: {
    alignItems: 'flex-end',
    marginLeft: MD.SPACING.XSMALL / 2,
    minWidth: 90,
    flexShrink: 0, // Match the real customer card
  },
  skeletonFinancialRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: MD.SPACING.XSMALL / 4,
    justifyContent: 'flex-end',
  },
  skeletonIconPlaceholder: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: MD.SPACING.XSMALL,
  },
  skeletonValue: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: 75, // Match valueContainer minWidth
    marginBottom: MD.SPACING.XSMALL / 4,
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
  // Add address skeleton styles to match the real customer card
  skeletonAddressContainer: {
    marginTop: MD.SPACING.XSMALL / 2,
    height: 32, // Match addressContainer height
  },
  skeletonAddressLine1: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '85%',
    marginBottom: 2,
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
  skeletonAddressLine2: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize
    width: '60%',
    borderRadius: MD.BORDER_RADIUS.SMALL / 2,
  },
});
