export interface LoginResponse {
  message: string;
  full_name: string;
  home_page?: string;
}

export interface LoggedUserResponse {
  message: string;
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface LoginFormErrors {
  email: string;
  password: string;
  general: string;
}

export interface LoginFormState {
  data: LoginFormData;
  errors: LoginFormErrors;
  loading: boolean;
  secureTextEntry: boolean;
}

export interface LoginValidationResult {
  isValid: boolean;
  errors: Partial<LoginFormErrors>;
}

export interface LoginResult {
  success: boolean;
  shouldNavigate: boolean;
  redirectTo?: string;
  error?: string;
}

export interface CachedEmailResult {
  email: string | null;
  source: 'primary' | 'backup' | 'none';
}
