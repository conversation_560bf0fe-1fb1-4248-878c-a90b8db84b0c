import React from 'react';
import { View, Text } from 'react-native';
import { Button } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CartEmptyStateProps } from '../types/business';
import { ROUTES } from '../constants/routes';
import { CART_MESSAGES } from '../constants/messages';
import { ICON_SIZES } from '../constants/ui';

/**
 * Reusable empty state component for when no carts are available
 */
const CartEmptyState: React.FC<CartEmptyStateProps> = ({
  onGoToCustomers,
  styles
}) => {
  return (
    <View style={styles.emptyContainer}>
      <MaterialCommunityIcons 
        name="cart-off" 
        size={ICON_SIZES.LARGE}
        color="#ccc" 
      />
      <Text style={styles.emptyText}>
        {CART_MESSAGES.EMPTY_STATE.TITLE}
      </Text>
      <Text style={styles.emptySubtext}>
        {CART_MESSAGES.EMPTY_STATE.SUBTITLE}
      </Text>
      <Button
        mode="contained"
        onPress={onGoToCustomers}
        style={styles.emptyButton}
        icon="account-group"
      >
        {CART_MESSAGES.EMPTY_STATE.BUTTON_TEXT}
      </Button>
    </View>
  );
};

export default React.memo(CartEmptyState);
