import { apiClient } from './apiClient';
import { API } from '../constants/api';
import { withErrorHandling } from '../utils/errors';
import { Item } from '../types/business';
import { FetchItemsResponse } from '../types/api';

/**
 * Fetch items from API
 *
 * @returns Array of items
 * @throws Error if the API call fails
 */
export const fetchItems = async (): Promise<Item[]> => {
  return withErrorHandling(async () => {
    const response = await apiClient.get<FetchItemsResponse>(API.ENDPOINTS.ITEMS);
    return response?.data?.message || [];
  });
};
