import React, { useEffect, useRef, useCallback, useState } from 'react';
import { View, Text, Keyboard, FlatList } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ActivityIndicator, useTheme } from 'react-native-paper';
import { Stack, useLocalSearchParams, useFocusEffect, router } from 'expo-router';
import { TabBarUtils } from '../../src/utils/ui';
import PageHeader from '../../src/components/PageHeader';

import { cartScreenStyles } from '../../src/styles/CartScreen';
import { SCREEN_TITLES } from '../../src/constants/messages';

import { useCartManager } from '../../src/hooks/useCartManager';


import { validateCartParams } from '../../src/orchestrators/cart';
import { CartData } from '../../src/types/business';
import CartItemComponent from '../../src/components/CartItem';
import CartEmptyState from '../../src/components/CartEmptyState';

export default function CartsScreen() {
  const params = useLocalSearchParams();
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  const styles = cartScreenStyles;
  const {
    carts,
    loading,
    loadAllCarts,
    forceRefreshCarts,
    handleNewCartItems,
    handleCheckout,
    handleContinueShopping,
    handleRemoveCart,
    handleToggleExpand,
    expandedCart
  } = useCartManager();

  // Navigation handler for UI elements (PageHeader back button, empty state)
  const handleBackPress = useCallback(() => {
    router.push('/(tabs)/home');
  }, []);

  // Separate handler for "Go to Customers" button in empty state
  const handleGoToCustomers = useCallback(() => {
    router.push('/(tabs)/customers');
  }, []);

  // Local state for filtered carts (for search)
  const [filteredCarts, setFilteredCarts] = useState<CartData[]>(carts);

  // Simple search functionality
  const [searchQuery, setSearchQuery] = useState('');

  // Update filtered carts when carts or search query change
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCarts(carts);
      return;
    }

    const filtered = carts.filter(cart => {
      const query = searchQuery.toLowerCase();
      return (
        cart.customerName.toLowerCase().includes(query) ||
        cart.items.some(item =>
          item.item_name.toLowerCase().includes(query) ||
          item.item_code.toLowerCase().includes(query)
        )
      );
    });

    setFilteredCarts(filtered);
  }, [carts, searchQuery]);

  // Search props for PageHeader
  const searchProps = {
    value: searchQuery,
    onChangeText: setSearchQuery,
    placeholder: 'Item Name, Customer'
  };



  const flatListRef = useRef<FlatList<any>>(null);
  const processedParams = useRef(false);

  // Memoized render item to prevent unnecessary re-renders
  const renderCartItem = useCallback(({ item: cart }: { item: CartData }) => {
    const isExpanded = expandedCart === cart.customerId;
    return (
      <CartItemComponent
        cart={cart}
        isExpanded={isExpanded}
        onToggleExpand={() => handleToggleExpand(cart.customerId)}
        onCheckout={() => handleCheckout(cart)}
        onContinueShopping={() => handleContinueShopping(cart)}
        onRemove={() => handleRemoveCart(cart)}
        styles={styles}
      />
    );
  }, [expandedCart, handleToggleExpand, handleCheckout, handleContinueShopping, handleRemoveCart, styles]);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  // Register scroll function globally for tab bar
  useEffect(() => {
    (global as any).cartsScreenScrollToTop = scrollToTop;
    return () => {
      (global as any).cartsScreenScrollToTop = null;
    };
  }, [scrollToTop]);

  useEffect(() => {
    if (!processedParams.current && validateCartParams(params)) {
      processedParams.current = true;
      handleNewCartItems(params).then(() => {
        // Force refresh after handling new cart items to ensure consistency
        forceRefreshCarts();
      });
    }
  }, [handleNewCartItems, params, forceRefreshCarts]);

  useEffect(() => {
    loadAllCarts();
  }, [loadAllCarts]);

  // 🚀 PERFORMANCE FIX: Only refresh when actually needed, not on every focus
  useFocusEffect(
    useCallback(() => {
      // Reset the processed params flag when screen is focused
      processedParams.current = false;

      // Only refresh if we have params indicating new cart data
      // This prevents unnecessary reloading when just navigating between tabs
      if (validateCartParams(params)) {
        forceRefreshCarts();
      }
    }, [forceRefreshCarts, params])
  );

  return (
    <View style={[
        styles.container,
        {
          paddingTop: insets.top,
          paddingBottom: 0 // Remove bottom padding to avoid greyed-out area
        }
      ]}>
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Header with integrated search - using the enhanced PageHeader */}
      <PageHeader
        title={SCREEN_TITLES.CART.MAIN}
        features={{
          search: searchProps,
          backButton: {
            onPress: handleBackPress
          }
        }}
      />

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading carts...</Text>
        </View>
      ) : carts.length === 0 ? (
        <CartEmptyState
          onGoToCustomers={handleGoToCustomers}
          styles={styles}
        />
      ) : filteredCarts.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No carts found</Text>
          <Text style={styles.emptySubtext}>Try adjusting your search to find carts</Text>
        </View>
      ) : (
        <View style={[styles.listContainer, styles.flashListContainer]}>
          <FlatList
            ref={flatListRef}
            data={filteredCarts}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.customerId}
            extraData={expandedCart}
            contentContainerStyle={{
              padding: 4,
              paddingBottom: TabBarUtils.getContentPadding(insets, 80)
            }}
            showsVerticalScrollIndicator={false}
            onScrollBeginDrag={() => Keyboard.dismiss()}

            // Optimized FlatList settings for smooth scrolling
            removeClippedSubviews={false} // Disable for dynamic heights
            initialNumToRender={6} // Reduced for better initial performance
            maxToRenderPerBatch={3} // Smaller batches for smoother scrolling
            updateCellsBatchingPeriod={100} // Increased for less frequent updates
            windowSize={8} // Reduced window size
            scrollEventThrottle={16} // 60fps for smooth scrolling

            // Additional optimizations for smooth scrolling
            maintainVisibleContentPosition={{
              minIndexForVisible: 0,
              autoscrollToTopThreshold: 10
            }}

            // Remove getItemLayout since cart items have dynamic heights (expanded/collapsed)
            // This was causing the jumping/flickering issue
          />
        </View>
      )}
    </View>
  );
};

// Export is at the function declaration
