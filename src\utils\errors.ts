/**
 * Error Handling Utilities
 * 
 * Consolidated error handling for API and database operations.
 * Combines functionality from apiErrorHandler.ts and databaseErrorHandler.ts
 */

// ===== API Error Handling =====

export enum ErrorType {
  NETWORK = 'network',
  TIMEOUT = 'timeout',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  SESSION_EXPIRED = 'session_expired',
  GENERIC = 'generic'
}

export interface ErrorResponse {
  type: ErrorType;
  originalError?: any;
}

/**
 * Higher-order function for API error handling
 */
export const withErrorHandling = async <T>(fn: () => Promise<T>): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    throw parseApiError(error);
  }
};

/**
 * Parse API errors and categorize them by type
 */
export const parseApiError = (error: any): ErrorResponse => {
  const defaultResponse: ErrorResponse = {
    type: ErrorType.GENERIC,
    originalError: error
  };

  if (!error) return defaultResponse;

  if (!error.response || error.message?.includes('Network Error')) {
    return {
      type: ErrorType.NETWORK,
      originalError: error
    };
  }

  if (error.message?.toLowerCase().includes('timeout')) {
    return {
      type: ErrorType.TIMEOUT,
      originalError: error
    };
  }

  if (error.response?.status) {
    const status = error.response.status;

    if (status === 401) {
      return {
        type: ErrorType.AUTHENTICATION,
        originalError: error
      };
    }

    if (status === 403) {
      const responseData = error.response.data as any;

      if (responseData && responseData.session_expired === 1) {
        return {
          type: ErrorType.SESSION_EXPIRED,
          originalError: error
        };
      } else {
        return {
          type: ErrorType.PERMISSION,
          originalError: error
        };
      }
    }
  }

  return defaultResponse;
};

// ===== Database Error Handling =====

/**
 * Handle database errors with simple logging and recovery options
 */
export const handleDatabaseError = (
  error: any,
  options?: {
    silent?: boolean;
    context?: string;
    onRecoveryNeeded?: () => Promise<boolean>;
  }
): Error => {
  const { context, onRecoveryNeeded } = options || {};

  if (onRecoveryNeeded) {
    setTimeout(() => {
      onRecoveryNeeded().catch(recoveryError => {
        // Silent fail
      });
    }, 0);
  }

  // Return the original error for chaining
  return error instanceof Error ? error : new Error(String(error));
};

/**
 * Check if error is a database-related error
 */
export const isDatabaseError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error.message || error.toString();
  const databaseErrorKeywords = [
    'database',
    'sqlite',
    'sql',
    'constraint',
    'foreign key',
    'unique',
    'not null'
  ];
  
  return databaseErrorKeywords.some(keyword => 
    errorMessage.toLowerCase().includes(keyword)
  );
};

/**
 * Check if error is a network-related error
 */
export const isNetworkError = (error: any): boolean => {
  if (!error) return false;
  
  return (
    !error.response ||
    error.message?.includes('Network Error') ||
    error.code === 'NETWORK_ERROR'
  );
};

/**
 * Get user-friendly error message based on error type
 */
export const getUserFriendlyErrorMessage = (error: ErrorResponse | any): string => {
  if (error && typeof error === 'object' && 'type' in error) {
    const errorResponse = error as ErrorResponse;

    switch (errorResponse.type) {
      case ErrorType.NETWORK:
        return 'Unable to connect. Please check your internet connection.';
      case ErrorType.TIMEOUT:
        return 'The server is taking too long to respond. Please try again later.';
      case ErrorType.AUTHENTICATION:
        return 'Invalid username or password. Please try again.';
      case ErrorType.PERMISSION:
        return 'You don\'t have permission to access this feature.';
      case ErrorType.SESSION_EXPIRED:
        return 'Your session has expired. Please sign in again.';
      default:
        return 'Something went wrong. Please try again.';
    }
  }

  // Handle Error instances
  if (error instanceof Error) {
    if (error.message.includes('Network') || error.message.includes('connect')) {
      return 'Connection problem. Please check your internet.';
    }
    return error.message;
  }

  // Handle string errors
  if (typeof error === 'string') return error;

  // Fallback for other error types
  if (isDatabaseError(error)) {
    return 'Database error occurred. Please try again.';
  }

  if (isNetworkError(error)) {
    return 'Unable to connect. Please check your internet connection.';
  }

  return 'An unexpected error occurred. Please try again.';
};
