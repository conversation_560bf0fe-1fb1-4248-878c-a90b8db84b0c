import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const addressModalStyles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderTopLeftRadius: MD.SPACING.LARGE,
    borderTopRightRadius: MD.SPACING.LARGE,
    maxHeight: '70%',
    minHeight: '40%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerTextContainer: {
    marginLeft: MD.SPACING.SMALL,
    flex: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  customerName: {
    fontSize: 14,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: 2,
  },
  closeButton: {
    padding: MD.SPACING.XSMALL,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    paddingBottom: MD.SPACING.LARGE,
  },
  addressItem: {
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  addressContent: {
    flex: 1,
  },
  addressDetails: {
    flex: 1,
  },
  addressLine: {
    fontSize: 14,
    color: NEUTRAL.TEXT_PRIMARY,
    lineHeight: 20,
    marginBottom: MD.SPACING.XSMALL / 2,
  },
  addressCity: {
    fontSize: 14,
    color: BRAND.PRIMARY,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyText: {
    fontSize: 16,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: MD.SPACING.MEDIUM,
  },
});
