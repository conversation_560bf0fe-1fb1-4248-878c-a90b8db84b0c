import React from 'react';
import { View, Text } from 'react-native';
import { CartItem } from '../types/business';
import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import { getCartItemFinancialValues } from '../orchestrators/cart';
import { formatCartItemRate, formatCartItemAmount, getCartItemAmountColor } from '../utils/formatting';

interface CartItemRendererProps {
  item: CartItem;
  index: number;
  styles: any;
}

/**
 * Simple cart item renderer
 */
const CartItemRenderer: React.FC<CartItemRendererProps> = ({ item, index, styles }) => {
  // Simple calculations
  const { rate, amount } = getCartItemFinancialValues(item);
  const itemValues = {
    rateText: formatCartItemRate(rate),
    amountText: formatCartItemAmount(rate, item.quantity),
    amountColor: getCartItemAmountColor(rate, item.quantity, { SEMANTIC, NEUTRAL }),
    qtyText: `${item.quantity} ${item.selectedUom}`
  };

  return (
    <View style={styles.itemContainer}>
      {/* Header with index and item name - inspired by transaction items */}
      <View style={styles.header}>
        <View style={styles.indexContainer}>
          <Text style={styles.index}>{index + 1}</Text>
        </View>
        <View style={styles.nameContainer}>
          <Text style={styles.itemName}>{item.item_name}</Text>
        </View>
      </View>

      {/* Details row with proper 3-column layout like checkout */}
      <View style={styles.detailsRow}>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Qty</Text>
          <Text style={styles.detailValue}>{itemValues.qtyText}</Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Rate</Text>
          <Text style={[styles.detailValue, { color: BRAND.PRIMARY }]}>
            {itemValues.rateText}
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Text style={styles.detailLabel}>Amount</Text>
          <Text style={[styles.detailValue, { color: itemValues.amountColor }]}>
            {itemValues.amountText}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default CartItemRenderer;
