import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { validateSessionOnAppStateChange } from './session';
import { AppLifecycleState } from '../types/initialization';
import useErrorHandler from '../hooks/useErrorHandler';
import { quantitySyncManager } from '../utils/quantitySync';

export const useAppLifecycle = () => {
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);
  const { handleError, handleSessionExpired } = useErrorHandler();

  useEffect(() => {
    // Initialize quantity sync manager when app starts
    quantitySyncManager.initialize();

    const subscription = AppState.addEventListener('change', async (nextAppState) => {
      if ((appStateRef.current === 'background' || appStateRef.current === 'inactive') && nextAppState === 'active') {
        try {
          const sessionResult = await validateSessionOnAppStateChange();

          if (sessionResult.error) {
            if (sessionResult.shouldRedirectToLogin) {
              handleSessionExpired();
              return;
            }
            handleError(sessionResult.error, { context: 'App State Change' });
          }
        } catch (error) {
          handleError(error, { context: 'App State Change' });
        }
      }
      appStateRef.current = nextAppState;
    });

    return () => {
      subscription.remove();
      // Cleanup quantity sync when component unmounts
      quantitySyncManager.cleanup();
    };
  }, [handleError, handleSessionExpired]);

  return { currentState: appStateRef.current };
};

export const getAppLifecycleState = (): AppLifecycleState => ({
  currentState: AppState.currentState as 'active' | 'background' | 'inactive',
  previousState: null,
  isFirstLaunch: true
});
