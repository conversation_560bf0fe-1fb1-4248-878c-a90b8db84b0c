import { useState, useCallback, useEffect, useRef } from 'react';
import { Alert, Keyboard, TextInput } from 'react-native';

/**
 * Base options for the useSearchFilter hook
 */
export interface BaseSearchFilterOptions {
  /** Initial search query */
  initialQuery?: string;
  /** Search placeholder */
  placeholder?: string;
  /** Search debounce time */
  debounceTime?: number;
  /** Initial selected filters */
  initialFilters?: string[];
  /** Callback when filtered data changes */
  onFilteredDataChange?: (filteredData: any[]) => void;
}

/**
 * Options for local search and filter
 */
export interface LocalSearchFilterOptions<T> extends BaseSearchFilterOptions {
  /** Data to search in */
  data: T[];
  /** Fields to search in */
  searchFields?: (keyof T)[];
  /** Function to format search values */
  searchFormatter?: (item: T, field: keyof T) => string;
  /** Filter functions */
  filterFunctions?: {
    [key: string]: (item: T, filterValue: string) => boolean;
  };
  /** Mode flag to indicate this is local mode */
  mode: 'local';
}

/**
 * Options for API-based search and filter
 */
export interface ApiSearchFilterOptions<T> extends BaseSearchFilterOptions {
  /** Function to fetch data from API */
  fetchFunction: (params: any) => Promise<{ data: T[], total?: number }>;
  /** Field to search in */
  searchField?: string;
  /** Search operator to use */
  searchOperator?: string;
  /** Page size */
  pageSize?: number;
  /** Function to get default filters */
  getDefaultFilters?: () => any[];
  /** Mode flag to indicate this is API mode */
  mode: 'api';
}

/**
 * Combined options type for the useSearchFilter hook
 */
export type SearchFilterOptions<T> = LocalSearchFilterOptions<T> | ApiSearchFilterOptions<T>;

/**
 * Unified hook for search and filter functionality
 *
 * This hook provides a consistent interface for both local and API-based search and filter.
 *
 * @param options Configuration options
 * @returns Object with search and filter state and handlers
 */
export function useSearchFilter<T>(options: SearchFilterOptions<T>) {
  // Extract common options
  const {
    onFilteredDataChange,
    mode,
    initialQuery = '',
    placeholder = 'Search',
    initialFilters = [],
  } = options;

  // State
  const [filteredData, setFilteredData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Search state
  const [searchQuery, setSearchQueryInternal] = useState(initialQuery);
  const [isSearchActive, setIsSearchActive] = useState(!!initialQuery);

  // Filter state
  const [selectedFilters, setSelectedFilters] = useState<string[]>(initialFilters);

  // Refs to track state
  const isProcessingRef = useRef(false);
  const isInitialRender = useRef(true);
  const searchInputRef = useRef<TextInput>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Store data in a ref to avoid recreating it on every render
  const dataRef = useRef<T[]>(mode === 'local' ? (options as LocalSearchFilterOptions<T>).data : []);

  // Extract search fields and formatter once to avoid recreating them on every render
  const searchFieldsRef = useRef<(keyof T)[]>(
    mode === 'local' ? (options as LocalSearchFilterOptions<T>).searchFields || [] : []
  );

  const searchFormatterRef = useRef<(item: T, field: keyof T) => string>(
    mode === 'local'
      ? (options as LocalSearchFilterOptions<T>).searchFormatter || ((item, field) => String(item[field] || ''))
      : ((item, field) => String(item[field] || ''))
  );

  const searchCache = useRef<Map<string, T[]>>(new Map());
  const searchMetrics = useRef<Map<string, number>>(new Map());

  const searchDataLocally = useCallback((query: string, dataToSearch: T[]) => {
    if (mode !== 'local' || !query?.trim() || !dataToSearch?.length) {
      return dataToSearch || [];
    }

    const normalizedQuery = query.trim().toLowerCase();

    const cacheKey = `${normalizedQuery}_${dataToSearch.length}_${dataToSearch[0] ? JSON.stringify(dataToSearch[0]).slice(0, 50) : ''}`;

    const cachedResult = searchCache.current.get(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }

    const startTime = performance.now();

    const searchFields = searchFieldsRef.current;
    const searchFormatter = searchFormatterRef.current;

    if (!searchFields?.length || !searchFormatter) {
      return dataToSearch;
    }

    const results = dataToSearch.filter(item => {
      if (!item) return false;

      for (const field of searchFields) {
        try {
          const formattedValue = searchFormatter(item, field).toLowerCase();
          if (formattedValue.includes(normalizedQuery)) {
            return true;
          }
        } catch {
          // Continue to next field on error
        }
      }
      return false;
    });

    const searchTime = performance.now() - startTime;
    searchMetrics.current.set(normalizedQuery, searchTime);

    if (searchTime > 50) {
      if (__DEV__) console.warn(`Slow search: "${normalizedQuery}" took ${searchTime.toFixed(2)}ms, found ${results.length} results`);
    }

    if (searchCache.current.size > 100) {
      const entries = Array.from(searchCache.current.entries());
      const toRemove = entries.slice(0, 20);
      toRemove.forEach(([key]) => searchCache.current.delete(key));
    }

    searchCache.current.set(cacheKey, results);

    return results;
  }, [mode]);

  // Extract filter functions and update when they change
  const filterFunctionsRef = useRef<{[key: string]: (item: T, filterValue: string) => boolean}>({});

  // Update filter functions ref when they change
  useEffect(() => {
    if (mode === 'local') {
      const newFilterFunctions = (options as LocalSearchFilterOptions<T>).filterFunctions || {};
      filterFunctionsRef.current = newFilterFunctions;

      // Filter functions ready - no logging needed
    }
  }, [mode, (options as LocalSearchFilterOptions<T>).filterFunctions]);

  // Simplified filter function for local data
  const filterDataLocally = useCallback((dataToFilter: T[]) => {
    if (mode !== 'local' || !selectedFilters.length || !dataToFilter?.length) {
      return dataToFilter || [];
    }

    const filterFunctions = filterFunctionsRef.current;

    // Parse filters into type-value pairs
    const filtersByType: Record<string, string[]> = {};

    selectedFilters.forEach((filterId: string) => {
      const [filterType, filterValue] = filterId.includes(':')
        ? filterId.split(':')
        : filterId.includes('_')
        ? [filterId.substring(0, filterId.indexOf('_')), filterId.substring(filterId.indexOf('_') + 1)]
        : [filterId, ''];

      if (filterType && filterValue) {
        if (!filtersByType[filterType]) {
          filtersByType[filterType] = [];
        }
        filtersByType[filterType].push(filterValue);
      }
    });

    const filteredResults = dataToFilter.filter(item => {
      if (!item) return false;

      // For each filter type, the item must match at least one filter value within that type
      // But the item must satisfy ALL filter types that are selected
      return Object.entries(filtersByType).every(([filterType, filterValues]) => {
        // Within each filter type, the item must match at least one of the values (OR logic)
        const matches = filterValues.some(filterValue => {
          if (filterFunctions?.[filterType]) {
            try {
              const result = filterFunctions[filterType](item, filterValue);
              return result;
            } catch (error) {
              // 🚀 CRITICAL: Log filter function errors
              if (__DEV__) console.error(`Filter function error for ${filterType}:`, error);
              return false;
            }
          }

          // Fallback to direct field comparison
          const fieldValue = String(item[filterType as keyof T] || '');
          const result = fieldValue === filterValue;
          return result;
        });
        return matches;
      });
    });

    // Filter applied - no logging needed for normal operation

    return filteredResults;
  }, [selectedFilters, mode]);

  // Extract getDefaultFilters once to avoid recreating it on every render
  const getDefaultFiltersRef = useRef<() => any[]>(
    mode === 'api' ? (options as ApiSearchFilterOptions<T>).getDefaultFilters || (() => []) : (() => [])
  );

  // Simplified API filter builder
  const buildApiFilters = useCallback(() => {
    if (mode !== 'api') return [];

    const getDefaultFilters = getDefaultFiltersRef.current;
    const apiFilters = getDefaultFilters ? getDefaultFilters() : [];

    // Group filters by type
    const filtersByType: Record<string, string[]> = {};

    selectedFilters.forEach((filterId: string) => {
      const [filterType, filterValue] = filterId.includes(':')
        ? filterId.split(':')
        : filterId.includes('_')
        ? [filterId.substring(0, filterId.indexOf('_')), filterId.substring(filterId.indexOf('_') + 1)]
        : [filterId, ''];

      if (!filtersByType[filterType]) {
        filtersByType[filterType] = [];
      }
      filtersByType[filterType].push(filterValue);
    });

    // Convert to API filters
    Object.entries(filtersByType).forEach(([filterType, filterValues]) => {
      if (filterType === 'status' || filterType === 'territory' || filterType === 'group') {
        const fieldName = filterType === 'territory' ? 'territory' :
                         filterType === 'group' ? 'customer_group' : 'status';
        apiFilters.push([fieldName, 'in', filterValues]);
      } else {
        const operator = filterValues.length === 1 ? '=' : 'in';
        const value = filterValues.length === 1 ? filterValues[0] : filterValues;
        apiFilters.push([filterType, operator, value]);
      }
    });

    return apiFilters;
  }, [selectedFilters, mode]);

  // Extract API options once to avoid recreating them on every render
  const fetchFunctionRef = useRef<(params: any) => Promise<{ data: T[], total?: number }>>(
    mode === 'api' ? (options as ApiSearchFilterOptions<T>).fetchFunction : null
  );

  const searchFieldRef = useRef<string>(
    mode === 'api' ? (options as ApiSearchFilterOptions<T>).searchField || 'name' : 'name'
  );

  const searchOperatorRef = useRef<string>(
    mode === 'api' ? (options as ApiSearchFilterOptions<T>).searchOperator || 'like' : 'like'
  );

  const pageSizeRef = useRef<number>(
    mode === 'api' ? (options as ApiSearchFilterOptions<T>).pageSize || 20 : 20
  );

  // Search query setter with debounce
  const setSearchQuery = useCallback((query: string) => {
    const normalizedQuery = query || '';
    setSearchQueryInternal(normalizedQuery);
    setIsSearchActive(!!normalizedQuery.trim());

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  }, []);

  // Clear search function
  const clearSearch = useCallback(() => {
    // Batch state updates for better performance
    setSearchQueryInternal('');
    setIsSearchActive(false);

    // Defer keyboard dismiss to avoid blocking UI
    requestAnimationFrame(() => {
      Keyboard.dismiss();
    });
  }, []);

  // Filter handlers
  const handleApplyFilters = useCallback((filterIds: string[]) => {
    setSelectedFilters(filterIds);
  }, []);

  const handleRemoveFilter = useCallback((filterId: string) => {
    setSelectedFilters(prev => prev.filter(id => id !== filterId));
  }, []);

  const handleClearAll = useCallback(() => {
    setSelectedFilters([]);
    // Also clear search when clearing all filters
    setSearchQuery('');
    setIsSearchActive(false);
  }, []);

  // Simplified local search and filter application
  const applyLocalSearchFilter = useCallback(() => {
    if (mode !== 'local') return;

    isProcessingRef.current = true;

    try {
      if (!dataRef.current?.length) {
        setFilteredData([]);
        onFilteredDataChange?.([]);
        return;
      }

      const hasSearch = searchQuery?.trim();
      const hasFilters = selectedFilters.length > 0;

      // Fast path: No search or filters - return original data without copying
      if (!hasSearch && !hasFilters) {
        setFilteredData(dataRef.current);
        onFilteredDataChange?.(dataRef.current);
        return;
      }

      // Only copy array when we need to modify it
      let results = dataRef.current;

      // Apply search
      if (hasSearch) {
        results = searchDataLocally(searchQuery, results);
      }

      // Apply filters
      if (hasFilters) {
        results = filterDataLocally(results);
      }

      setFilteredData(results);
      onFilteredDataChange?.(results);
    } catch (error) {
      // 🚀 CRITICAL: Log search and filter errors
      if (__DEV__) console.error('Error applying search and filter:', error);
    } finally {
      isProcessingRef.current = false;
    }
  }, [mode, searchQuery, selectedFilters, searchDataLocally, filterDataLocally, onFilteredDataChange]);

  // Track the last API call to prevent duplicates
  const lastApiCallRef = useRef({ page: 0, filters: '', isLoadingMore: false, timestamp: 0 });

  // Simplified API search function
  const performApiSearch = useCallback((page = 1, isLoadingMore = false, forceRefresh = false) => {
    if (mode !== 'api') return;

    if (isProcessingRef.current && !isLoadingMore && !forceRefresh) {
      return;
    }

    const fetchFunction = fetchFunctionRef.current;
    const searchField = searchFieldRef.current;
    const searchOperator = searchOperatorRef.current;
    const pageSize = pageSizeRef.current;

    if (!fetchFunction) {
      // 🚀 CRITICAL: Log missing fetch function
      if (__DEV__) console.error('No fetch function provided');
      return;
    }

    const apiFilters = buildApiFilters();

    if (searchQuery.trim()) {
      apiFilters.push([searchField, searchOperator, `%${searchQuery.trim()}%`]);
    }

    const filtersString = JSON.stringify(apiFilters);
    const currentTimestamp = Date.now();

    // Prevent duplicate calls
    const lastCall = lastApiCallRef.current;
    if (!forceRefresh && !isLoadingMore && page === lastCall.page &&
        filtersString === lastCall.filters && (currentTimestamp - lastCall.timestamp) < 1000) {
      return;
    }

    lastApiCallRef.current = { page, filters: filtersString, isLoadingMore, timestamp: currentTimestamp };

    if (!isLoadingMore) {
      setLoading(true);
    }
    isProcessingRef.current = true;

    fetchFunction({
      page,
      pageSize,
      filters: apiFilters
    }).then(result => {
      if (!result?.data || !Array.isArray(result.data)) {
        throw new Error('Invalid results received from server');
      }

      if (isLoadingMore) {
        setFilteredData(prevData => {
          const prevArray = prevData || [];
          const existingIds = new Set(prevArray.map((item: any) => item.name || item.id));
          const newItems = result.data.filter((item: any) => !existingIds.has(item.name || item.id));
          return [...prevArray, ...newItems];
        });
        setHasMore(result.data.length === pageSize);
      } else {
        setFilteredData(result.data);
        setHasMore(result.data.length === pageSize);
        if (onFilteredDataChange) {
          onFilteredDataChange(result.data);
        }
      }
    }).catch((error) => {
      // 🚀 CRITICAL: Log API search errors
      if (__DEV__) console.error('API search error:', error);
      Alert.alert('Error', 'Search failed. Please check your connection and try again.');
    }).finally(() => {
      setLoading(false);
      isProcessingRef.current = false;
    });
  }, [mode, searchQuery, buildApiFilters, onFilteredDataChange]);

  // Simplified data update effect for local mode
  useEffect(() => {
    if (mode !== 'local' || isProcessingRef.current) return;

    const localData = mode === 'local' ? (options as LocalSearchFilterOptions<T>).data : [];

    if (!localData?.length) {
      setFilteredData([]);
      onFilteredDataChange?.([]);
      return;
    }

    dataRef.current = localData;

    // Always apply current search and filters when data changes - IMMEDIATELY
    if (selectedFilters.length > 0 || searchQuery?.trim()) {
      applyLocalSearchFilter();
    } else {
      setFilteredData(localData);
      onFilteredDataChange?.(localData);
    }
  }, [mode, (options as LocalSearchFilterOptions<T>).data, selectedFilters, searchQuery, applyLocalSearchFilter, onFilteredDataChange]);

  // Simplified initialization
  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;

      if (mode === 'local' && dataRef.current) {
        if (selectedFilters.length > 0 || searchQuery?.trim()) {
          applyLocalSearchFilter();
        } else {
          setFilteredData(dataRef.current);
          onFilteredDataChange?.(dataRef.current);
        }
      } else if (mode === 'api' && (selectedFilters.length > 0 || searchQuery?.trim())) {
        performApiSearch();
      }
    }
  }, []);

  // Simplified search/filter change handler
  const lastSearchRef = useRef({ query: '', filters: [] as string[] });

  useEffect(() => {
    if (isInitialRender.current || isProcessingRef.current) return;

    const currentQuery = searchQuery.trim();
    const lastSearch = lastSearchRef.current;

    const queryChanged = currentQuery !== lastSearch.query;
    const filtersChanged = selectedFilters.length !== lastSearch.filters.length ||
                          selectedFilters.some((filter, index) => filter !== lastSearch.filters[index]);

    if (!queryChanged && !filtersChanged) return;

    // Update last search reference efficiently
    lastSearchRef.current = {
      query: currentQuery,
      filters: selectedFilters // Use reference instead of copy for comparison
    };

    // For local mode, apply filters immediately without any delay
    if (mode === 'local') {
      applyLocalSearchFilter();
    } else {
      // For API mode, use a small delay
      const timeoutId = setTimeout(() => {
        performApiSearch(1, false);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [mode, searchQuery, selectedFilters, applyLocalSearchFilter, performApiSearch]);

  // Simplified handlers
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleClearSearch = useCallback(() => {
    // Optimize: Check filters length without copying array
    const hasFilters = selectedFilters.length > 0;

    clearSearch();

    // Fast path: If no filters and local mode, immediately restore full data
    if (mode === 'local' && !hasFilters && dataRef.current) {
      // Use requestAnimationFrame to defer the update and improve perceived performance
      requestAnimationFrame(() => {
        setFilteredData(dataRef.current);
        onFilteredDataChange?.(dataRef.current);
      });
    }
  }, [clearSearch, selectedFilters.length, mode, onFilteredDataChange]);

  const applySearchAndFilters = useCallback((query: string, filterIds: string[], page = 1) => {
    isProcessingRef.current = true;

    setSearchQuery(query || '');
    handleApplyFilters(Array.isArray(filterIds) ? filterIds : []);

    if (mode === 'local') {
      applyLocalSearchFilter();
      isProcessingRef.current = false;
    } else {
      setTimeout(() => {
        performApiSearch(page);
        isProcessingRef.current = false;
      }, 50);
    }
  }, [mode]);

  // Simplified search props
  const searchProps = {
    value: searchQuery,
    onChangeText: handleSearchChange,
    onClear: handleClearSearch,
    placeholder,
    ref: searchInputRef,
    handleApplyFilters,
    handleRemoveFilter,
    handleClearAll,
    selectedFilters,
    applySearchAndFilters
  };

  // Simplified load more function
  const loadMore = useCallback((page: number) => {
    if (mode === 'api' && !loading && hasMore) {
      performApiSearch(page, true, false);
    }
  }, [mode, performApiSearch, loading, hasMore]);

  return {
    // State
    filteredData,
    searchQuery,
    isSearchActive,
    selectedFilters,
    loading,
    hasMore,

    // Handlers
    setSearchQuery: handleSearchChange,
    clearSearch: handleClearSearch,
    handleApplyFilters,
    handleRemoveFilter,
    handleClearAll,
    applySearchAndFilters,

    // API-specific functions
    performSearch: mode === 'api' ? performApiSearch : undefined,
    loadMore: mode === 'api' ? loadMore : undefined,
    refresh: mode === 'api' ? ((page = 1) => performApiSearch(page, false, true)) : undefined,

    // Props for components
    searchProps
  };
}
