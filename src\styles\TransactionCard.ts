import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const transactionCardStyles = StyleSheet.create({
  listItem: {
    backgroundColor: NEUTRAL.WHITE,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  cardContent: {
    width: '100%',
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.XXSMALL,
  },
  idContainer: {
    flex: 1,
  },
  id: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  date: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize + 1,
    color: NEUTRAL.TEXT_SECONDARY,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
  },
  customer: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize + 1,
    color: NEUTRAL.TEXT_PRIMARY,
    fontWeight: '400',
    marginBottom: MD.SPACING.XSMALL,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  statusAmountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  statusChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.XSMALL + 2,
    paddingVertical: MD.SPACING.XSMALL / 2,
    borderRadius: MD.BORDER_RADIUS.LARGE - 4,
    height: 22,
  },
  statusIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginRight: MD.SPACING.XSMALL - 1,
  },
  statusText: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize + 1,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
  },
  amountContainer: {
    alignItems: 'flex-end',
  },
  amountRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  outstandingAmount: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: SEMANTIC.ERROR,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    marginRight: MD.SPACING.XSMALL,
  },
  separator: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '400',
    color: NEUTRAL.TEXT_SECONDARY,
    marginHorizontal: MD.SPACING.XSMALL,
  },
  amount: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
});
