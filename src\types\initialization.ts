export interface InitializationState {
  isInitializing: boolean;
  isInitialized: boolean;
  error: Error | null;
  redirectTo: string | null;
}

export interface SessionValidationResult {
  isValid: boolean;
  shouldRedirectToLogin: boolean;
  error?: Error;
}

export interface AppInitializationOptions {
  onError?: (error: Error, context: string) => void;
}

export interface AppLifecycleState {
  currentState: 'active' | 'background' | 'inactive';
  previousState: 'active' | 'background' | 'inactive' | null;
  isFirstLaunch: boolean;
}

export interface InitializationContext {
  error?: Error;
  retryCount?: number;
  maxRetries?: number;
}
