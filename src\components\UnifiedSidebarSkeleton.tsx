import React from 'react';
import { View } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import { unifiedSidebarSkeletonStyles } from '../styles/UnifiedSidebarSkeleton';

interface UnifiedSidebarSkeletonProps {
  shimmerEnabled?: boolean;
}

// Simple shimmer effect component
const ShimmerView: React.FC<{ style: any; enabled?: boolean }> = ({ style, enabled = true }) => (
  <View style={[style, { backgroundColor: enabled ? NEUTRAL.BACKGROUND_LIGHT : '#f0f0f0' }]} />
);

const UnifiedSidebarSkeleton: React.FC<UnifiedSidebarSkeletonProps> = ({ 
  shimmerEnabled = true 
}) => {
  return (
    <View style={unifiedSidebarSkeletonStyles.sidebarContainer}>
      {/* Toggle Header Skeleton */}
      <View style={unifiedSidebarSkeletonStyles.toggleContainer}>
        <ShimmerView style={unifiedSidebarSkeletonStyles.toggleButtonSkeleton} enabled={shimmerEnabled} />
        <ShimmerView style={unifiedSidebarSkeletonStyles.toggleButtonSkeleton} enabled={shimmerEnabled} />
      </View>

      {/* Scrollable Content Skeleton */}
      <View style={unifiedSidebarSkeletonStyles.sidebarContent}>
        {/* "All" tab skeleton */}
        <View style={unifiedSidebarSkeletonStyles.itemTabSkeleton}>
          <ShimmerView style={unifiedSidebarSkeletonStyles.itemTextSkeleton} enabled={shimmerEnabled} />
          <ShimmerView style={unifiedSidebarSkeletonStyles.itemCountSkeleton} enabled={shimmerEnabled} />
        </View>

        {/* Divider */}
        <View style={unifiedSidebarSkeletonStyles.itemDivider} />

        {/* Individual item skeletons */}
        {Array.from({ length: 6 }, (_, index) => (
          <React.Fragment key={index}>
            <View style={unifiedSidebarSkeletonStyles.itemTabSkeleton}>
              <ShimmerView style={unifiedSidebarSkeletonStyles.itemTextSkeleton} enabled={shimmerEnabled} />
              <ShimmerView style={unifiedSidebarSkeletonStyles.itemCountSkeleton} enabled={shimmerEnabled} />
            </View>
            {index < 5 && <View style={unifiedSidebarSkeletonStyles.itemDivider} />}
          </React.Fragment>
        ))}
      </View>
    </View>
  );
};

export default React.memo(UnifiedSidebarSkeleton);
