import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const customerScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
  },
  headerContainer: {
    backgroundColor: NEUTRAL.WHITE,
  },
  content: {
    flex: 1,
  },
  listContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: MD.SPACING.LARGE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XLARGE,
  },
  loadingText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: MD.SPACING.MEDIUM,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: MD.SPACING.MEDIUM,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  errorText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: MD.SPACING.MEDIUM,
  },
  footerLoader: {
    paddingVertical: MD.SPACING.MEDIUM,
    alignItems: 'center',
  },
});
