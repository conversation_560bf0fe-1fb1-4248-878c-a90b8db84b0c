import React from 'react';
import { View, Text } from 'react-native';
import { Avatar } from 'react-native-paper';
import { checkoutSuccessStateStyles } from '../styles/CheckoutSuccessState';

interface CheckoutSuccessStateProps {
  customerName: string;
  orderName?: string;
}

const CheckoutSuccessState: React.FC<CheckoutSuccessStateProps> = ({
  customerName,
  orderName
}) => {
  return (
    <View style={checkoutSuccessStateStyles.container}>
      <Avatar.Icon
        size={80}
        icon="check-circle"
        style={checkoutSuccessStateStyles.successIcon}
      />
      
      <Text style={checkoutSuccessStateStyles.successTitle}>
        Order Placed Successfully!
      </Text>
      
      <Text style={checkoutSuccessStateStyles.successSubtitle}>
        {orderName 
          ? `Order #${orderName} has been created for ${customerName}`
          : `Your order for ${customerName} has been placed`
        }
      </Text>
      
      <Text style={checkoutSuccessStateStyles.processingText}>
        Processing your request...
      </Text>
    </View>
  );
};

export default React.memo(CheckoutSuccessState);
