import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const checkoutCustomerInfoStyles = StyleSheet.create({
  container: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginHorizontal: MD.SPACING.SMALL,
    marginBottom: MD.SPACING.MEDIUM,
    padding: MD.SPACING.MEDIUM,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  customerName: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
    marginBottom: MD.SPACING.XSMALL,
  },
  customerDetails: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: 2,
  },
  contactRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: MD.SPACING.XSMALL,
  },
  financialRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: MD.SPACING.SMALL,
  },
  financialItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  icon: {
    marginRight: 6,
  },
  outstandingValue: {
    color: SEMANTIC.ERROR,
    fontWeight: '600',
  },
});
