import React, { memo, useMemo } from 'react';
import { View, Text } from 'react-native';
import { TransactionItem } from '../types/business';
import { formatCurrency } from '../utils/formatting';
import { transactionItemCardStyles } from '../styles/TransactionItemCard';

interface TransactionItemCardProps {
  item: TransactionItem;
  index: number;
}

const TransactionItemCardBase: React.FC<TransactionItemCardProps> = ({ item, index }) => {
  // Safety check for item
  if (!item) {
    return null;
  }

  // Memoized calculations for better performance
  const { formattedNetRate, formattedRate, formattedAmount, qtyText } = useMemo(() => {
    const netRate = formatCurrency(item.base_net_rate || 0).replace('UGX ', '');
    const rate = formatCurrency(item.rate).replace('UGX ', '');
    const amount = formatCurrency(item.amount).replace('UGX ', '');
    const qty = `${item.qty} ${item.uom}`;

    return {
      formattedNetRate: netRate,
      formattedRate: rate,
      formattedAmount: amount,
      qtyText: qty,
    };
  }, [item.base_net_rate, item.rate, item.amount, item.qty, item.uom]);

  return (
    <View style={transactionItemCardStyles.container}>
      <View style={transactionItemCardStyles.header}>
        <View style={transactionItemCardStyles.indexContainer}>
          <Text style={transactionItemCardStyles.index}>{item.idx || index + 1}</Text>
        </View>
        <View style={transactionItemCardStyles.nameContainer}>
          <Text style={transactionItemCardStyles.itemName}>{item.item_name}</Text>
        </View>
      </View>

      <View style={transactionItemCardStyles.detailsRow}>
        <View style={transactionItemCardStyles.detailItem}>
          <Text style={transactionItemCardStyles.detailLabel}>Qty</Text>
          <Text style={transactionItemCardStyles.detailValue}>{qtyText}</Text>
        </View>

        <View style={transactionItemCardStyles.detailItem}>
          <Text style={transactionItemCardStyles.detailLabel}>Net Rate</Text>
          <Text style={transactionItemCardStyles.detailValue}>{formattedNetRate}</Text>
        </View>

        <View style={transactionItemCardStyles.detailItem}>
          <Text style={transactionItemCardStyles.detailLabel}>Rate</Text>
          <Text style={transactionItemCardStyles.detailValue}>{formattedRate}</Text>
        </View>

        <View style={transactionItemCardStyles.detailItem}>
          <Text style={transactionItemCardStyles.detailLabel}>Amount</Text>
          <Text style={transactionItemCardStyles.detailValue}>{formattedAmount}</Text>
        </View>
      </View>

      <View style={transactionItemCardStyles.divider} />
    </View>
  );
};



// Memoize the component to prevent unnecessary re-renders
const TransactionItemCard = memo(TransactionItemCardBase, (prevProps, nextProps) => {
  // Safety checks for undefined items
  const prevItem = prevProps.item;
  const nextItem = nextProps.item;

  if (!prevItem || !nextItem) {
    return prevItem === nextItem;
  }

  // Only re-render if these specific properties change
  return (
    prevItem.name === nextItem.name &&
    prevItem.qty === nextItem.qty &&
    prevItem.rate === nextItem.rate &&
    prevItem.base_net_rate === nextItem.base_net_rate &&
    prevItem.amount === nextItem.amount
  );
});

export default TransactionItemCard;
