/**
 * Filter Configuration and Business Logic
 * 
 * Contains filter functions and configuration for customer filtering
 */

import { FilterGroup } from '../components/FilterModal';
import { Customer } from '../types/business';

export const CUSTOMER_FILTER_FUNCTIONS = {
  territory: (customer: Customer, value: string) => customer.territory === value,
  group: (customer: Customer, value: string) => customer.customer_group === value,
};

// Note: Customer search configuration moved to ui.ts as SEARCH.CUSTOMER_CONFIG
// Keeping this here for backward compatibility until migration is complete
export const CUSTOMER_SEARCH_CONFIG = {
  fields: ['customer_name', 'tax_id', 'mobile_no'] as (keyof Customer)[],
  placeholder: 'Name, TIN, Phone',
  debounceTime: 0,
  formatter: (customer: Customer, field: keyof Customer) => String(customer[field] || ''),
};

export const generateCustomerFilterGroups = (customers: Customer[]): FilterGroup[] => {
  const territories = [...new Set(customers.map(c => c.territory))].filter(Boolean).sort();
  const customerGroups = [...new Set(customers.map(c => c.customer_group))].filter(Boolean).sort();

  const filterGroups: FilterGroup[] = [];

  if (territories.length > 0) {
    filterGroups.push({
      id: 'territory',
      title: 'Territory',
      icon: 'map-marker-outline',
      options: territories.map(territory => ({
        id: `territory_${territory}`,
        label: territory
      }))
    });
  }

  if (customerGroups.length > 0) {
    filterGroups.push({
      id: 'group',
      title: 'Customer Group',
      icon: 'account-group-outline',
      options: customerGroups.map(group => ({
        id: `group_${group}`,
        label: group
      }))
    });
  }

  return filterGroups;
};

export const getActiveCustomerFilters = (selectedFilters: string[]) => {
  const filters = [];

  for (const id of selectedFilters) {
    if (id.startsWith('territory_')) {
      const territory = id.replace('territory_', '');
      filters.push({
        id,
        label: territory,
        icon: 'map-marker-outline',
        type: 'filter' as const
      });
    } else if (id.startsWith('group_')) {
      const group = id.replace('group_', '');
      filters.push({
        id,
        label: group,
        icon: 'account-group-outline',
        type: 'filter' as const
      });
    }
  }

  return filters;
};

export const getActiveCustomerSort = (selectedSort: string) => {
  if (!selectedSort) return null;

  const sortLabels: Record<string, { label: string; icon: string }> = {
    'name_asc': { label: 'Name: A to Z', icon: 'sort-alphabetical-ascending' },
    'name_desc': { label: 'Name: Z to A', icon: 'sort-alphabetical-descending' },
    'credit_limit_asc': { label: 'Credit: Low to High', icon: 'sort-numeric-ascending' },
    'credit_limit_desc': { label: 'Credit: High to Low', icon: 'sort-numeric-descending' },
    'outstanding_asc': { label: 'Outstanding: Low to High', icon: 'sort-numeric-ascending' },
    'outstanding_desc': { label: 'Outstanding: High to Low', icon: 'sort-numeric-descending' },
  };

  const sortInfo = sortLabels[selectedSort];
  if (!sortInfo) return null;

  return {
    id: selectedSort,
    label: sortInfo.label,
    icon: sortInfo.icon,
    type: 'sort' as const
  };
};

export const generateCustomerSortGroups = () => {
  return [
    {
      id: 'name',
      title: 'Name',
      icon: 'sort-alphabetical-variant',
      options: [
        { id: 'name_asc', label: 'A to Z' },
        { id: 'name_desc', label: 'Z to A' }
      ]
    },
    {
      id: 'credit',
      title: 'Credit Limit',
      icon: 'credit-card-outline',
      options: [
        { id: 'credit_limit_asc', label: 'Low to High' },
        { id: 'credit_limit_desc', label: 'High to Low' }
      ]
    },
    {
      id: 'outstanding',
      title: 'Outstanding',
      icon: 'cash-multiple',
      options: [
        { id: 'outstanding_asc', label: 'Low to High' },
        { id: 'outstanding_desc', label: 'High to Low' }
      ]
    }
  ];
};

export const sortCustomers = (customers: Customer[], sortId: string): Customer[] => {
  if (!sortId || sortId === 'none' || sortId === '') {
    return customers;
  }

  return [...customers].sort((a, b) => {
    switch (sortId) {
      case 'name_asc':
        return (a.customer_name || '').localeCompare(b.customer_name || '');
      case 'name_desc':
        return (b.customer_name || '').localeCompare(a.customer_name || '');
      case 'credit_limit_asc':
        return (a.custom_credit_limit || 0) - (b.custom_credit_limit || 0);
      case 'credit_limit_desc':
        return (b.custom_credit_limit || 0) - (a.custom_credit_limit || 0);
      case 'outstanding_asc':
        return (a.outstanding || 0) - (b.outstanding || 0);
      case 'outstanding_desc':
        return (b.outstanding || 0) - (a.outstanding || 0);
      default:
        return 0;
    }
  });
};
