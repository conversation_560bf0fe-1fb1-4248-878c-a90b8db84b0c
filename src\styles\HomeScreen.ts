import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const sharedStyles = StyleSheet.create({
  h1: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: MD.TYPOGRAPHY.H1.letterSpacing,
  },
  h2: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: MD.TYPOGRAPHY.H2.letterSpacing,
  },
});

export const homeStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.LARGE,
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  userSection: {
    flex: 1,
  },
  greeting: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: 2,
    fontWeight: '400',
  },
  name: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize,
    fontWeight: '700',
    color: NEUTRAL.TEXT_PRIMARY,
    letterSpacing: -0.5,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: MD.SPACING.SMALL,
  },
  iconButton: {
    padding: 4,
  },
  iconContainer: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: 22,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 3,
    shadowColor: BRAND.PRIMARY,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  scrollView: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XLARGE * 2,
    backgroundColor: NEUTRAL.BACKGROUND,
  },
  loadingText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: MD.SPACING.MEDIUM,
    fontWeight: '500',
  },
  sectionContainer: {
    padding: MD.SPACING.LARGE,
    paddingTop: MD.SPACING.XLARGE,
  },
  sectionTitleContainer: {
    marginBottom: MD.SPACING.XLARGE,
  },
  sectionTitle: {
    marginBottom: MD.SPACING.SMALL,
    fontWeight: '700',
  },
  titleDivider: {
    height: 3,
    backgroundColor: BRAND.PRIMARY,
    width: 50,
    borderRadius: 2,
  },
  dashboardContent: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.LARGE,
    padding: MD.SPACING.XLARGE * 1.5,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 240,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  placeholderIcon: {
    marginBottom: MD.SPACING.XLARGE,
    opacity: 0.7,
  },
  placeholderTitle: {
    marginBottom: MD.SPACING.LARGE,
    textAlign: 'center',
    fontWeight: '600',
  },
  placeholderText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    maxWidth: 280,
    fontWeight: '400',
  },
  quickActionsContainer: {
    flexDirection: 'row',
    gap: MD.SPACING.MEDIUM,
    marginBottom: MD.SPACING.XLARGE,
  },
  quickActionCard: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
    padding: MD.SPACING.LARGE,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  quickActionTitle: {
    fontSize: MD.TYPOGRAPHY.SUBTITLE2.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    marginTop: MD.SPACING.SMALL,
    textAlign: 'center',
  },
  quickActionSubtitle: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: 2,
    textAlign: 'center',
  },
});
