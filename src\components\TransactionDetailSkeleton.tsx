import React from 'react';
import { View, ScrollView } from 'react-native';
import { transactionDetailSkeletonStyles } from '../styles/TransactionDetailSkeleton';

const TransactionDetailSkeleton: React.FC = () => {
  return (
    <ScrollView style={transactionDetailSkeletonStyles.container}>
      <View style={transactionDetailSkeletonStyles.scrollViewContent}>
        {/* Header Section - matches actual transaction header */}
        <View style={transactionDetailSkeletonStyles.section}>
          <View style={transactionDetailSkeletonStyles.cardHeader}>
            <View style={transactionDetailSkeletonStyles.skeletonStatusChip} />
            <View style={transactionDetailSkeletonStyles.skeletonReorderButton} />
          </View>

          {/* Customer and other details - matches detailRow structure */}
          <View style={transactionDetailSkeletonStyles.detailRow}>
            <View style={transactionDetailSkeletonStyles.skeletonDetailLabel} />
            <View style={transactionDetailSkeletonStyles.skeletonDetailValue} />
          </View>
          <View style={transactionDetailSkeletonStyles.detailRow}>
            <View style={transactionDetailSkeletonStyles.skeletonDetailLabel} />
            <View style={transactionDetailSkeletonStyles.skeletonDetailValue} />
          </View>
          <View style={transactionDetailSkeletonStyles.detailRow}>
            <View style={transactionDetailSkeletonStyles.skeletonDetailLabel} />
            <View style={transactionDetailSkeletonStyles.skeletonDetailValue} />
          </View>
          <View style={transactionDetailSkeletonStyles.detailRow}>
            <View style={transactionDetailSkeletonStyles.skeletonDetailLabel} />
            <View style={transactionDetailSkeletonStyles.skeletonDetailValue} />
          </View>
        </View>

        {/* Summary Section */}
        <View style={transactionDetailSkeletonStyles.section}>
          <View style={transactionDetailSkeletonStyles.skeletonSectionTitle} />
          {Array.from({ length: 4 }, (_, index) => (
            <View key={index} style={transactionDetailSkeletonStyles.detailRow}>
              <View style={transactionDetailSkeletonStyles.skeletonDetailLabel} />
              <View style={transactionDetailSkeletonStyles.skeletonDetailValue} />
            </View>
          ))}
          <View style={transactionDetailSkeletonStyles.divider} />
          <View style={transactionDetailSkeletonStyles.totalRow}>
            <View style={transactionDetailSkeletonStyles.skeletonTotalLabel} />
            <View style={transactionDetailSkeletonStyles.skeletonTotalValue} />
          </View>
        </View>

        {/* Items Section */}
        <View style={transactionDetailSkeletonStyles.section}>
          <View style={transactionDetailSkeletonStyles.itemsHeader}>
            <View style={transactionDetailSkeletonStyles.skeletonSectionTitle} />
            <View style={transactionDetailSkeletonStyles.skeletonItemsCount} />
          </View>
          {Array.from({ length: 3 }, (_, index) => (
            <View key={index} style={transactionDetailSkeletonStyles.skeletonItemCard}>
              {/* Item header with index and name - matches TransactionItemCard header */}
              <View style={transactionDetailSkeletonStyles.skeletonItemHeader}>
                <View style={transactionDetailSkeletonStyles.skeletonItemIndex} />
                <View style={transactionDetailSkeletonStyles.skeletonItemName} />
              </View>

              {/* Item details row - matches TransactionItemCard detailsRow */}
              <View style={transactionDetailSkeletonStyles.skeletonItemDetails}>
                <View style={transactionDetailSkeletonStyles.skeletonItemDetail}>
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailLabel} />
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailValue} />
                </View>
                <View style={transactionDetailSkeletonStyles.skeletonItemDetail}>
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailLabel} />
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailValue} />
                </View>
                <View style={transactionDetailSkeletonStyles.skeletonItemDetail}>
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailLabel} />
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailValue} />
                </View>
                <View style={transactionDetailSkeletonStyles.skeletonItemDetail}>
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailLabel} />
                  <View style={transactionDetailSkeletonStyles.skeletonItemDetailValue} />
                </View>
              </View>

              {/* Divider - matches TransactionItemCard divider */}
              <View style={transactionDetailSkeletonStyles.skeletonItemDivider} />
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};

export default React.memo(TransactionDetailSkeleton);
