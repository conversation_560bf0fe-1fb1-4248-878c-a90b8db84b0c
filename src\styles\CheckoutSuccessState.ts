import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const checkoutSuccessStateStyles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.XLARGE,
    paddingVertical: MD.SPACING.XLARGE * 2,
    backgroundColor: NEUTRAL.WHITE,
  },
  successIcon: {
    backgroundColor: BRAND.PRIMARY, // Use theme primary color
    marginBottom: MD.SPACING.LARGE,
  },
  successTitle: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.MEDIUM,
    letterSpacing: MD.TYPOGRAPHY.H1.letterSpacing,
  },
  successSubtitle: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: MD.SPACING.LARGE,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  processingText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: BRAND.PRIMARY,
    textAlign: 'center',
    fontStyle: 'italic',
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
});
