import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const transactionEmptyStateStyles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyIcon: {
    marginBottom: MD.SPACING.LARGE,
  },
  emptyTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: MD.SPACING.LARGE,
  },
  emptySubText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 18,
    marginBottom: MD.SPACING.MEDIUM,
  },
  emptyButton: {
    marginTop: MD.SPACING.MEDIUM,
  },
  clearButton: {
    marginTop: MD.SPACING.LARGE,
    minWidth: 140,
  },
});
