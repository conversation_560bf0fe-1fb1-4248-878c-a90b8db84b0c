import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { View, Text, Modal, TouchableOpacity, ScrollView } from 'react-native';
import { IconButton, useTheme, Surface } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { NEUTRAL } from '../constants/colors';
import { filterModalStyles } from '../styles/FilterModal';

export interface FilterGroup {
  id: string;
  title: string;
  icon: string;
  options: FilterOption[];
}

export interface FilterOption {
  id: string;
  label: string;
}

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  filterGroups: FilterGroup[];
  selectedFilters: string[];
  onApplyFilters: (selectedFilters: string[]) => void;
  onClearFilters: () => void;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  filterGroups,
  selectedFilters,
  onApplyFilters,
  onClearFilters,
}) => {
  const theme = useTheme();
  const [localSelectedFilters, setLocalSelectedFilters] = useState<string[]>(selectedFilters);
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);


  const selectedFiltersSet = useMemo(() => new Set(localSelectedFilters), [localSelectedFilters]);

  // Memoize filter groups to prevent unnecessary re-renders
  const memoizedFilterGroups = useMemo(() => filterGroups, [filterGroups]);

  // Sync local state with props only when modal becomes visible
  useEffect(() => {
    if (visible) {
      setLocalSelectedFilters([...selectedFilters]); // Create a copy to avoid reference issues
      // Always set the first group as expanded when modal opens
      if (memoizedFilterGroups.length > 0) {
        setExpandedGroup(memoizedFilterGroups[0].id);
      }
    } else {
      // Reset expanded group when modal is closed
      setExpandedGroup(null);
    }
  }, [visible, selectedFilters, memoizedFilterGroups]); // Include selectedFilters in dependencies


  const toggleFilterSelection = useCallback((filterId: string) => {
    setLocalSelectedFilters(prev => {
      const filterSet = new Set(prev);
      if (filterSet.has(filterId)) {
        filterSet.delete(filterId);
      } else {
        filterSet.add(filterId);
      }
      return Array.from(filterSet);
    });
  }, []);

  const handleApplyFilters = useCallback(() => {
    // Always apply filters, even if they haven't changed
    // This ensures the UI updates properly
    onApplyFilters(localSelectedFilters);
    onClose();
  }, [localSelectedFilters, onApplyFilters, onClose]);

  const handleClearFilters = useCallback(() => {
    if (localSelectedFilters.length > 0) {
      setLocalSelectedFilters([]);
      onClearFilters();
    }
    onClose();
  }, [localSelectedFilters.length, onClearFilters, onClose]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={false}
      hardwareAccelerated={true}
    >
      <View style={filterModalStyles.modalContainer}>
        <TouchableOpacity
          style={filterModalStyles.backdropTouchable}
          activeOpacity={1}
          onPress={onClose}
        />
        <View style={filterModalStyles.modalContent}>
          <View style={filterModalStyles.modalHeader}>
            <Text style={filterModalStyles.modalTitle}>Filter By</Text>
            <IconButton
              icon="close"
              size={20}
              onPress={onClose}
              style={filterModalStyles.closeButton}
              iconColor={NEUTRAL.TEXT_PRIMARY}
            />
          </View>

          {filterGroups.length === 0 ? (
            <View style={filterModalStyles.noOptionsContainer}>
              <Text style={filterModalStyles.noOptionsText}>No filter options available</Text>
            </View>
          ) : (
            <>
              <View style={filterModalStyles.twoColumnContainer}>
                <View style={filterModalStyles.tabColumn}>
                  {filterGroups.length > 0 ? (
                    <View>
                      {filterGroups.map((group) => {
                        const hasSelected = group.options.some(option => localSelectedFilters.includes(option.id));
                        const isActive = expandedGroup === group.id;

                        return (
                          <TouchableOpacity
                            key={group.id}
                            style={[
                              filterModalStyles.tabItem,
                              isActive && filterModalStyles.activeTabItem,
                              hasSelected && filterModalStyles.tabItemWithSelection
                            ]}
                            onPress={() => setExpandedGroup(group.id)}
                          >
                            <Text
                              style={[
                                filterModalStyles.tabItemText,
                                isActive && filterModalStyles.activeTabItemText,
                                hasSelected && filterModalStyles.tabItemTextWithSelection
                              ]}
                            >
                              {group.title}
                            </Text>
                          </TouchableOpacity>
                        );
                      })}
                    </View>
                  ) : (
                    <View style={filterModalStyles.noOptionsContainer}>
                      <Text style={filterModalStyles.noOptionsText}>No filter groups</Text>
                    </View>
                  )}
                </View>


                <View style={filterModalStyles.optionsColumn}>
                  <ScrollView
                    style={filterModalStyles.optionsScrollContainer}
                    contentContainerStyle={filterModalStyles.optionsContainer}
                    showsVerticalScrollIndicator={false}
                    removeClippedSubviews={false}
                  >
                    {expandedGroup && (() => {
                      const activeGroup = filterGroups.find(g => g.id === expandedGroup);
                      if (!activeGroup) return null;

                      return activeGroup.options.map((option) => {
                        const isSelected = selectedFiltersSet.has(option.id);
                        const cardStyle = [
                          filterModalStyles.optionCard,
                          isSelected && filterModalStyles.selectedOptionCard
                        ];
                        const textStyle = [
                          filterModalStyles.optionText,
                          isSelected && filterModalStyles.selectedOptionText
                        ];
                        const iconName = isSelected ? "checkbox-marked" : "checkbox-blank-outline";
                        const iconColor = isSelected ? theme.colors.primary : NEUTRAL.TEXT_SECONDARY;

                        return (
                          <Surface
                            key={option.id}
                            style={cardStyle}
                            elevation={0}
                          >
                            <TouchableOpacity
                              onPress={() => toggleFilterSelection(option.id)}
                              style={filterModalStyles.optionTouchable}
                              activeOpacity={0.7}
                            >
                              <View style={filterModalStyles.optionContent}>
                                <View style={filterModalStyles.optionIconContainer}>
                                  <MaterialCommunityIcons
                                    name={iconName}
                                    size={20}
                                    color={iconColor}
                                  />
                                </View>
                                <View style={filterModalStyles.optionTextContainer}>
                                  <Text style={textStyle}>
                                    {option.label}
                                  </Text>
                                </View>
                              </View>
                            </TouchableOpacity>
                          </Surface>
                        );
                      });
                    })()}
                  </ScrollView>
                </View>
              </View>
            </>
          )}

          <View style={filterModalStyles.buttonsContainer}>
            <TouchableOpacity
              style={[
                filterModalStyles.clearButton,
                localSelectedFilters.length === 0 && filterModalStyles.disabledClearButton
              ]}
              onPress={handleClearFilters}
              disabled={localSelectedFilters.length === 0}
            >
              <Text style={[
                filterModalStyles.clearButtonText,
                localSelectedFilters.length === 0 && filterModalStyles.disabledClearButtonText
              ]}>
                Clear All
              </Text>
            </TouchableOpacity>


            <TouchableOpacity
              style={filterModalStyles.applyButton}
              onPress={handleApplyFilters}
            >
              <Text style={filterModalStyles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default memo(FilterModal);
