/**
 * Version Management Utilities
 * 
 * Renamed from versionManager.ts for better organization.
 * Simple version checking logic for cold start only.
 */

import { APP_INFO } from '../constants/config';
import { checkVersion } from '../services/versionService';
import { UpdateAction } from '../types/business';

/**
 * Compare version strings (semantic versioning)
 * Returns: -1 if v1 < v2, 0 if equal, 1 if v1 > v2
 */
const compareVersions = (v1: string, v2: string): number => {
  const parts1 = v1.split('.').map(Number);
  const parts2 = v2.split('.').map(Number);

  const maxLength = Math.max(parts1.length, parts2.length);

  for (let i = 0; i < maxLength; i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;

    if (part1 < part2) return -1;
    if (part1 > part2) return 1;
  }

  return 0;
};

/**
 * Check for updates silently in background
 * Simple logic - no caching, no complexity
 */
export const checkForUpdates = async (): Promise<UpdateAction> => {
  try {
    const versionInfo = await checkVersion();

    // If API call failed, return no action
    if (!versionInfo) {
      return {
        type: 'none',
        reason: 'Unable to check for updates',
        versionInfo: {
          current_version: APP_INFO.VERSION,
          download_url: '',
          forced_update: false,
          release_notes: [],
          file_size_mb: 0,
          release_date: new Date().toLocaleDateString('en-GB')
        }
      };
    }

    const currentVersion = APP_INFO.VERSION;
    const latestVersion = versionInfo.current_version;

    // Check if there's a newer version available
    if (compareVersions(currentVersion, latestVersion) < 0) {
      // Check if forced update is enabled
      if (versionInfo.forced_update) {
        return {
          type: 'forced',
          reason: 'A critical update is available. Please update to continue.',
          versionInfo
        };
      }

      // Optional update
      return {
        type: 'optional',
        reason: 'A new version is available with improvements and new features.',
        versionInfo
      };
    }

    // No update needed
    return {
      type: 'none',
      reason: 'App is up to date',
      versionInfo
    };
  } catch (error) {
    // Return no action if everything fails
    return {
      type: 'none',
      reason: 'Unable to check for updates',
      versionInfo: {
        current_version: APP_INFO.VERSION,
        download_url: '',
        forced_update: false,
        release_notes: [],
        file_size_mb: 0,
        release_date: new Date().toLocaleDateString('en-GB')
      }
    };
  }
};

/**
 * Get current app version info
 */
export const getCurrentVersion = (): { version: string; build: string } => {
  return {
    version: APP_INFO.VERSION,
    build: APP_INFO.BUILD
  };
};

/**
 * Check if current version is newer than specified version
 */
export const isNewerVersion = (version: string): boolean => {
  return compareVersions(APP_INFO.VERSION, version) > 0;
};

/**
 * Check if current version is older than specified version
 */
export const isOlderVersion = (version: string): boolean => {
  return compareVersions(APP_INFO.VERSION, version) < 0;
};

/**
 * Check if current version equals specified version
 */
export const isSameVersion = (version: string): boolean => {
  return compareVersions(APP_INFO.VERSION, version) === 0;
};

/**
 * Parse version string into components
 */
export const parseVersion = (version: string): { major: number; minor: number; patch: number } => {
  const parts = version.split('.').map(Number);
  return {
    major: parts[0] || 0,
    minor: parts[1] || 0,
    patch: parts[2] || 0
  };
};

/**
 * Format version for display
 */
export const formatVersion = (version: string, includeBuild: boolean = false): string => {
  if (includeBuild && version === APP_INFO.VERSION) {
    return `${version} (${APP_INFO.BUILD})`;
  }
  return version;
};

/**
 * Get version comparison result as human-readable string
 */
export const getVersionComparisonText = (currentVersion: string, compareVersion: string): string => {
  const comparison = compareVersions(currentVersion, compareVersion);
  
  if (comparison > 0) {
    return `${currentVersion} is newer than ${compareVersion}`;
  } else if (comparison < 0) {
    return `${currentVersion} is older than ${compareVersion}`;
  } else {
    return `${currentVersion} is the same as ${compareVersion}`;
  }
};

/**
 * Check if version string is valid semantic version
 */
export const isValidVersion = (version: string): boolean => {
  const semverRegex = /^\d+\.\d+\.\d+$/;
  return semverRegex.test(version);
};

/**
 * Get next version based on type of update
 */
export const getNextVersion = (
  currentVersion: string, 
  updateType: 'major' | 'minor' | 'patch'
): string => {
  const { major, minor, patch } = parseVersion(currentVersion);
  
  switch (updateType) {
    case 'major':
      return `${major + 1}.0.0`;
    case 'minor':
      return `${major}.${minor + 1}.0`;
    case 'patch':
      return `${major}.${minor}.${patch + 1}`;
    default:
      return currentVersion;
  }
};

/**
 * Get version update type between two versions
 */
export const getUpdateType = (fromVersion: string, toVersion: string): 'major' | 'minor' | 'patch' | 'none' => {
  const from = parseVersion(fromVersion);
  const to = parseVersion(toVersion);
  
  if (to.major > from.major) {
    return 'major';
  } else if (to.minor > from.minor) {
    return 'minor';
  } else if (to.patch > from.patch) {
    return 'patch';
  } else {
    return 'none';
  }
};

/**
 * Check for updates with custom version comparison
 */
export const checkForUpdatesWithCustomLogic = async (
  customCompare?: (current: string, latest: string) => boolean
): Promise<UpdateAction> => {
  try {
    const versionInfo = await checkVersion();
    
    if (!versionInfo) {
      return {
        type: 'none',
        reason: 'Unable to check for updates',
        versionInfo: {
          current_version: APP_INFO.VERSION,
          download_url: '',
          forced_update: false,
          release_notes: [],
          file_size_mb: 0,
          release_date: new Date().toLocaleDateString('en-GB')
        }
      };
    }

    const currentVersion = APP_INFO.VERSION;
    const latestVersion = versionInfo.current_version;
    
    // Use custom comparison if provided, otherwise use default
    const needsUpdate = customCompare 
      ? customCompare(currentVersion, latestVersion)
      : compareVersions(currentVersion, latestVersion) < 0;

    if (needsUpdate) {
      return {
        type: versionInfo.forced_update ? 'forced' : 'optional',
        reason: versionInfo.forced_update 
          ? 'A critical update is available. Please update to continue.'
          : 'A new version is available with improvements and new features.',
        versionInfo
      };
    }

    return {
      type: 'none',
      reason: 'App is up to date',
      versionInfo
    };
  } catch (error) {
    return {
      type: 'none',
      reason: 'Unable to check for updates',
      versionInfo: {
        current_version: APP_INFO.VERSION,
        download_url: '',
        forced_update: false,
        release_notes: [],
        file_size_mb: 0,
        release_date: new Date().toLocaleDateString('en-GB')
      }
    };
  }
};
