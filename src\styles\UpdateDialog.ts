import { StyleSheet } from 'react-native';
import { NEUTRAL, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const updateDialogStyles = StyleSheet.create({
  // Dialog container
  dialog: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
  },

  // Dialog title
  dialogTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL,
  },

  // Dialog content
  dialogContent: {
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
  },

  // Version text
  versionText: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL,
  },

  // Info text (file size, date)
  infoText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.MEDIUM,
  },

  // Reason text
  reasonText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: MD.SPACING.MEDIUM,
  },

  // Release notes section
  releaseNotesSection: {
    marginBottom: MD.SPACING.MEDIUM,
  },
  releaseNotesTitle: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    marginBottom: MD.SPACING.SMALL,
  },
  releaseNoteItem: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    lineHeight: 20,
    marginBottom: MD.SPACING.XSMALL,
    paddingLeft: MD.SPACING.SMALL,
  },

  // Warning container for forced updates
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: SEMANTIC.WARNING + '20', // 20% opacity
    padding: MD.SPACING.MEDIUM,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    borderLeftWidth: 4,
    borderLeftColor: SEMANTIC.ERROR,
    marginBottom: MD.SPACING.MEDIUM,
  },
  warningIcon: {
    marginRight: MD.SPACING.SMALL,
  },
  warningText: {
    flex: 1,
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    fontWeight: '600',
    color: SEMANTIC.ERROR,
    lineHeight: 20,
  },

  // Dialog actions
  dialogActions: {
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingBottom: MD.SPACING.MEDIUM,
    gap: MD.SPACING.MEDIUM,
  },

  // Button styles
  laterButton: {
    flex: 1,
    borderColor: NEUTRAL.TEXT_SECONDARY,
  },
  laterButtonText: {
    color: NEUTRAL.TEXT_PRIMARY,
    fontSize: MD.TYPOGRAPHY.BUTTON.fontSize,
    fontWeight: '500',
  },
  downloadButton: {
    flex: 1,
  },
  downloadButtonText: {
    color: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.BUTTON.fontSize,
    fontWeight: '600',
  },

});
