import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import { quantityControlStyles as styles } from '../styles/QuantityControl';

interface QuantityControlProps {
  quantity: number;
  onIncrement: () => void;
  onDecrement: () => void;
  onDirectInput?: (quantity: number) => void;
  allowDirectInput?: boolean;
  disabled?: boolean;
}

const QuantityControl: React.FC<QuantityControlProps> = React.memo(({
  quantity,
  onIncrement,
  onDecrement,
  onDirectInput,
  allowDirectInput = true,
  disabled = false,
}) => {
  const [inputValue, setInputValue] = useState(quantity.toString());
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const lastUpdateRef = useRef<NodeJS.Timeout | null>(null);
  const currentQuantityRef = useRef(quantity);
  const userEditingRef = useRef(false);

  // Update input value and quantity ref when quantity changes from outside
  useEffect(() => {
    // Always update in these cases:
    // 1. When input is not focused
    // 2. When quantity is set to 0 (likely from delete button)
    // 3. When quantity changes while input is focused but user is not actively editing
    if (!isFocused || quantity === 0 || (isFocused && !userEditingRef.current)) {
      setInputValue(quantity.toString());
    }
    currentQuantityRef.current = quantity;
  }, [quantity, isFocused]);

  // Set up keyboard dismiss listener to blur the input
  // We're removing this listener as it can cause inconsistent behavior
  // The input will naturally blur when the user taps outside or presses Done
  useEffect(() => {
    // Cleanup timeouts on unmount
    return () => {
      if (lastUpdateRef.current) {
        clearTimeout(lastUpdateRef.current);
      }
    };
  }, []);

  // Handle text input changes with delayed quantity updates
  // This prevents intermediate calculations while the user is still typing
  const handleInputChange = (text: string) => {
    // Only allow numeric input or empty string
    if (/^\d*$/.test(text)) {
      // Mark that user is actively editing
      userEditingRef.current = true;

      // Just update the input value without triggering quantity updates
      setInputValue(text);

      // We'll only update the actual quantity when:
      // 1. The input loses focus (handleInputBlur)
      // 2. The user presses Done (handleSubmitEditing)
      // 3. After a short period of inactivity (300ms)

      // Clear any pending updates
      if (lastUpdateRef.current) {
        clearTimeout(lastUpdateRef.current);
      }

      // Update after a short delay of inactivity (300ms)
      // This provides a more responsive feel while still preventing
      // excessive updates during rapid typing
      lastUpdateRef.current = setTimeout(() => {
        // Handle empty input - set quantity to 0
        if (text.trim() === '') {
          if (quantity !== 0 && onDirectInput) {
            onDirectInput(0);
          }
        } else {
          const numValue = parseInt(text, 10);
          if (!isNaN(numValue) && numValue >= 0 && onDirectInput) {
            // Update quantity after the user has stopped typing
            onDirectInput(numValue);

            // Don't dismiss keyboard or blur input here
            // Let the user continue typing if they want
          }
        }

        // User is no longer actively editing after the timeout
        userEditingRef.current = false;
      }, 300); // 300ms delay for better responsiveness
    }
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    // Reset the editing state when focusing
    // This will be set to true when the user actually starts typing
    userEditingRef.current = false;
  };

  const handleInputBlur = () => {
    // Set focused state to false
    setIsFocused(false);

    // User is no longer actively editing
    userEditingRef.current = false;

    // Cancel any pending updates
    if (lastUpdateRef.current) {
      clearTimeout(lastUpdateRef.current);
      lastUpdateRef.current = null;
    }

    // Handle empty input - set quantity to 0
    if (inputValue.trim() === '') {
      if (quantity !== 0 && onDirectInput) {
        onDirectInput(0);
      }
      setInputValue('0');
      return;
    }

    // Convert to number and validate
    const numValue = parseInt(inputValue, 10);
    if (isNaN(numValue) || numValue < 0) {
      // If invalid, reset to current quantity
      setInputValue(quantity.toString());
      return;
    }

    // Always update the quantity when input loses focus
    // This ensures we have the final value
    if (numValue !== quantity && onDirectInput) {
      onDirectInput(numValue);
      // Don't dismiss keyboard here - let the natural blur handle it
    }
  };

  const handleSubmitEditing = () => {
    // Cancel any pending updates
    if (lastUpdateRef.current) {
      clearTimeout(lastUpdateRef.current);
      lastUpdateRef.current = null;
    }

    // User is no longer actively editing
    userEditingRef.current = false;

    // Handle empty input - set quantity to 0
    if (inputValue.trim() === '') {
      if (quantity !== 0 && onDirectInput) {
        onDirectInput(0);
      }
      setInputValue('0');
    } else {
      // Apply the current value for non-empty inputs
      const numValue = parseInt(inputValue, 10);
      if (!isNaN(numValue) && numValue >= 0 && numValue !== quantity && onDirectInput) {
        onDirectInput(numValue);
      }
    }

    // Don't dismiss keyboard or remove focus - keep cursor in the input field
    // This allows the user to continue editing if needed
    // This fixes the issue where pressing "Done" on the keyboard would remove focus
  };

  return (
    <View style={styles.quantityControls}>
      <TouchableOpacity
        onPress={() => {
          if (quantity > 0 && !disabled) {
            // When decrement button is pressed, we're not in user editing mode
            userEditingRef.current = false;
            onDecrement();
          }
        }}
        style={[
          styles.quantityButton,
          (quantity === 0 || disabled) ? styles.quantityButtonDisabled : null
        ]}
        disabled={quantity === 0 || disabled}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.quantityButtonText,
          (quantity === 0 || disabled) ? styles.quantityButtonTextDisabled : null
        ]}>-</Text>
      </TouchableOpacity>

      {allowDirectInput ? (
        <TextInput
          ref={inputRef}
          style={[
            styles.quantityInput,
            disabled && styles.quantityInputDisabled
          ]}
          value={inputValue}
          onChangeText={handleInputChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          onSubmitEditing={handleSubmitEditing}
          keyboardType="numeric"
          returnKeyType="done"
          selectTextOnFocus
          editable={!disabled}
        />
      ) : (
        <Text style={styles.quantityValue}>{quantity}</Text>
      )}

      <TouchableOpacity
        onPress={() => {
          if (!disabled) {
            // When increment button is pressed, we're not in user editing mode
            userEditingRef.current = false;
            onIncrement();
          }
        }}
        style={[
          styles.quantityButton,
          disabled && styles.quantityButtonDisabled
        ]}
        disabled={disabled}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.quantityButtonText,
          disabled && styles.quantityButtonTextDisabled
        ]}>+</Text>
      </TouchableOpacity>
    </View>
  );
}, (prevProps, nextProps) => {
  // Only re-render if quantity, disabled state, or allowDirectInput changes
  return (
    prevProps.quantity === nextProps.quantity &&
    prevProps.disabled === nextProps.disabled &&
    prevProps.allowDirectInput === nextProps.allowDirectInput
  );
});

export default QuantityControl;
