import { useState, useMemo, useCallback, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { CartItem } from '../types/business';
import {
  QuantityUpdater,
  QuantityUpdateFunction,
  UseCheckoutCartReturn,
  NavigationOptions
} from '../types/business';

import { ROUTES } from '../constants/routes';
import { STORAGE_KEYS } from '../constants/storage';
import { calculateTotalItems, calculateCartTotal, isCartEmpty } from '../orchestrators/cart';

export const useCheckoutCart = (
  initialItems: CartItem[],
  customerId: string,
  customerName: string,
  navigationOptions: NavigationOptions
): UseCheckoutCartReturn => {
  // Ensure all cart items have customer information and orderIndex
  const itemsWithCustomerInfo = initialItems.map((item, index) => ({
    ...item,
    customerId,
    customerName,
    orderIndex: item.orderIndex || (index + 1) // Assign orderIndex if missing
  }));

  const [cartItems, setCartItems] = useState<CartItem[]>(itemsWithCustomerInfo);

  const cartCacheKey = `${STORAGE_KEYS.CART_PREFIX}${customerId}`;

  // Update cart items when initialItems changes (when cart loads from AsyncStorage)
  useEffect(() => {
    const updatedItems = initialItems.map((item, index) => ({
      ...item,
      customerId,
      customerName,
      orderIndex: item.orderIndex || (index + 1)
    }));
    setCartItems(updatedItems);
  }, [initialItems, customerId, customerName]);

  // Cart items update automatically when AsyncStorage data loads

  const totalItems = useMemo(() => calculateTotalItems(cartItems), [cartItems]);
  const totalAmount = useMemo(() => calculateCartTotal(cartItems), [cartItems]);
  const cartEmpty = useMemo(() => isCartEmpty(cartItems), [cartItems]);
  const navigateBack = useCallback((items: CartItem[]) => {
    const { fromCart, originalSource } = navigationOptions;

    if (fromCart) {
      router.push({
        pathname: ROUTES.CARTS,
        params: {
          customerId,
          customerName,
          items: JSON.stringify(items),
          timestamp: Date.now().toString(), // Force parameter refresh
        },
      });
    } else {
      router.push({
        pathname: ROUTES.CREATE_ORDER,
        params: {
          customerId,
          customerName,
          items: JSON.stringify(items),
          from: originalSource === 'cart' ? 'cart' : 'customer', // Use 'from' parameter to match order creation expectations
          timestamp: Date.now().toString(), // Force parameter refresh
        },
      });
    }
  }, [customerId, customerName, navigationOptions]);
  const updateQuantity: QuantityUpdateFunction = useCallback(
    async (itemCode: string, newQuantityOrFn: number | QuantityUpdater) => {
      try {
        let updatedItems: CartItem[] = [];

        // Update local state and capture the new items
        setCartItems(prevItems => {
          const newItems = prevItems.map(item => {
            if (item.item_code === itemCode) {
              let updatedQuantity: number;

              if (typeof newQuantityOrFn === 'function') {
                updatedQuantity = Math.max(0, newQuantityOrFn(item.quantity));
              } else {
                updatedQuantity = Math.max(0, newQuantityOrFn);
              }

              return { ...item, quantity: updatedQuantity };
            }
            return item;
          }).filter(item => item.quantity > 0); // Remove items with 0 quantity

          updatedItems = newItems;
          return newItems;
        });

        // Save to AsyncStorage immediately for consistency
        if (updatedItems.length > 0) {
          await AsyncStorage.setItem(cartCacheKey, JSON.stringify(updatedItems));
        } else {
          await AsyncStorage.removeItem(cartCacheKey);
        }

        // Navigate back if cart is empty
        if (updatedItems.length === 0) {
          setTimeout(() => navigateBack([]), 100);
        }

      } catch (error) {
        if (__DEV__) console.error('Error updating cart quantity:', error);
      }
    },
    [cartCacheKey, navigateBack]
  );

  // Clear cart function for after successful order placement
  const clearCart = useCallback(() => {
    setCartItems([]);
  }, []);

  return {
    cartItems,
    updateQuantity,
    clearCart,
    totalItems,
    totalAmount,
    cartEmpty,
  };
};
