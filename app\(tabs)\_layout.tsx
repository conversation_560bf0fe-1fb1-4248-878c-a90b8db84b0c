import React from 'react';
import { Tabs } from 'expo-router';
import { View } from 'react-native';
import FloatingTabBar from '../../src/components/FloatingTabBar';
import { useTabNavigation } from '../../src/hooks/useTabNavigation';

const tabsConfig = [
  {
    key: 'home',
    title: 'Home',
    icon: 'home-outline',
    iconFocused: 'home',
  },
  {
    key: 'customers',
    title: 'Customers',
    icon: 'account-group-outline',
    iconFocused: 'account-group',
  },
  {
    key: 'orders',
    title: 'Orders',
    icon: 'clipboard-list-outline',
    iconFocused: 'clipboard-list',
  },
  {
    key: 'invoices',
    title: 'Invoices',
    icon: 'file-document-outline',
    iconFocused: 'file-document',
  },
  {
    key: 'carts',
    title: 'Carts',
    icon: 'cart-outline',
    iconFocused: 'cart',
  },
];

const TabsLayout: React.FC = () => {
  // Global tab navigation handler - will detect current tab automatically
  useTabNavigation();

  return (
    <View style={{ flex: 1, backgroundColor: '#fff' }}>
      <Tabs
        screenOptions={{
          headerShown: false,
          lazy: false,
          animation: 'none',
          freezeOnBlur: false,
        }}
        tabBar={(props) => <FloatingTabBar {...props} tabs={tabsConfig} />}
      >
        <Tabs.Screen name="home" options={{ lazy: false }} />
        <Tabs.Screen name="customers" options={{ lazy: false }} />
        <Tabs.Screen name="orders" options={{ lazy: true }} />
        <Tabs.Screen name="invoices" options={{ lazy: true }} />
        <Tabs.Screen name="carts" options={{ lazy: true }} />
      </Tabs>
    </View>
  );
};

export default TabsLayout;