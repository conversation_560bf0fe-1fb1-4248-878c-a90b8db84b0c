import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const filterBarStyles = StyleSheet.create({
  container: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingTop: MD.SPACING.SMALL,
    paddingBottom: MD.SPACING.XSMALL,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexGrow: 1,
    paddingTop: 0,
    paddingBottom: MD.SPACING.XSMALL / 2,
  },
  chipWrapper: {
    marginRight: MD.SPACING.SMALL,
  },
  chip: {
    height: 32,
    borderRadius: MD.BORDER_RADIUS.LARGE,
    borderWidth: 1,
    backgroundColor: `${BRAND.PRIMARY}08`,
    elevation: 0,
  },
  chipText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    marginHorizontal: MD.SPACING.XSMALL,
  },
  enhancedCloseButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent', // Make it invisible
  },
  relativeContainer: {
    position: 'relative',
  },
  closeButtonSpacer: {
    width: 24,
    height: 24,
  },
});
