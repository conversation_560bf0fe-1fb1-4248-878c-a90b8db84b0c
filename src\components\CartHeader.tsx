import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CartHeaderProps } from '../types/business';
import { ICON_SIZES, HIT_SLOP } from '../constants/ui';
import { BRAND, NEUTRAL } from '../constants/colors';
import { formatCurrency } from '../utils/formatting';
import { formatCartItemCount } from '../orchestrators/cart';

interface CartHeaderPropsWithStyles extends CartHeaderProps {
  styles: any;
}

/**
 * Reusable cart header component showing customer info and cart summary
 */
const CartHeader: React.FC<CartHeaderPropsWithStyles> = ({
  cart,
  isExpanded,
  onToggleExpand,
  totalAmount,
  styles
}) => {
  const totalQuantity = cart.items.reduce((sum, item) => sum + item.quantity, 0);
  const itemCountText = formatCartItemCount(cart.items.length, totalQuantity);

  return (
    <TouchableOpacity
      style={[
        styles.cartHeader,
        isExpanded && styles.cartHeaderExpanded
      ]}
      onPress={onToggleExpand}
      activeOpacity={0.7}
      hitSlop={HIT_SLOP}
    >
      <View style={styles.cartHeaderLeft}>
        <MaterialCommunityIcons
          name="account"
          size={ICON_SIZES.MEDIUM}
          color={BRAND.PRIMARY}
          style={styles.cartIcon}
        />
        <View>
          <Text style={styles.cartCustomerName}>{cart.customerName}</Text>
          <View style={styles.cartSubInfo}>
            <Text style={styles.cartItemCount}>
              {itemCountText}
            </Text>
            <Text style={styles.cartTotal}>
              {formatCurrency(totalAmount)}
            </Text>
            {cart.lastUpdated && (
              <Text style={styles.cartTimestamp}>
                Last updated: {cart.lastUpdated}
              </Text>
            )}
          </View>
        </View>
      </View>
      <MaterialCommunityIcons
        name={isExpanded ? "chevron-up" : "chevron-down"}
        size={ICON_SIZES.MEDIUM}
        color={NEUTRAL.TEXT_SECONDARY}
      />
    </TouchableOpacity>
  );
};



export default React.memo(CartHeader);
