import { StyleSheet } from 'react-native';
import { BRAND, NEUTRAL } from '../constants/colors';
import MD from '../constants/design';
export const checkoutScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },

  header: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: NEUTRAL.DIVIDER,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingTop: MD.SPACING.SMALL,
    paddingBottom: MD.SPACING.LARGE,
  },

  customerCard: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginHorizontal: MD.SPACING.SMALL,
    marginBottom: MD.SPACING.MEDIUM,
    padding: MD.SPACING.MEDIUM,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  customerName: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
    marginBottom: MD.SPACING.XSMALL,
  },
  customerDetails: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: 2,
  },
  customerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: MD.SPACING.XSMALL,
  },
  customerIcon: {
    marginRight: 6,
  },



  // Item cards - clean and minimal
  itemCard: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginHorizontal: MD.SPACING.SMALL,
    marginBottom: MD.SPACING.XSMALL,
    padding: MD.SPACING.MEDIUM,
    elevation: 1,
    position: 'relative',
  },
  deleteButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    padding: 4,
    backgroundColor: 'rgba(255,255,255,0.9)',
    borderRadius: 12,
    zIndex: 1,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  indexContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: `${BRAND.PRIMARY}20`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: MD.SPACING.SMALL,
  },
  index: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
  },
  nameContainer: {
    flex: 1,
    paddingRight: 30,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px to match order creation
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },

  // Simple row layout
  itemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: MD.SPACING.SMALL,
  },

  // UOM button
  uomButton: {
    backgroundColor: `${BRAND.PRIMARY}15`,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginRight: MD.SPACING.SMALL,
    marginLeft: -12,
    minWidth: 60,
    alignItems: 'center',
    justifyContent: 'center',
  },
  uomText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    fontWeight: '600',
    color: BRAND.PRIMARY,
    textAlign: 'center',
  },

  // Quantity controls
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: MD.SPACING.MEDIUM,
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: NEUTRAL.DIVIDER,
  },
  quantityText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px to match order creation
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  quantityValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px to match order creation
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    marginHorizontal: 6, // Further reduced from 8 to 6 for even tighter spacing
    minWidth: 24,
    textAlign: 'center',
  },
  // Left controls section
  leftControlsSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 32, // 24px (index container width) + 8px (margin) = 32px to align with item name
  },
  uomQuantitySpacing: {
    marginLeft: 12,
  },

  // Right info section
  rightInfoSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateColumn: {
    alignItems: 'center',
    width: 50,
    marginRight: 12,
  },
  amountColumn: {
    alignItems: 'center',
    width: 60,
  },
  priceLabel: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: 2,
  },
  priceValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px to match order creation
    fontWeight: '600',
  },
  rateValue: {
    color: BRAND.PRIMARY, // Use theme color like customer name
  },
  amountValue: {
    color: '#34C759', // Green for amount
  },

  // Footer
  footer: {
    backgroundColor: NEUTRAL.WHITE,
    padding: MD.SPACING.MEDIUM,
    borderTopWidth: 1,
    borderTopColor: NEUTRAL.DIVIDER,
    elevation: 3,
    // paddingBottom will be set dynamically using TabBarUtils
  },
  placeOrderButton: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    height: 48,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
  },
  buttonText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '600',
    color: NEUTRAL.WHITE,
  },
  buttonAmount: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: NEUTRAL.WHITE,
  },
  disabledButton: {
    backgroundColor: NEUTRAL.TEXT_DISABLED,
    opacity: 0.6,
  },
});


