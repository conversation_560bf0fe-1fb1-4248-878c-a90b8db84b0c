import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CheckoutHeaderProps } from '../types/business';
import { SCREEN_TITLES } from '../constants/messages';
import { ICON_SIZES, HIT_SLOP } from '../constants/ui';
import { checkoutHeaderStyles } from '../styles/CheckoutHeader';

/**
 * Reusable header component for checkout screen
 */
const CheckoutHeader: React.FC<CheckoutHeaderProps> = ({
  onBackPress,
  title = SCREEN_TITLES.CHECKOUT.MAIN
}) => {
  return (
    <View style={checkoutHeaderStyles.header}>
      <View style={checkoutHeaderStyles.headerContent}>
        <TouchableOpacity
          onPress={onBackPress}
          style={checkoutHeaderStyles.backButton}
          hitSlop={HIT_SLOP}
        >
          <MaterialCommunityIcons
            name="arrow-left"
            size={ICON_SIZES.MEDIUM}
            color="#333"
          />
        </TouchableOpacity>

        <Text style={checkoutHeaderStyles.title} numberOfLines={1}>
          {title}
        </Text>

        {/* Empty view for balanced layout */}
        <View style={checkoutHeaderStyles.spacer} />
      </View>
    </View>
  );
};

export default React.memo(CheckoutHeader);
