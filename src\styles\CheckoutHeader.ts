import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';
import { ICON_SIZES } from '../constants/ui';

export const checkoutHeaderStyles = StyleSheet.create({
  header: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    elevation: 0, // Remove elevation for flat design consistency
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'left',
    flex: 1,
  },
  backButton: {
    padding: MD.SPACING.SMALL,
  },
  spacer: {
    width: ICON_SIZES.MEDIUM,
  },
});
