/**
 * Theme configuration
 *
 * This file defines the app's theme based on react-native-paper's DefaultTheme
 * with customized colors from our color constants.
 */

import { DefaultTheme } from 'react-native-paper';
import { BRAND, NEUTRAL, SEMANTIC } from './colors';

const colors = {
  primary: BRAND.PRIMARY,
  secondary: BRAND.SECONDARY,
  accent: BRAND.ACCENT,
  background: NEUTRAL.BACKGROUND,
  surface: NEUTRAL.SURFACE,
  text: NEUTRAL.TEXT_PRIMARY,
  error: SEMANTIC.ERROR,
  success: SEMANTIC.SUCCESS,
  warning: SEMANTIC.WARNING,
};

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    error: colors.error,
  },
};

export default theme;
