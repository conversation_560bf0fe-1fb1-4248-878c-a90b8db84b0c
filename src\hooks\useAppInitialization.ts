
import { useEffect, useState, useCallback, useRef } from 'react';
import { initializeApp, createInitialState } from '../orchestrators/appInitializer';
import { InitializationState } from '../types/initialization';
import useErrorHandler from './useErrorHandler';
import { ErrorType } from '../utils/errors';

export const useAppInitialization = () => {
  const [state, setState] = useState<InitializationState>(createInitialState);
  const { handleError, handleSessionExpired } = useErrorHandler();
  const initializationRef = useRef<boolean>(false);

  const startInitialization = useCallback(async () => {
    if (initializationRef.current) return;
    initializationRef.current = true;

    setState(prev => ({ ...prev, isInitializing: true, error: null, redirectTo: null }));

    try {
      const redirectTo = await initializeApp({
        onError: (error: Error, context: string) => {
          if ((error as any)?.type === ErrorType.SESSION_EXPIRED) {
            handleSessionExpired();
            return;
          }
          handleError(error, { context, onRetry: () => {
            initializationRef.current = false;
            startInitialization();
          }});
        }
      });

      setState(prev => ({ ...prev, isInitializing: false, isInitialized: true, redirectTo }));
    } catch (error) {
      setState(prev => ({ ...prev, isInitializing: false, error: error as Error }));

      if ((error as any)?.type === ErrorType.SESSION_EXPIRED) {
        handleSessionExpired();
        return;
      }

      handleError(error as Error, {
        context: 'App Initialization',
        onRetry: () => {
          initializationRef.current = false;
          startInitialization();
        },
        silent: true
      });
    }
  }, [handleError, handleSessionExpired]);

  useEffect(() => {
    const immediateInit = () => {
      startInitialization();
    };

    if (typeof requestIdleCallback !== 'undefined') {
      const idleCallback = requestIdleCallback(immediateInit, { timeout: 100 });
      return () => cancelIdleCallback(idleCallback);
    }

    const initFrame = requestAnimationFrame(immediateInit);
    return () => cancelAnimationFrame(initFrame);
  }, [startInitialization]);

  return {
    ...state,
    startInitialization,
    retry: startInitialization
  };
};
