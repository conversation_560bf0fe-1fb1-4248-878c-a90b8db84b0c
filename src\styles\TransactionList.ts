import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const transactionListStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
  },
  listContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: MD.SPACING.LARGE,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XLARGE,
  },
  loadingText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: MD.SPACING.MEDIUM,
  },
  footerLoader: {
    paddingVertical: MD.SPACING.MEDIUM,
    alignItems: 'center',
  },
});
