/**
 * Application Messages - Simplified
 */

// Simple message constants - removed over-engineered nested structures
export const MESSAGES = {
  // Cart
  REMOVE_CART_CONFIRM: 'Are you sure you want to remove this cart?',
  CART_EMPTY: 'Your carts are empty',
  CART_LOADING: 'Loading carts...',
  NO_ITEMS: 'No items in cart',

  // Checkout
  CONFIRM_ORDER: 'Are you sure you want to place this order?',
  ORDER_SUCCESS: 'Order placed successfully!',
  ORDER_CREATED: 'Order has been created',
  PLACING_ORDER: 'Placing order...',
  UOM_CHANGE_RESTRICTED: 'Changing UOM is not available in checkout. Please go back to order creation.',

  // Create Order
  LOADING_ITEMS: 'Loading items...',

  // Common
  ERROR: 'An error occurred',
  LOADING: 'Loading...',
  CANCEL: 'Cancel',
  OK: 'OK',
  RETRY: 'Retry',
};

// Simple button text
export const BUTTONS = {
  CHECKOUT: 'Checkout',
  PLACE_ORDER: 'Place Order',
  CONTINUE: 'Continue',
  DELETE: 'Delete',
  BACK: 'Back',
  CANCEL: 'Cancel',
  RETRY: 'Retry',
  GO_TO_CUSTOMERS: 'Go to Customers',
  VIEW_ORDERS: 'View Orders',
  NEW_ORDER: 'New Order',
};

// Simple screen titles
export const TITLES = {
  CART: 'Your Carts',
  CHECKOUT: 'Checkout',
  CUSTOMER: 'Customer',
  ORDER_ITEMS: 'Order Items',
};

// Legacy exports for compatibility
export const SCREEN_TITLES = {
  CART: { MAIN: TITLES.CART, CUSTOMER_SECTION: TITLES.CUSTOMER },
  CHECKOUT: { MAIN: TITLES.CHECKOUT, CUSTOMER_SECTION: TITLES.CUSTOMER, ORDER_ITEMS: TITLES.ORDER_ITEMS },
};

export const BUTTON_TEXT = {
  CART: { CHECKOUT: BUTTONS.CHECKOUT, CONTINUE: BUTTONS.CONTINUE, DELETE: BUTTONS.DELETE, GO_TO_CUSTOMERS: BUTTONS.GO_TO_CUSTOMERS },
  CHECKOUT: { PLACE_ORDER: BUTTONS.PLACE_ORDER, CHECKOUT: BUTTONS.CHECKOUT, BACK: BUTTONS.BACK, CANCEL: BUTTONS.CANCEL, RETRY: BUTTONS.RETRY },
};

export const CART_MESSAGES = {
  REMOVE_CART: { TITLE: 'Remove Cart', MESSAGE: MESSAGES.REMOVE_CART_CONFIRM, CANCEL: MESSAGES.CANCEL, CONFIRM: 'Remove' },
  ERROR: { TITLE: MESSAGES.ERROR, LOADING_FAILED: 'Failed to load carts', REMOVING_FAILED: 'Failed to remove cart', PARSING_FAILED: 'Error parsing cart data', HANDLING_FAILED: 'Error handling cart items' },
  LOADING: { CARTS: MESSAGES.CART_LOADING },
  EMPTY_STATE: { TITLE: MESSAGES.CART_EMPTY, SUBTITLE: 'Add items to your carts from the customer screen', BUTTON_TEXT: BUTTONS.GO_TO_CUSTOMERS, NO_ITEMS: MESSAGES.NO_ITEMS },
};

export const CHECKOUT_MESSAGES = {
  CONFIRM_ORDER: { TITLE: 'Confirm Order', MESSAGE: (customerName: string) => `Are you sure you want to place this order for ${customerName}?`, CANCEL: MESSAGES.CANCEL, CONFIRM: BUTTONS.PLACE_ORDER },
  SUCCESS: { TITLE: MESSAGES.ORDER_SUCCESS, MESSAGE: (orderName: string, customerName: string) => `Order #${orderName} has been created for ${customerName}.\n\nWhat would you like to do next?`, VIEW_ORDERS: BUTTONS.VIEW_ORDERS, NEW_ORDER: BUTTONS.NEW_ORDER },
  ERROR: { TITLE: MESSAGES.ERROR, RETRY: MESSAGES.RETRY, OK: MESSAGES.OK, GENERIC: MESSAGES.ERROR },
  UOM_CHANGE: { TITLE: 'UOM Change', MESSAGE: MESSAGES.UOM_CHANGE_RESTRICTED, OK: MESSAGES.OK },
  LOADING: { PLACING_ORDER: MESSAGES.PLACING_ORDER, PROCESSING: MESSAGES.LOADING },
};
