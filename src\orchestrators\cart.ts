import AsyncStorage from '@react-native-async-storage/async-storage';
import { CartItem, CartScreenParams, OrderItem } from '../types/business';
import { STORAGE_KEYS } from '../constants/storage';
import { NAVIGATION_SOURCES } from '../constants/routes';
export const clearAllCarts = async (): Promise<void> => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const cartKeys = keys.filter(key => key.startsWith('cart_'));
    if (cartKeys.length > 0) {
      await AsyncStorage.multiRemove(cartKeys);
    }
  } catch (error) {
    // Silent fail
  }
};
export const calculateCartTotal = (cartItems: CartItem[]): number => {
  return cartItems.reduce((total, item) => {
    const rate = typeof item.rate === 'number' ? item.rate : parseFloat(item.rate || '0');
    return total + (rate * item.quantity);
  }, 0);
};

export const calculateCartTotalWithLookup = (
  cartItems: CartItem[],
  itemsMap: Record<string, OrderItem>
): number => {
  return cartItems.reduce((total, item) => {
    const itemDetails = itemsMap[item.item_code];
    if (!itemDetails) return total;
    const uomInfo = itemDetails.uoms.find(u => u.uom === item.selectedUom);
    const rate = uomInfo?.rate || 0;
    return total + (rate * item.quantity);
  }, 0);
};

export const calculateTotalItems = (cartItems: CartItem[]): number => {
  return cartItems.reduce((sum, item) => sum + item.quantity, 0);
};



export const getCartCount = (cartItems: CartItem[]): number => {
  return cartItems.filter(item => item.quantity > 0).length;
};

export const isCartEmpty = (cartItems: CartItem[]): boolean => {
  return cartItems.length === 0 || cartItems.every(item => item.quantity === 0);
};

export const validateCartParams = (params: CartScreenParams): boolean => {
  return !!(params?.customerId && params?.customerName && params?.items);
};
export const sortCartItems = (items: CartItem[]): CartItem[] => {
  return [...items].sort((a, b) => a.item_name.localeCompare(b.item_name));
};

export const sortCartItemsForCheckout = (items: CartItem[]): CartItem[] => {
  return items
    .filter(item => item.quantity > 0)
    .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0));
};
export const prepareCartItemsForNavigation = (
  cartItems: CartItem[],
  items: OrderItem[]
): (CartItem & { rate: number })[] => {
  return cartItems
    .filter(item => item.quantity > 0)
    .map(item => {
      const itemDetails = items.find(i => i.item_code === item.item_code);
      const uomInfo = itemDetails?.uoms?.find(u => u.uom === item.selectedUom);
      return { ...item, rate: uomInfo?.rate || 0 };
    });
};
export const createNavigationOptions = (
  params: any,
  customerId: string,
  customerName: string,
  cartItems: CartItem[]
) => {
  return {
    fromCart: params?.fromCart === true,
    originalSource: params?.originalSource || NAVIGATION_SOURCES.CUSTOMER,
    customerId,
    customerName,
    items: cartItems,
  };
};

// Simple checkout utilities
export const parseCartItemsFromParams = (params: any): CartItem[] => {
  try {
    return params.items ? JSON.parse(params.items) : [];
  } catch (error) {
    return [];
  }
};

export const extractCustomerInfo = (params: any) => {
  return {
    default_price_list: params.default_price_list || '',
    tax_id: params.tax_id || '',
    custom_credit_limit: params.custom_credit_limit || 0,
    outstanding: params.outstanding || 0,
    mobile_no: params.mobile_no || '',
    customer_primary_address: params.customer_primary_address || ''
  };
};

export const generateCartCacheKey = (customerId: string): string => {
  return `${STORAGE_KEYS.CART_PREFIX}${customerId}`;
};

// Legacy functions for compatibility
export const getCartItemFinancialValues = (item: CartItem) => {
  // Handle different rate formats and ensure we have a valid number
  let rate = 0;
  if (typeof item.rate === 'number') {
    rate = item.rate;
  } else if (typeof item.rate === 'string' && item.rate.trim() !== '') {
    rate = parseFloat(item.rate);
  }

  // Ensure rate is a valid number
  if (isNaN(rate) || rate < 0) {
    rate = 0;
  }

  const amount = item.quantity > 0 ? rate * item.quantity : 0;
  return { rate, amount };
};

export const formatCartItemCount = (count: number, totalQuantity?: number): string => {
  if (count === 0) return 'No items';
  if (count === 1) return totalQuantity && totalQuantity > 1 ? `1 item (${totalQuantity} total)` : '1 item';
  return totalQuantity && totalQuantity !== count ? `${count} items (${totalQuantity} total)` : `${count} items`;
};
