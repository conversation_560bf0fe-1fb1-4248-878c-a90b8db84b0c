import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const pageHeaderStyles = StyleSheet.create({
  header: {
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  searchRow: {
    width: '100%',
    marginBottom: 0,
  },
  headerTitle: {
    fontSize: 18, // Increased from H2 (16) to 18 for slightly bigger title
    fontWeight: '700', // Changed from 'bold' to '700' for consistency
    color: NEUTRAL.TEXT_PRIMARY,
    flex: 1,
  },
  headerTitleWithBack: {
    marginLeft: 8,
  },
  backButton: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  rightElementsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchContainer: {
    width: '100%',
  },
});
