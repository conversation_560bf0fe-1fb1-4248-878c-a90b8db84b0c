/**
 * UI Constants - Simplified
 */

// Basic UI values
export const HIT_SLOP = {
  top: 10,
  bottom: 10,
  left: 10,
  right: 10,
};

// Simple timing values
export const TIMING = {
  DEBOUNCE: 300,
  ANIMATION: 300,
  CART_SAVE_DEBOUNCE: 500,
  STATE_UPDATE_DELAY: 100,
};

// Basic list configuration
export const LIST_CONFIG = {
  ESTIMATED_ITEM_SIZE: 90,
  INITIAL_NUM_TO_RENDER: 10,
  SCROLL_EVENT_THROTTLE: 16,
  SCROLL_THRESHOLD: 300,
};

// Simple search configuration
export const SEARCH = {
  PLACEHOLDER: {
    TRANSACTIONS: 'Customer Name',
    CARTS: 'Customer, Item',
    CUSTOMERS: 'Name, TIN, Phone',
  },
  DEBOUNCE_TIME: 300,
  FIELDS: {
    CART: ['customerName'] as const,
    CUSTOMER: ['customer_name', 'tax_id', 'mobile_no'] as const,
  },
};

// Legacy exports for compatibility
export const ICON_SIZES = {
  SMALL: 16,
  MEDIUM: 24,
  LAR<PERSON>: 64,
};

export const ANIMATION = {
  SCROLL_TO_TOP: true,
  SCROLL_ANIMATED: true,
  DISABLE_AUTO_LAYOUT: true,
};
