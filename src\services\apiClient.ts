import axios, { AxiosInstance } from 'axios';
import { API } from '../constants/api';
import { STORAGE_KEYS } from '../constants/storage';
import * as SecureStorage from '../utils/auth';
import { addErrorHandlingMiddleware } from '../utils/apiMiddleware';

export const BASE_URL = API.BASE_URL;

// 🚀 WORLD-CLASS API Client with hyper-optimizations
export const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: BASE_URL,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      // 🚀 HYPER-PERFORMANCE HEADERS
      'Accept-Encoding': 'gzip, deflate, br, zstd',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Keep-Alive': 'timeout=60, max=1000',
      'User-Agent': 'SalesMate/1.0 (Mobile; Android)',
    },
    timeout: API.TIMEOUT,
    validateStatus: null,
    // 🚀 ULTRA-PERFORMANCE OPTIMIZATIONS
    maxRedirects: 2, // Reduced for faster response
    maxContentLength: 100 * 1024 * 1024, // 100MB
    maxBodyLength: 100 * 1024 * 1024, // 100MB
    // 🚀 HTTP/2 and advanced connection optimization
    httpAgent: false,
    httpsAgent: false,
    // 🚀 COMPRESSION OPTIMIZATION
    decompress: true,
    // 🚀 RESPONSE OPTIMIZATION
    responseType: 'json',
    responseEncoding: 'utf8',
  });

  client.interceptors.request.use(
    async (config) => {
      try {
        const sid = await SecureStorage.getSecureItem(STORAGE_KEYS.SID);
        config.headers = config.headers || {};

        if (sid) {
          config.headers.Cookie = `sid=${sid}`;
        }

        return config;
      } catch (error) {
        return Promise.reject(error);
      }
    },
    (error) => Promise.reject(error)
  );

  addErrorHandlingMiddleware(client);
  return client;
};

export const apiClient = createApiClient();
