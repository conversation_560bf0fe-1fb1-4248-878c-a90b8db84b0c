import { StyleSheet } from 'react-native';
import { BRAND } from '../constants/colors';

export const filterTabsStyles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },

  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 2,
  },

  tab: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 6,
    backgroundColor: 'transparent',
    minHeight: 32,
  },

  selectedTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  pressedTab: {
    backgroundColor: BRAND.PRIMARY_10, // Use theme primary light color for pressed state
    transform: [{ scale: 0.98 }],
  },

  disabledTab: {
    opacity: 0.6,
  },

  tabText: {
    fontSize: 13,
    fontWeight: '500',
    color: '#666666',
    textAlign: 'center',
  },

  selectedTabText: {
    color: BRAND.PRIMARY,
    fontWeight: '600',
  },

  countText: {
    fontSize: 11,
    color: '#999999',
    marginLeft: 3,
    fontWeight: '400',
  },

  selectedCountText: {
    color: BRAND.PRIMARY,
    fontWeight: '500',
  },
});
