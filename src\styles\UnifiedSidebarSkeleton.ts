import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const unifiedSidebarSkeletonStyles = StyleSheet.create({
  sidebarContainer: {
    width: 65,
    backgroundColor: NEUTRAL.WHITE,
    borderRightWidth: 1,
    borderRightColor: MD.DIVIDER,
    flexDirection: 'column',
  },
  
  // Toggle header skeleton styles
  toggleContainer: {
    flexDirection: 'column',
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    paddingVertical: MD.SPACING.XXSMALL,
  },
  toggleButtonSkeleton: {
    height: 28,
    marginHorizontal: MD.SPACING.XXSMALL / 2,
    marginVertical: MD.SPACING.XXSMALL / 4,
    borderRadius: MD.BORDER_RADIUS.XSMALL,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  
  // Scrollable content skeleton styles
  sidebarContent: {
    flex: 1,
    paddingVertical: MD.SPACING.XSMALL,
  },
  
  // Item tab skeleton styles
  itemTabSkeleton: {
    paddingVertical: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.XXSMALL / 2,
    marginHorizontal: MD.SPACING.XXSMALL / 2,
    marginVertical: MD.SPACING.XXSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    minHeight: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemTextSkeleton: {
    width: '80%',
    height: 12,
    borderRadius: 6,
    marginBottom: MD.SPACING.XXSMALL / 2,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  itemCountSkeleton: {
    width: '40%',
    height: 10,
    borderRadius: 5,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  
  // Divider
  itemDivider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginHorizontal: MD.SPACING.XSMALL,
    opacity: 0.3,
  },
});
