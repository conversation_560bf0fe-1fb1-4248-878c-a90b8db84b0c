/**
 * Performance monitoring utilities for filter operations
 */

export class FilterPerformanceMonitor {
  private static measurements: Map<string, number> = new Map();

  static startMeasurement(operation: string): void {
    if (__DEV__) {
      this.measurements.set(operation, performance.now());
    }
  }

  static endMeasurement(operation: string): number {
    if (__DEV__) {
      const startTime = this.measurements.get(operation);
      if (startTime) {
        const duration = performance.now() - startTime;
        this.measurements.delete(operation);
        
        // Log slow operations
        if (duration > 16) { // More than one frame at 60fps
          console.warn(`Slow filter operation: ${operation} took ${duration.toFixed(2)}ms`);
        }
        
        return duration;
      }
    }
    return 0;
  }

  static measureFilterChange<T>(operation: string, fn: () => T): T {
    this.startMeasurement(operation);
    const result = fn();
    this.endMeasurement(operation);
    return result;
  }
}

/**
 * Optimized filter functions with performance monitoring
 */
export const createOptimizedFilterFunctions = () => {
  return {
    filterBySupplier: (items: any[], supplier: string) => {
      return FilterPerformanceMonitor.measureFilterChange('filterBySupplier', () => {
        return items.filter(item => item.supplier_short_name === supplier);
      });
    },

    filterByBrand: (items: any[], brand: string) => {
      return FilterPerformanceMonitor.measureFilterChange('filterByBrand', () => {
        return items.filter(item => item.brand === brand);
      });
    },

    filterByItemGroup: (items: any[], itemGroup: string) => {
      return FilterPerformanceMonitor.measureFilterChange('filterByItemGroup', () => {
        return items.filter(item => item.item_group === itemGroup);
      });
    },

    searchItems: (items: any[], searchTerm: string) => {
      return FilterPerformanceMonitor.measureFilterChange('searchItems', () => {
        const normalizedTerm = searchTerm.toLowerCase().trim();
        return items.filter(item =>
          item.item_name.toLowerCase().includes(normalizedTerm) ||
          item.supplier_short_name.toLowerCase().includes(normalizedTerm) ||
          item.brand.toLowerCase().includes(normalizedTerm) ||
          item.item_group.toLowerCase().includes(normalizedTerm)
        );
      });
    }
  };
};
