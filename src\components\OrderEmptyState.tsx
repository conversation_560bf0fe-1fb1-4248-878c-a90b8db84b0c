import React from 'react';
import { View } from 'react-native';
import { Text, Button, Avatar } from 'react-native-paper';
import { orderEmptyStateStyles } from '../styles/OrderEmptyState';

interface OrderEmptyStateProps {
  searchQuery?: string;
  selectedValue?: string | null;
  filterType?: 'supplier' | 'brand' | 'itemGroup';
  onClearFilters?: () => void;
  loading?: boolean;
}

const OrderEmptyState: React.FC<OrderEmptyStateProps> = ({
  searchQuery,
  selectedValue,
  filterType,
  onClearFilters,
  loading = false,
}) => {
  if (loading) {
    return null;
  }

  // Determine the appropriate message based on filters
  const getEmptyMessage = () => {
    const hasSearch = searchQuery?.trim();
    const hasFilter = selectedValue;
    const hasAnyFilter = hasSearch || hasFilter;
    
    if (hasAnyFilter) {
      let filterDescription = '';
      if (hasSearch && hasFilter) {
        filterDescription = `search "${searchQuery}" and ${filterType} filter`;
      } else if (hasSearch) {
        filterDescription = `search "${searchQuery}"`;
      } else if (hasFilter) {
        const filterTypeLabel = filterType === 'itemGroup' ? 'item group' : filterType;
        filterDescription = `${filterTypeLabel} "${selectedValue}"`;
      }

      return {
        title: 'No items found',
        subtitle: `No items match your ${filterDescription}. Try adjusting your filters to find items.`,
        showClearButton: true,
      };
    }
    
    return {
      title: 'No items available',
      subtitle: 'Items will appear here once they are loaded for this customer',
      showClearButton: false,
    };
  };

  const { title, subtitle, showClearButton } = getEmptyMessage();

  return (
    <View style={orderEmptyStateStyles.emptyContainer}>
      <Avatar.Icon
        size={64}
        icon="package-variant"
        style={orderEmptyStateStyles.emptyIcon}
      />
      <Text style={orderEmptyStateStyles.emptyText}>{title}</Text>
      <Text style={orderEmptyStateStyles.emptySubText}>{subtitle}</Text>
      
      {showClearButton && onClearFilters && (
        <Button
          mode="outlined"
          onPress={onClearFilters}
          style={orderEmptyStateStyles.clearButton}
          icon="filter-remove"
        >
          Clear All Filters
        </Button>
      )}
    </View>
  );
};

export default React.memo(OrderEmptyState);
