/**
 * Cache Service - General-purpose caching service with memory and AsyncStorage support
 */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiClient } from '../services/apiClient';
import { withErrorHandling } from './errors';

// Define cache keys as an enum for type safety
export enum CacheKey {
  CUSTOMERS = 'customers'
}

// INSTANT cache configuration - no TTL, cache forever until manual refresh

// Ultra-fast in-memory cache with aggressive caching
interface CacheEntry {
  data: any;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

const memoryCache: Map<string, CacheEntry> = new Map();

// Larger memory cache for ultra-fast access (no pagination limits)
const MAX_MEMORY_CACHE_SIZE = 200;

export const cacheService = {
  // INSTANT cache storage - no TTL, just store forever
  async setData<T>(key: CacheKey, data: T): Promise<void> {
    try {
      const cacheKey = `cache_${key}`;
      const now = Date.now();

      // Store in memory cache for INSTANT future access
      memoryCache.set(key, {
        data,
        timestamp: now,
        accessCount: 1,
        lastAccessed: now
      });

      // Store in AsyncStorage for persistence (no timestamp needed)
      await AsyncStorage.setItem(cacheKey, JSON.stringify(data));

      // Manage memory cache size with LRU eviction
      this.manageMemoryCacheSize();
    } catch (error) {
      // Silent error handling in production
    }
  },

  // INSTANT cache retrieval - no TTL, cache forever until manual refresh
  async getData<T>(key: CacheKey): Promise<T | null> {
    try {
      // Check memory cache first for INSTANT access
      const cacheEntry = memoryCache.get(key);
      if (cacheEntry) {
        // Update access statistics for LRU
        cacheEntry.accessCount++;
        cacheEntry.lastAccessed = Date.now();
        return cacheEntry.data as T;
      }

      const cacheKey = `cache_${key}`;

      // Get from AsyncStorage (no TTL check - cache forever)
      const data = await AsyncStorage.getItem(cacheKey);
      if (!data) return null;

      try {
        // Parse and store in memory cache for future INSTANT access
        const parsedData = JSON.parse(data) as T;
        const now = Date.now();
        memoryCache.set(key, {
          data: parsedData,
          timestamp: now,
          accessCount: 1,
          lastAccessed: now
        });
        return parsedData;
      } catch (parseError) {
        // If JSON parsing fails, clear invalid cache
        this.clearCache(key);
        return null;
      }
    } catch (error) {
      return null;
    }
  },

  async fetchWithCache<T>(
    key: CacheKey,
    fetchFunction: () => Promise<T>,
    forceRefresh = false
  ): Promise<T> {
    if (!forceRefresh) {
      const cachedData = await this.getData<T>(key);
      if (cachedData) {
        return cachedData;
      }
    }

    const data = await fetchFunction();
    await this.setData(key, data);
    return data;
  },

  async preloadCriticalData(): Promise<void> {
    try {
      const preloadPromises = [
        this.preloadCustomers(),
        this.preloadUserSettings(),
        this.preloadFrequentData()
      ];

      Promise.allSettled(preloadPromises).then((results) => {
        const failed = results.filter(r => r.status === 'rejected').length;
        if (failed > 0 && __DEV__) {
          console.warn(`Preload: ${failed} operations failed`);
        }
      });
    } catch (error) {
      // Silent fail
    }
  },

  async preloadCustomers(): Promise<void> {
    try {
      const { fetchCustomers } = await import('../services/customerService');
      await this.fetchWithCache(CacheKey.CUSTOMERS, fetchCustomers, false);
    } catch (error) {
      // Silent fail
    }
  },

  async preloadUserSettings(): Promise<void> {
    try {
      const settingsKeys = [CacheKey.CUSTOMERS];
      const promises = settingsKeys.map(key =>
        this.getData(key).catch(() => null)
      );
      await Promise.allSettled(promises);
    } catch (error) {
      // Silent fail
    }
  },

  async preloadFrequentData(): Promise<void> {
    try {
      const frequentKeys = [CacheKey.CUSTOMERS];
      const promises = frequentKeys.map(key =>
        this.getData(key).catch(() => null)
      );
      await Promise.allSettled(promises);
    } catch (error) {
      // Silent fail
    }
  },

  // 🚀 SMART CACHE WARMING - Prepare cache for anticipated user actions
  async warmCache(keys: CacheKey[]): Promise<void> {
    const promises = keys.map(async (key) => {
      try {
        // Check if already cached
        const cached = await this.getData(key);
        if (cached) return; // Already warm

        // Warm cache based on key type
        switch (key) {
          case CacheKey.CUSTOMERS:
            const { fetchCustomers } = await import('../services/customerService');
            await this.fetchWithCache(key, fetchCustomers, false);
            break;
          // Add more cache warming strategies as needed
        }
      } catch (error) {
        // Silent fail for cache warming
      }
    });

    await Promise.allSettled(promises);
  },

  // Legacy method - kept for backward compatibility
  async fetchAndCache<T>(key: CacheKey, endpoint: string, forceRefresh = false): Promise<T> {
    return withErrorHandling(async () => {
      // Try cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedData = await this.getData<T>(key);
        if (cachedData) return cachedData;
      }

      // Fetch from API
      const response = await apiClient.get(endpoint);
      const data = response?.data?.message || [];

      // Cache the data
      await this.setData(key, data);

      return data;
    });
  },

  // Clear cache for a specific key (INSTANT cleanup)
  async clearCache(key: CacheKey): Promise<void> {
    try {
      const cacheKey = `cache_${key}`;

      // Clear memory cache and AsyncStorage (no timestamp to clean)
      memoryCache.delete(key);
      await AsyncStorage.removeItem(cacheKey);
    } catch (error) {
      // Silent error handling
    }
  },

  // Clear all cached data
  async clearAllCache(): Promise<void> {
    try {
      // Get all keys and filter for cache keys
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));

      // Clear enhanced memory cache
      memoryCache.clear();

      // Clear AsyncStorage
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
    } catch (error) {
      // Silent error handling
    }
  },

  // Enhanced memory cache management with LRU eviction
  manageMemoryCacheSize(): void {
    if (memoryCache.size > MAX_MEMORY_CACHE_SIZE) {
      // Sort by last accessed time and access count for LRU eviction
      const entries = Array.from(memoryCache.entries());
      entries.sort(([, a], [, b]) => {
        // First sort by access count (ascending), then by last accessed (ascending)
        if (a.accessCount !== b.accessCount) {
          return a.accessCount - b.accessCount;
        }
        return a.lastAccessed - b.lastAccessed;
      });

      // Remove least recently used entries
      const entriesToRemove = entries.slice(0, entries.length - MAX_MEMORY_CACHE_SIZE);
      entriesToRemove.forEach(([key]) => {
        memoryCache.delete(key);
      });
    }
  }
};
