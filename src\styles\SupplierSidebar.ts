import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const supplierSidebarStyles = StyleSheet.create({
  sidebarContainer: {
    width: 65, // Reduced from 70 to optimize space
    backgroundColor: NEUTRAL.WHITE,
    borderRightWidth: 1,
    borderRightColor: MD.DIVIDER,
    flexDirection: 'column',
  },
  sidebarContent: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: MD.SPACING.XSMALL,
  },
  supplierTab: {
    position: 'relative',
    paddingVertical: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.XXSMALL / 2, // Reduced horizontal padding
    marginHorizontal: MD.SPACING.XXSMALL / 2, // Reduced horizontal margin
    marginVertical: MD.SPACING.XXSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    backgroundColor: 'transparent',
    minHeight: 50,
    justifyContent: 'center',
  },
  selectedSupplierTab: {
    backgroundColor: BRAND.PRIMARY_50,
  },
  tabContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  supplierTabText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // 12px - better balance for readability
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.XXSMALL,
    lineHeight: 16, // Adjusted for better spacing
    width: '100%',
  },
  selectedSupplierTabText: {
    color: BRAND.PRIMARY,
    fontWeight: '700',
  },
  supplierCount: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize, // 10px - smaller for count badge
    fontWeight: '600',
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    paddingHorizontal: MD.SPACING.XXSMALL,
    paddingVertical: 1,
    borderRadius: MD.BORDER_RADIUS.XSMALL,
    minWidth: 18,
    overflow: 'hidden',
  },
  selectedSupplierCount: {
    color: BRAND.PRIMARY,
    backgroundColor: BRAND.PRIMARY_10,
    fontWeight: '700',
  },
  activeIndicator: {
    position: 'absolute',
    right: 0, // Moved to right side for better visibility
    top: 0,
    bottom: 0, // Span full height of the card
    width: 3,
    backgroundColor: BRAND.PRIMARY,
    borderTopLeftRadius: MD.BORDER_RADIUS.XSMALL, // Changed to left radius since it's on the right
    borderBottomLeftRadius: MD.BORDER_RADIUS.XSMALL,
  },
  // Divider between supplier tabs
  supplierDivider: {
    height: 1,
    backgroundColor: MD.DIVIDER,
    marginHorizontal: MD.SPACING.XSMALL,
    opacity: 0.5,
  },
});
