/**
 * FloatingTabBar - World-class floating tab bar component
 *
 * Features:
 * - Glassmorphism floating pill design
 * - Smooth icon scaling animations
 * - Dynamic icon and color transitions
 * - Responsive design that adapts to screen size
 * - Clean, modern aesthetic without background highlights
 */

import React, { useRef, useEffect, useCallback, useMemo, useState } from 'react';
import {
  View,
  TouchableOpacity,
  Animated,
  Keyboard,
} from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BRAND } from '../constants/colors';
import { floatingTabBarStyles as styles } from '../styles/FloatingTabBar';

interface TabItem {
  key: string;
  title: string;
  icon: any;
  iconFocused: any;
}

interface FloatingTabBarProps {
  state: any;
  navigation: any;
  tabs: TabItem[];
}

const FloatingTabBar: React.FC<FloatingTabBarProps> = ({
  state,
  navigation,
  tabs,
}) => {
  const insets = useSafeAreaInsets();
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // Animation values - memoized to prevent recreation
  const scaleAnimations = useMemo(
    () => tabs.map(() => new Animated.Value(1)),
    [tabs.length] // Only recreate if number of tabs changes
  );

  // Animation value for hiding/showing tab bar
  const tabBarAnimation = useRef(new Animated.Value(1)).current;

  // Calculate tab bar dimensions
  const bottomOffset = Math.max(insets.bottom + 25, 35);

  // Keyboard event listeners with improved animation
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setIsKeyboardVisible(true);
        // Use spring animation for smoother feel
        Animated.spring(tabBarAnimation, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setIsKeyboardVisible(false);
        // Delay the show animation slightly to avoid flickering
        setTimeout(() => {
          Animated.spring(tabBarAnimation, {
            toValue: 1,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }, 50);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [tabBarAnimation]);

  useEffect(() => {
    const activeIndex = state.index;

    const animationPromises = scaleAnimations.map((anim, index) => {
      return new Promise<void>((resolve) => {
        Animated.spring(anim, {
          toValue: index === activeIndex ? 1.1 : 1,
          useNativeDriver: true,
          tension: 200,
          friction: 10,
          velocity: 0,
          restDisplacementThreshold: 0.01,
          restSpeedThreshold: 0.01,
        }).start(() => resolve());
      });
    });

    Promise.all(animationPromises).then(() => {});
  }, [state.index, scaleAnimations]);

  // Memoize scroll functions mapping to prevent recreation on every render
  // Each screen gets its own unique global function name to avoid conflicts
  const scrollFunctions = useMemo(() => ({
    'customers': 'customerScreenScrollToTop',
    'orders': 'ordersScreenScrollToTop',
    'invoices': 'invoicesScreenScrollToTop',
    'carts': 'cartsScreenScrollToTop',
  }), []);

  // Cache global scroll functions for faster access
  const scrollFunctionCache = useRef<{ [key: string]: (() => void) | null }>({});

  // Optimize tab press handler with useCallback
  const handleTabPress = useCallback((route: { name: keyof typeof scrollFunctions; key: string }, index: number) => {
    const isFocused = state.index === index;

    // If the tab is already focused, scroll to top
    if (isFocused) {
      const scrollFunctionName = scrollFunctions[route.name];
      if (scrollFunctionName) {
        // Use cached function or get from global
        let scrollFunction = scrollFunctionCache.current[scrollFunctionName];
        if (!scrollFunction) {
          scrollFunction = (global as any)[scrollFunctionName];
          if (scrollFunction) {
            scrollFunctionCache.current[scrollFunctionName] = scrollFunction;
          }
        }

        if (scrollFunction) {
          if (__DEV__) console.log(`🚀 Calling scroll function: ${scrollFunctionName} for tab: ${route.name}`);
          // Use requestAnimationFrame for smoother scroll
          requestAnimationFrame(() => {
            try {
              scrollFunction!();
            } catch (error) {
              if (__DEV__) console.warn(`Error calling scroll function for ${route.name}:`, error);
              // Clear the cached function if it fails
              scrollFunctionCache.current[scrollFunctionName] = null;
            }
          });
        } else {
          // Debug: Log when scroll function is not available
          if (__DEV__) console.log(`❌ Scroll function ${scrollFunctionName} not available for ${route.name}`);

          // Try to get the function again after a short delay (screen might still be mounting)
          setTimeout(() => {
            const retryFunction = (global as any)[scrollFunctionName];
            if (retryFunction) {
              if (__DEV__) console.log(`Retry successful for ${scrollFunctionName}`);
              scrollFunctionCache.current[scrollFunctionName] = retryFunction;
              try {
                retryFunction();
              } catch (error) {
                if (__DEV__) console.warn(`Error on retry for ${route.name}:`, error);
              }
            } else {
              if (__DEV__) console.warn(`Scroll function ${scrollFunctionName} still not available after retry`);
            }
          }, 100);
        }
      }
      return;
    }

    // Optimize navigation with immediate response
    const event = navigation.emit({
      type: 'tabPress',
      target: route.key,
      canPreventDefault: true,
    });

    if (!event.defaultPrevented) {
      navigation.navigate(route.name);
    }
  }, [state.index, navigation, scrollFunctions]);

  // Memoize tab items to prevent unnecessary re-renders
  const tabItems = useMemo(() => {
    return tabs.map((tab, index) => {
      const route = state.routes[index];
      const isFocused = state.index === index;

      return (
        <TouchableOpacity
          key={tab.key}
          style={styles.tabItem}
          onPress={() => handleTabPress(route, index)}
          activeOpacity={0.7}
          // Add hit slop for better touch response
          hitSlop={{ top: 10, bottom: 10, left: 5, right: 5 }}
        >
          <Animated.View
            style={[
              styles.tabContent,
              { transform: [{ scale: scaleAnimations[index] }] },
            ]}
          >
            <MaterialCommunityIcons
              name={isFocused ? tab.iconFocused : tab.icon}
              size={24}
              color={isFocused ? BRAND.PRIMARY : 'rgba(0, 0, 0, 0.4)'}
            />
            <Animated.Text
              style={[
                styles.tabLabel,
                { color: isFocused ? BRAND.PRIMARY : 'rgba(0, 0, 0, 0.4)' },
              ]}
            >
              {tab.title}
            </Animated.Text>
          </Animated.View>
        </TouchableOpacity>
      );
    });
  }, [tabs, state.routes, state.index, scaleAnimations, handleTabPress]);

  return (
    <Animated.View
      style={[
        styles.container,
        {
          bottom: bottomOffset,
          opacity: tabBarAnimation,
          transform: [
            {
              translateY: tabBarAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [150, 0], // Increased distance to ensure complete hiding
              }),
            },
            {
              scale: tabBarAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1], // Add scale animation for smoother effect
              }),
            },
          ],
        }
      ]}
      pointerEvents={isKeyboardVisible ? 'none' : 'auto'} // Disable touch when hidden
    >
      <View style={styles.solidContainer}>
        <View style={styles.tabsContainer}>
          {tabItems}
        </View>
      </View>
    </Animated.View>
  );
};

export default FloatingTabBar;
