import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const cartHeaderStyles = StyleSheet.create({
  header: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  subtitle: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: MD.SPACING.XSMALL,
  },
});
