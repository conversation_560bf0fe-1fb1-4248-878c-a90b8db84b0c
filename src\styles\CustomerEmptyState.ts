import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const customerEmptyStateStyles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyIcon: {
    marginBottom: MD.SPACING.LARGE,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL,
  },
  emptySubText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: MD.SPACING.LARGE,
  },
  clearButton: {
    marginTop: MD.SPACING.MEDIUM,
    borderColor: BRAND.PRIMARY,
  },
});
