import React, { memo, useMemo } from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Transaction } from '../types/business';
import { formatCurrency, formatDate } from '../utils/formatting';
import { getTransactionDate, getStatusColor, isSalesInvoice } from '../utils/transactions';
import { TRANSACTION } from '../constants/business';
import { transactionCardStyles } from '../styles/TransactionCard';
import { generateAccessibilityLabel, getAccessibilityHint } from '../utils/ui';

interface TransactionCardProps {
  item: Transaction;
  transactionType: 'order' | 'invoice';
  onPress: (id: string) => void;
  onLongPress?: (id: string) => void;
  onStatusPress?: (status: string) => void;
}

const TransactionCardBase: React.FC<TransactionCardProps> = (props) => {
  const { item, onPress, onLongPress, onStatusPress } = props;

  // Safety check for item
  if (!item) {
    return null;
  }

  // Memoized calculations for better performance
  const { statusColor, date, transactionTypeLabel, mainAccessibilityLabel, accessibilityHint } = useMemo(() => {
    const color = getStatusColor(item.status || 'Draft', TRANSACTION.STATUS_COLORS);
    const transactionDate = getTransactionDate(item);
    const typeLabel = props.transactionType === 'order' ? 'Order' : 'Invoice';

    const label = generateAccessibilityLabel(
      `${typeLabel} ${item.name || 'N/A'}`,
      [
        `Customer: ${item.customer_name || 'Unknown Customer'}`,
        `Status: ${item.status || 'Draft'}`,
        `Amount: ${formatCurrency(item.base_grand_total || 0)}`,
        `Date: ${formatDate(transactionDate)}`
      ]
    );

    const hint = getAccessibilityHint(`view ${typeLabel.toLowerCase()} details`);

    return {
      statusColor: color,
      date: transactionDate,
      transactionTypeLabel: typeLabel,
      mainAccessibilityLabel: label,
      accessibilityHint: hint,
    };
  }, [item.status, item.name, item.customer_name, item.base_grand_total, props.transactionType]);

  // Memoized handlers
  const handlePress = React.useCallback(() => {
    if (item?.name) {
      onPress(item.name);
    }
  }, [onPress, item?.name]);

  const handleLongPress = React.useCallback(() => {
    if (!item?.name) return;

    if (onLongPress) {
      onLongPress(item.name);
    } else {
      onPress(item.name);
    }
  }, [onLongPress, onPress, item?.name]);

  const handleStatusPress = React.useCallback(() => {
    if (onStatusPress && item?.status) {
      onStatusPress(item.status);
    }
  }, [onStatusPress, item?.status]);

  return (
    <TouchableOpacity
      style={transactionCardStyles.listItem}
      onPress={handlePress}
      onLongPress={handleLongPress}
      activeOpacity={0.7}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={mainAccessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityState={{
        selected: false,
        disabled: false
      }}
    >
      <View style={transactionCardStyles.cardContent}>
        {/* Transaction ID and Date */}
        <View style={transactionCardStyles.cardHeader}>
          <View style={transactionCardStyles.idContainer}>
            <Text style={transactionCardStyles.id}>#{item.name || 'N/A'}</Text>
          </View>
          <Text style={transactionCardStyles.date}>{formatDate(date)}</Text>
        </View>

        {/* Customer name */}
        <Text style={transactionCardStyles.customer} numberOfLines={1} ellipsizeMode="tail">
          {item.customer_name || 'Unknown Customer'}
        </Text>

        {/* Status and Amount */}
        <View style={transactionCardStyles.statusAmountContainer}>
          <TouchableOpacity
            onPress={handleStatusPress}
            disabled={!onStatusPress}
            accessible={!!onStatusPress}
            accessibilityRole={onStatusPress ? "button" : "text"}
            accessibilityLabel={`Status: ${item.status || 'Draft'}`}
            accessibilityHint={onStatusPress ? getAccessibilityHint(`filter by ${item.status || 'Draft'} status`) : undefined}
            accessibilityState={{
              disabled: !onStatusPress
            }}
          >
            <View style={[transactionCardStyles.statusChip, { backgroundColor: `${statusColor}20` }]}>
              <View style={[transactionCardStyles.statusIndicator, { backgroundColor: statusColor }]} />
              <Text style={[transactionCardStyles.statusText, { color: statusColor }]}>{item.status || 'Draft'}</Text>
            </View>
          </TouchableOpacity>
          <View style={transactionCardStyles.amountContainer}>
            {isSalesInvoice(item) && item.outstanding_amount > 0 ? (
              <View style={transactionCardStyles.amountRow}>
                <Text style={transactionCardStyles.outstandingAmount}>
                  {formatCurrency(item.outstanding_amount)}
                </Text>
                <Text style={transactionCardStyles.separator}>|</Text>
                <Text style={transactionCardStyles.amount}>{formatCurrency(item.base_grand_total || 0)}</Text>
              </View>
            ) : (
              <Text style={transactionCardStyles.amount}>{formatCurrency(item.base_grand_total || 0)}</Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// Styles are in TransactionCard.ts following one-to-one mapping

// Memoize the component with a custom comparison function for better performance
const TransactionCard = React.memo(TransactionCardBase, (prevProps, nextProps) => {
  // Safety checks for undefined items
  const prevItem = prevProps.item;
  const nextItem = nextProps.item;

  if (!prevItem || !nextItem) {
    return prevItem === nextItem;
  }

  // Check if both items are invoices with outstanding_amount
  const prevOutstanding = isSalesInvoice(prevItem) ? prevItem.outstanding_amount : undefined;
  const nextOutstanding = isSalesInvoice(nextItem) ? nextItem.outstanding_amount : undefined;

  return (
    prevItem.name === nextItem.name &&
    prevItem.status === nextItem.status &&
    prevItem.base_grand_total === nextItem.base_grand_total &&
    prevOutstanding === nextOutstanding &&
    prevProps.transactionType === nextProps.transactionType
  );
});

export default TransactionCard;
