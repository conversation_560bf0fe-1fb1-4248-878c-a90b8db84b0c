import React, { useEffect, useCallback, useMemo, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Keyboard,
  Animated,
  StatusBar,
  RefreshControl,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocalSearchParams, useFocusEffect, router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { IconButton, useTheme } from 'react-native-paper';

import FloatingButtons from '../src/components/FloatingButtons';
import PageHeader from '../src/components/PageHeader';
import { CartItem, OrderItem } from '../src/types/business';
import OrderCartItem from '../src/components/OrderCartItem';
import UnifiedSidebar from '../src/components/UnifiedSidebar';
import FilterTabs from '../src/components/FilterTabs';
import CheckoutButton from '../src/components/CheckoutButton';
import OrderEmptyState from '../src/components/OrderEmptyState';

import { useCartManager } from '../src/hooks/useCartManager';
import { useCreateOrderData } from '../src/hooks/useCreateOrderData';
import { useCreateOrderNavigation } from '../src/hooks/useCreateOrderNavigation';
import { calculateCartTotalWithLookup } from '../src/orchestrators/cart';
import { HIT_SLOP, LIST_CONFIG } from '../src/constants/ui';
import { BUSINESS_RULES } from '../src/constants/config';
import { createOrderScreenStyles } from '../src/styles/CreateOrderScreen';



const CreateOrderScreen = () => {
  // Get params with territory and group that were passed from the customers screen
  const params = useLocalSearchParams();
  const customerId = params.customerId as string;
  const customerName = params.customerName as string;
  const paramTerritory = params.territory as string;
  const paramCustomerGroup = params.customerGroup as string;

  // Refresh state for pull-to-refresh
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Theme for consistent styling
  const theme = useTheme();



  // Use custom hooks for cart management
  const {
    cartItems,
    setCartItems,
    cartCount,
    saveCartData,
    clearCart,
    cartCacheKey
  } = useCartManager(customerId, customerName);

  // Use the data fetching hook
  const {
    items,
    loading,
    error,
    availableSuppliers,
    supplierCounts,
    availableBrands,
    brandCounts,
    availableItemGroups,
    itemGroupCounts,
    fetchItemsList,
    setItems
  } = useCreateOrderData({
    customerId,
    customerTerritory: paramTerritory || '',
    customerGroup: paramCustomerGroup || '',
    paramTerritory,
    paramCustomerGroup,
    cartCacheKey
  });



  // Optimized filter state with debounced search
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<'supplier' | 'brand' | 'itemGroup'>('brand'); // Default to Brand tab
  const [selectedValue, setSelectedValue] = useState<string | null>(null);

  // Temporary UOM display for items with qty = 0
  const [tempUomDisplay, setTempUomDisplay] = useState<Record<string, string>>({});

  // Refs to prevent stale closure issues
  const itemsRef = useRef(items);
  const tempUomDisplayRef = useRef(tempUomDisplay);
  useEffect(() => {
    itemsRef.current = items;
    tempUomDisplayRef.current = tempUomDisplay;
  }, [items, tempUomDisplay]);

  // Debounce search query for better performance - reduced delay for responsiveness
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 200); // Reduced from 300ms to 200ms for better responsiveness
    return () => clearTimeout(timer);
  }, [searchQuery]);

  const searchProps = {
    value: searchQuery,
    onChangeText: setSearchQuery,
    placeholder: 'Item, Supplier, Brand, Group'
  };

  const handleFilterTypeChange = useCallback((type: 'supplier' | 'brand' | 'itemGroup') => {
    // AGGRESSIVE: Use React.startTransition for immediate UI response
    React.startTransition(() => {
      setFilterType(type);
      setSelectedValue(null);
    });
  }, []);

  const handleValueSelect = useCallback((value: string | null) => {
    // AGGRESSIVE: Use React.startTransition for immediate UI response
    React.startTransition(() => {
      setSelectedValue(value);
    });
  }, []);

  // Clear all filters function
  const handleClearFilters = useCallback(() => {
    setSearchQuery('');
    setDebouncedSearchQuery('');
    setSelectedValue(null);
    setFilterType('brand'); // Reset to default filter type (Brand)
  }, []);

  // Create optimized items lookup map
  const itemsLookupMap = useMemo(() => {
    const map = new Map<string, OrderItem>();
    items.forEach(item => map.set(item.item_code, item));
    return map;
  }, [items]);

  // AGGRESSIVE: Ultra-fast filter processing with immediate response for non-search filters
  const filteredItems = useMemo(() => {
    if (items.length === 0) return [];

    const hasSearch = debouncedSearchQuery.trim();
    const hasFilter = selectedValue;

    // If no filters, return original array (avoid unnecessary copying)
    if (!hasSearch && !hasFilter) return items;

    // AGGRESSIVE: For filter-only changes (no search), process immediately
    if (!hasSearch && hasFilter) {
      // Use faster property access for filter-only operations
      switch (filterType) {
        case 'supplier':
          return items.filter(item => item.supplier_short_name === selectedValue);
        case 'brand':
          return items.filter(item => item.brand === selectedValue);
        case 'itemGroup':
          return items.filter(item => item.item_group === selectedValue);
        default:
          return items;
      }
    }

    // For search + filter combinations, use optimized single-pass
    const searchTerm = debouncedSearchQuery.toLowerCase().trim();

    return items.filter(item => {
      // Apply search filter first (most selective)
      const matchesSearch =
        item.item_name.toLowerCase().includes(searchTerm) ||
        item.supplier_short_name.toLowerCase().includes(searchTerm) ||
        item.brand.toLowerCase().includes(searchTerm) ||
        item.item_group.toLowerCase().includes(searchTerm);

      if (!matchesSearch) return false;

      // Apply category filter if present
      if (hasFilter) {
        switch (filterType) {
          case 'supplier':
            return item.supplier_short_name === selectedValue;
          case 'brand':
            return item.brand === selectedValue;
          case 'itemGroup':
            return item.item_group === selectedValue;
          default:
            return false;
        }
      }

      return true;
    });
  }, [items, debouncedSearchQuery, selectedValue, filterType]);

  // Get current filter data based on filter type
  const getCurrentFilterData = useMemo(() => {
    switch (filterType) {
      case 'supplier':
        return {
          availableItems: availableSuppliers,
          itemCounts: supplierCounts
        };
      case 'brand':
        return {
          availableItems: availableBrands,
          itemCounts: brandCounts
        };
      case 'itemGroup':
        return {
          availableItems: availableItemGroups,
          itemCounts: itemGroupCounts
        };
      default:
        return {
          availableItems: [],
          itemCounts: {}
        };
    }
  }, [filterType, availableSuppliers, supplierCounts, availableBrands, brandCounts, availableItemGroups, itemGroupCounts]);

  // Memoize FilterTabs props - ALWAYS show tabs immediately, even during loading
  const filterTabsProps = useMemo(() => ({
    selectedFilterType: filterType,
    onFilterTypeChange: handleFilterTypeChange,
    supplierCount: loading ? 0 : availableSuppliers.length, // Show 0 during loading, update when ready
    brandCount: loading ? 0 : availableBrands.length,
    itemGroupCount: loading ? 0 : availableItemGroups.length
  }), [
    filterType,
    handleFilterTypeChange,
    loading, // Add loading to dependencies
    availableSuppliers.length,
    availableBrands.length,
    availableItemGroups.length
  ]);

  // Optimized UnifiedSidebar props to prevent unnecessary re-renders
  const unifiedSidebarProps = useMemo(() => ({
    selectedValue,
    availableItems: getCurrentFilterData.availableItems,
    itemCounts: getCurrentFilterData.itemCounts,
    onItemSelect: handleValueSelect,
    loading: loading && items.length === 0 // Only show skeleton on initial load
  }), [
    selectedValue,
    getCurrentFilterData.availableItems,
    getCurrentFilterData.itemCounts,
    handleValueSelect,
    loading,
    items.length
  ]);

  // Use the navigation management hook
  const {
    handleCheckout,
    handleGoToCart,
    setupBackHandler
  } = useCreateOrderNavigation({
    customerId,
    customerName,
    cartItems,
    items,
    params,
    saveCartData
  });

  const insets = useSafeAreaInsets();

  // Simple refs
  const flatListRef = useRef<FlatList<CartItem> | null>(null);
  const scrollY = useRef(new Animated.Value(0)).current;

  // AGGRESSIVE: Immediate UI render + background data loading
  useEffect(() => {
    const removeBackHandler = setupBackHandler();

    // Start data loading immediately in background - don't block UI
    if (items.length === 0) {
      // Use requestIdleCallback for non-blocking data fetch
      const loadData = () => {
        try {
          fetchItemsList();
        } catch (error) {
          // Error handled by hook
        }
      };

      // Load data immediately but don't block render
      if (typeof requestIdleCallback !== 'undefined') {
        requestIdleCallback(loadData, { timeout: 100 });
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(loadData, 0);
      }
    }

    return () => {
      removeBackHandler();
    };
  }, [customerId, setupBackHandler, fetchItemsList]);

  // Simple cart loading from AsyncStorage - checkout now keeps it updated
  useFocusEffect(
    React.useCallback(() => {
      const loadCartData = async () => {
        try {
          const cachedCartString = await AsyncStorage.getItem(cartCacheKey);
          if (cachedCartString) {
            const parsedCart = JSON.parse(cachedCartString) as CartItem[];

            // Ensure all items have orderIndex assigned (for backward compatibility)
            const cartWithOrderIndex = parsedCart.map((item, index) => ({
              ...item,
              orderIndex: item.orderIndex || (index + 1)
            }));

            setCartItems(cartWithOrderIndex);

            // Save back to AsyncStorage if we added orderIndex to any items (non-blocking)
            const needsUpdate = parsedCart.some(item => !item.orderIndex);
            if (needsUpdate) {
              AsyncStorage.setItem(cartCacheKey, JSON.stringify(cartWithOrderIndex)).catch(() => {
                // Silent fail for background save
              });
            }
          } else {
            setCartItems([]);
          }
        } catch (error) {
          if (__DEV__) console.warn('Error loading cart:', error);
          setCartItems([]);
        }
      };

      loadCartData();
    }, [cartCacheKey, setCartItems])
  );

  // AGGRESSIVE: Instant scroll to top when filter changes (no animation)
  useEffect(() => {
    // Use requestAnimationFrame for immediate but smooth scroll
    requestAnimationFrame(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
    });
  }, [filterType, selectedValue]);





  // Pull-to-refresh handler
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Import the quantity sync utility dynamically
      const { quantitySyncManager } = await import('../src/utils/quantitySync');

      // Force refresh the quantity cache
      await quantitySyncManager.forceRefresh();

      // Reload the items data to get fresh quantities
      await fetchItemsList();

      if (__DEV__) console.log('Data refreshed successfully');
    } catch (error) {
      if (__DEV__) console.warn('Failed to refresh data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [fetchItemsList]);

  // Using fresh item lookups for better performance and reliability

  // Optimized updateQuantity with immediate UI update and AsyncStorage save
  const enhancedUpdateQuantity = useCallback(async (itemCode: string, quantityOrFn: number | ((prevQty: number) => number)) => {
    try {
      let updatedItems: CartItem[] = [];

      // Update local state immediately for responsive UI
      setCartItems(prevItems => {
        const existingItemIndex = prevItems.findIndex(item => item.item_code === itemCode);
        let newItems = [...prevItems];

        if (existingItemIndex >= 0) {
          // Update existing item
          const currentQuantity = newItems[existingItemIndex].quantity;
          const newQuantity = typeof quantityOrFn === 'function' ? quantityOrFn(currentQuantity) : quantityOrFn;

          if (newQuantity <= 0) {
            // Remove item from cart
            newItems = newItems.filter(item => item.item_code !== itemCode);

          } else {
            // Update quantity
            // Cart quantity updated successfully
            newItems[existingItemIndex] = { ...newItems[existingItemIndex], quantity: newQuantity };
          }
        } else {
          // Add new item to cart - use ref to get latest items (prevents stale closure)
          const itemDetails = itemsRef.current.find(item => item.item_code === itemCode);
          if (!itemDetails) {
            if (__DEV__) console.warn('Item not found:', itemCode);
            return prevItems;
          }

          const newQuantity = typeof quantityOrFn === 'function' ? quantityOrFn(0) : quantityOrFn;
          if (newQuantity <= 0) return prevItems;

          // Calculate the next orderIndex to maintain order of addition
          const maxOrderIndex = newItems.reduce((max, item) => Math.max(max, item.orderIndex || 0), 0);

          // Use currently displayed UOM or default to first UOM
          const currentTempUom = tempUomDisplayRef.current[itemCode];
          const currentDisplayUom = currentTempUom || itemDetails.uoms[0]?.uom || '';
          const selectedUomData = itemDetails.uoms.find(uom => uom.uom === currentDisplayUom) || itemDetails.uoms[0];



          const newCartItem: CartItem = {
            item_code: itemCode,
            item_name: itemDetails.item_name,
            item_group: itemDetails.item_group,
            quantity: newQuantity,
            selectedUom: selectedUomData.uom,
            conversionFactor: selectedUomData.conversion_factor,
            rate: selectedUomData.rate,
            orderIndex: maxOrderIndex + 1,
            customerId,
            customerName
          };

          // Item added to cart successfully
          newItems.push(newCartItem);
        }

        updatedItems = newItems;
        return newItems;
      });

      // Save to AsyncStorage immediately to ensure checkout sees the data
      try {
        if (updatedItems.length > 0) {
          await AsyncStorage.setItem(cartCacheKey, JSON.stringify(updatedItems));
        } else {
          await AsyncStorage.removeItem(cartCacheKey);
        }
      } catch (error) {
        if (__DEV__) console.warn('Cart save failed:', error);
      }



      // Cart state update is sufficient for re-render

    } catch (error) {
      if (__DEV__) console.warn('Error updating cart:', error);
    }
  }, [customerId, customerName, setCartItems, cartCacheKey]); // Using refs to prevent stale closures

  // Memoized cart items map for quick lookup - prevents unnecessary re-renders
  const cartItemsMap = useMemo(() => {
    const map: Record<string, CartItem> = {};
    cartItems.forEach(item => {
      map[item.item_code] = item;
    });
    return map;
  }, [cartItems]);

  // Cart state is working correctly



  // Optimized display items with memoized filtering
  const displayItems = useMemo(() => {
    if (filteredItems.length === 0) return [];

    // Convert to display format with cart quantities
    return filteredItems.map(item => {
      const cartItem = cartItemsMap[item.item_code];
      const displayQuantity = cartItem?.quantity || 0;

      // Display quantity correctly from cart state

      // Use cart UOM if in cart, otherwise use temp display UOM, otherwise default to first UOM
      const preferredUom = cartItem?.selectedUom || tempUomDisplay[item.item_code] || item.uoms[0]?.uom || '';
      const uomData = item.uoms.find((uom: any) => uom.uom === preferredUom) || item.uoms[0];

      return {
        item_code: item.item_code,
        item_name: item.item_name,
        item_group: item.item_group,
        quantity: displayQuantity,
        selectedUom: preferredUom,
        conversionFactor: uomData?.conversion_factor || 1,
        rate: uomData?.rate || 0,
        customerId,
        customerName
      };
    });
  }, [filteredItems, cartItemsMap, tempUomDisplay, customerId, customerName]);

  // UOM change handler with optimized lookup
  const changeUom = useCallback((itemCode: string) => {
    // Use lookup map for O(1) performance
    const item = itemsLookupMap.get(itemCode);
    if (!item || !item.uoms || item.uoms.length <= 1) return;

    // Check if item is in cart
    const cartItemIndex = cartItems.findIndex(cartItem => cartItem.item_code === itemCode);

    if (cartItemIndex >= 0) {
      // Item is in cart (qty > 0) - update cart item UOM directly
      setCartItems(prev => {
        const cartItem = prev[cartItemIndex];
        const currentUomIndex = item.uoms.findIndex((uom: any) => uom.uom === cartItem.selectedUom);
        const nextUomIndex = (currentUomIndex + 1) % item.uoms.length;
        const nextUom = item.uoms[nextUomIndex].uom;
        const nextConversionFactor = item.uoms[nextUomIndex].conversion_factor;
        const nextRate = item.uoms[nextUomIndex].rate;

        const newCartItems = [...prev];
        newCartItems[cartItemIndex] = {
          ...cartItem,
          selectedUom: nextUom,
          conversionFactor: nextConversionFactor,
          rate: nextRate
        };

        // Save to AsyncStorage for cart items
        const saveToStorage = async () => {
          try {
            await AsyncStorage.setItem(cartCacheKey, JSON.stringify(newCartItems));
          } catch (error) {
            if (__DEV__) console.warn('Failed to save cart after UOM change:', error);
          }
        };
        saveToStorage();

        return newCartItems;
      });
    } else {
      // Item not in cart (qty = 0) - just update temporary display UOM for price checking
      setTempUomDisplay(prev => {
        const currentUom = prev[itemCode] || item.uoms[0].uom;
        const currentUomIndex = item.uoms.findIndex((uom: any) => uom.uom === currentUom);
        const nextUomIndex = (currentUomIndex + 1) % item.uoms.length;
        const nextUom = item.uoms[nextUomIndex].uom;



        return {
          ...prev,
          [itemCode]: nextUom
        };
      });
    }
  }, [itemsLookupMap, cartItems, cartCacheKey]);



  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);





  // Optimized total cart amount calculation using lookup map
  const totalCartAmount = useMemo(() => {
    if (cartItems.length === 0) return 0;

    // Convert Map to Record for compatibility with calculateCartTotalWithLookup
    const itemsMap: Record<string, OrderItem> = {};
    itemsLookupMap.forEach((item, itemCode) => {
      itemsMap[itemCode] = item;
    });
    return calculateCartTotalWithLookup(cartItems, itemsMap);
  }, [cartItems, itemsLookupMap]);

  // Optimized item renderer with lookup map for O(1) performance
  const renderItem = useCallback(({ item }: { item: CartItem }) => {
    const itemDetails = itemsLookupMap.get(item.item_code);
    if (!itemDetails) return null;

    return (
      <OrderCartItem
        item={item}
        itemDetails={itemDetails}
        updateQuantity={enhancedUpdateQuantity}
        changeUom={changeUom}
        styles={styles}
      />
    );
  }, [itemsLookupMap, enhancedUpdateQuantity, changeUom, styles]);

  // Enhanced empty state with filter clearing functionality
  const renderEmptyState = useCallback(() => {
    if (loading || items.length === 0) {
      return null;
    }

    return (
      <OrderEmptyState
        searchQuery={debouncedSearchQuery}
        selectedValue={selectedValue}
        filterType={filterType}
        onClearFilters={handleClearFilters}
        loading={loading}
      />
    );
  }, [loading, items.length, debouncedSearchQuery, selectedValue, filterType, handleClearFilters]);

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      {/* Remove navigation overlay to prevent flickering */}

      {/* Header with enhanced PageHeader component */}
      <View style={styles.headerContainer}>
        <PageHeader
          title={customerName || 'Customer'}
          features={{
            search: searchProps,
            backButton: {
              onPress: () => {
                // INSTANT navigation - zero blocking operations
                const fromSource = params?.from;

                // Navigate immediately without any delays
                if (fromSource === 'cart') {
                  router.push('/(tabs)/carts');
                } else if (fromSource === 'reorder') {
                  router.push('/(tabs)/orders');
                } else {
                  router.push('/(tabs)/customers');
                }

                // Cart auto-saves on quantity changes, no need to save on navigation
                // This eliminates the AsyncStorage delay completely
              }
            },
            rightElements: (
              <View style={styles.headerRightElements}>
                {cartCount > 0 && (
                  <IconButton
                    icon="delete-outline"
                    size={24}
                    iconColor="#333"
                    onPress={clearCart}
                    style={styles.headerIcon}
                  />
                )}

                <TouchableOpacity
                  onPress={handleGoToCart}
                  style={styles.cartButton}
                  hitSlop={HIT_SLOP}
                >
                  <MaterialCommunityIcons name="cart" size={20} color="#333" />
                  {cartCount > 0 && (
                    <View style={[styles.cartBadge, cartCount > BUSINESS_RULES.CART_BADGE_WIDE_THRESHOLD ? styles.cartBadgeWide : null]}>
                      <Text style={styles.cartBadgeText}>{cartCount}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            )
          }}
        />
      </View>

      <View style={styles.content}>
        {/* Price Update Notice removed */}

        {/* Filter Tabs - Simple fixed tabs for filter selection */}
        <FilterTabs {...filterTabsProps} />

        {/* Modern Layout with Sidebar and Main Content */}
        <View style={styles.modernLayout}>
          {/* Left Sidebar with Unified Brand/Supplier Tabs */}
          <UnifiedSidebar {...unifiedSidebarProps} />

          {/* Main Content Area */}
          <View style={styles.mainContent}>
            {/* Show standard loading or content */}
            <View style={styles.listContainer}>
              {loading && items.length === 0 ? (
                // Standard loading pattern - consistent with other screens
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={theme.colors.primary} />
                  <Text style={styles.loadingText}>Loading items...</Text>
                </View>
              ) : error ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{error}</Text>
                  <TouchableOpacity onPress={() => {
                    setItems([]);
                    fetchItemsList();
                  }} style={styles.retryButton}>
                    <Text style={styles.retryButtonText}>Retry</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <>
                  {/* Always render content, even while loading */}
                  {/* Search Bar is now in the header */}

                  {/* Item List with FlatList - Optimized for Smooth Scrolling */}
                  <View style={styles.flashListContainer}>
                    <FlatList
                      ref={flatListRef}
                      data={displayItems}
                      renderItem={renderItem}
                      keyExtractor={(item: CartItem) => item.item_code}

                      // AGGRESSIVE: Ultra-responsive FlatList for instant filter changes
                      removeClippedSubviews={true}
                      maxToRenderPerBatch={12} // Larger batches for instant filter response
                      updateCellsBatchingPeriod={16} // 60fps updates for smooth filter changes
                      initialNumToRender={20} // More items for instant visibility
                      windowSize={10} // Larger window for smoother experience
                      disableVirtualization={false} // Keep virtualization for performance

                      // Remove getItemLayout since items may have varying heights
                      // This prevents jumping and flickering during scroll

                      contentContainerStyle={{
                        paddingHorizontal: 2,
                        paddingTop: 4,
                        paddingBottom: cartCount > 0 ? 100 : 20
                      }}

                      // Smooth scrolling settings
                      showsVerticalScrollIndicator={false}
                      onScrollBeginDrag={() => Keyboard.dismiss()}
                      scrollEventThrottle={16} // 60fps for smooth scrolling

                      // Disable momentum scrolling for more controlled feel
                      decelerationRate="normal"

                      // Components
                      ListEmptyComponent={renderEmptyState}

                      // Pull-to-refresh with theme colors
                      refreshControl={
                        <RefreshControl
                          refreshing={isRefreshing}
                          onRefresh={handleRefresh}
                          tintColor={theme.colors.primary}
                          colors={[theme.colors.primary]}
                          title="Refreshing quantities..."
                        />
                      }
                    />
                  </View>

                  {/* Floating Buttons with fixed position */}
                  <FloatingButtons
                    onScrollToTop={scrollToTop}
                    scrollY={scrollY}
                    scrollThreshold={LIST_CONFIG.SCROLL_THRESHOLD}
                  />


                </>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* Fixed Footer with Checkout Button */}
      <CheckoutButton
        cartCount={cartCount}
        totalAmount={totalCartAmount}
        onPress={handleCheckout}
        disabled={cartCount === 0}
      />


      </View>
  );
};

const styles = createOrderScreenStyles;



export default CreateOrderScreen;
