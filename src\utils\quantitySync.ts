import { AppState, AppStateStatus } from 'react-native';
import { startBackgroundQuantitySync, stopBackgroundQuantitySync, refreshActualQuantitiesCache } from './quantityCache';

/**
 * Quantity Sync Manager
 * 
 * This utility manages the background synchronization of actual quantities
 * to ensure they are always up-to-date without impacting UI performance.
 */
class QuantitySyncManager {
  private isInitialized = false;
  private appStateSubscription: any = null;
  private syncOnForeground = true;

  /**
   * Initialize the quantity sync manager
   * This should be called once when the app starts
   */
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    if (__DEV__) console.log('Initializing Quantity Sync Manager...');

    // Start background sync immediately
    startBackgroundQuantitySync();

    // Listen to app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);

    this.isInitialized = true;
    if (__DEV__) console.log('Quantity Sync Manager initialized successfully');
  }

  /**
   * Cleanup the quantity sync manager
   * This should be called when the app is being destroyed
   */
  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    if (__DEV__) console.log('Cleaning up Quantity Sync Manager...');

    // Stop background sync
    stopBackgroundQuantitySync();

    // Remove app state listener
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    this.isInitialized = false;
    if (__DEV__) console.log('Quantity Sync Manager cleaned up');
  }

  /**
   * Handle app state changes
   * Restart sync when app comes to foreground, stop when going to background
   */
  private handleAppStateChange = (nextAppState: AppStateStatus): void => {
    if (__DEV__) console.log('App state changed to:', nextAppState);

    switch (nextAppState) {
      case 'active':
        // App came to foreground
        if (this.syncOnForeground) {
          if (__DEV__) console.log('App became active, starting quantity sync...');
          startBackgroundQuantitySync();

          // Optionally refresh cache immediately when app becomes active
          this.refreshCacheOnForeground();
        }
        break;

      case 'background':
      case 'inactive':
        // App went to background
        if (__DEV__) console.log('App went to background, stopping quantity sync...');
        stopBackgroundQuantitySync();
        break;
    }
  };

  /**
   * Refresh cache when app comes to foreground
   * This ensures we have the latest data when user starts using the app
   */
  private refreshCacheOnForeground = async (): Promise<void> => {
    try {
      if (__DEV__) console.log('Refreshing quantity cache on foreground...');
      await refreshActualQuantitiesCache();
      if (__DEV__) console.log('Quantity cache refreshed successfully');
    } catch (error) {
      if (__DEV__) console.warn('Failed to refresh quantity cache on foreground:', error);
    }
  };

  /**
   * Force refresh the quantity cache
   * This can be called manually when needed (e.g., pull to refresh)
   */
  public async forceRefresh(): Promise<void> {
    try {
      if (__DEV__) console.log('Force refreshing quantity cache...');
      await refreshActualQuantitiesCache();
      if (__DEV__) console.log('Quantity cache force refreshed successfully');
    } catch (error) {
      if (__DEV__) console.warn('Failed to force refresh quantity cache:', error);
      throw error;
    }
  }

  /**
   * Enable or disable sync on foreground
   */
  public setSyncOnForeground(enabled: boolean): void {
    this.syncOnForeground = enabled;
    if (__DEV__) console.log('Sync on foreground:', enabled ? 'enabled' : 'disabled');
  }

  /**
   * Check if the manager is initialized
   */
  public isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Manually start background sync
   * Useful for testing or manual control
   */
  public startSync(): void {
    if (__DEV__) console.log('Manually starting quantity sync...');
    startBackgroundQuantitySync();
  }

  /**
   * Manually stop background sync
   * Useful for testing or manual control
   */
  public stopSync(): void {
    if (__DEV__) console.log('Manually stopping quantity sync...');
    stopBackgroundQuantitySync();
  }
}

// Create singleton instance
export const quantitySyncManager = new QuantitySyncManager();

/**
 * Hook to initialize quantity sync in React components
 * This should be called in the root component (App.tsx)
 */
export const useQuantitySync = () => {
  const initialize = () => quantitySyncManager.initialize();
  const cleanup = () => quantitySyncManager.cleanup();
  const forceRefresh = () => quantitySyncManager.forceRefresh();
  const isReady = () => quantitySyncManager.isReady();

  return {
    initialize,
    cleanup,
    forceRefresh,
    isReady
  };
};

/**
 * Initialize quantity sync (for non-React usage)
 */
export const initializeQuantitySync = () => {
  quantitySyncManager.initialize();
};

/**
 * Cleanup quantity sync (for non-React usage)
 */
export const cleanupQuantitySync = () => {
  quantitySyncManager.cleanup();
};
