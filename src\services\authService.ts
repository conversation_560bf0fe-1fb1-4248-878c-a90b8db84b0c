import { apiClient } from './apiClient';
import { LoginResponse } from '../types/auth';
import { STORAGE_KEYS } from '../constants/storage';
import { API } from '../constants/api';
import * as SecureStorage from '../utils/auth';
import { preloadItemPrices } from '../orchestrators/preload';
import { deleteDatabase, initializeDatabaseOnStartup, clearCache } from '../utils/sqliteService';
import { withErrorHandling } from '../utils/errors';
import { fetchCustomers } from './customerService';
import { cacheService, CacheKey } from '../utils/cacheService';
import { backgroundOperationsManager, BackgroundOperationType } from '../orchestrators/backgroundOperations';

/**
 * Login user and store authentication data
 */
export const login = async (email: string, password: string): Promise<LoginResponse> => {
  return withErrorHandling(async () => {
    const response = await apiClient.post(
      API.ENDPOINTS.LOGIN,
      { usr: email, pwd: password },
      {
        withCredentials: true,
        timeout: 5000
      }
    );
    const cookie = response.headers['set-cookie']?.find((c: string) => c.includes('sid='));
    if (!cookie) {
      throw new Error('No session cookie received');
    }

    const sid = cookie.split(';')[0].split('=')[1];
    if (!sid) {
      throw new Error('Invalid session cookie');
    }

    // Extract and validate user data
    let firstName = 'User';
    try {
      if (response.data && typeof response.data === 'object') {
        // Handle different possible response formats
        const userData = response.data;

        if (userData.full_name && typeof userData.full_name === 'string') {
          firstName = userData.full_name.split(' ')[0].trim();
        } else if (userData.first_name && typeof userData.first_name === 'string') {
          firstName = userData.first_name.trim();
        } else if (userData.name && typeof userData.name === 'string') {
          firstName = userData.name.split(' ')[0].trim();
        }

        // Ensure firstName is not empty and is valid
        if (!firstName || firstName.length === 0) {
          firstName = 'User';
        }
      }
    } catch (error) {
      firstName = 'User';
    }

    // PERFORMANCE OPTIMIZATION: Store critical data in parallel
    // This reduces the login time by executing these operations concurrently
    await Promise.all([
      SecureStorage.setSecureItem(STORAGE_KEYS.SID, sid),
      SecureStorage.setSecureItem(STORAGE_KEYS.LAST_EMAIL, email.trim()),
      SecureStorage.setSecureItem(STORAGE_KEYS.FIRST_NAME, firstName)
    ]);

    // PERFORMANCE OPTIMIZATION: Prefetch data in stages to prioritize UI responsiveness
    // Stage 1: Immediately after login, prefetch only critical data needed for home screen
    // This ensures the home screen can render quickly
    setTimeout(() => {
      // First prefetch customers - needed for the customers tab
      fetchCustomers(true).catch(() => {/* Silently fail */});
    }, 100);

    backgroundOperationsManager.startOperation(BackgroundOperationType.LOGIN_SYNC);

    Promise.allSettled([
      initializeDatabaseOnStartup(),
      preloadItemPrices(),
      cacheService.preloadCriticalData()
    ]).then(() => {
      backgroundOperationsManager.completeOperation();
    }).catch(() => {
      backgroundOperationsManager.failOperation();
    });
    return response.data;
  });
};

/**
 * Logout user and clear authentication data
 */
export const logout = async (keepUserEmail: boolean = true): Promise<void> => {
  await clearAuthData(keepUserEmail);

  // Clear customer cache
  cacheService.clearCache(CacheKey.CUSTOMERS).catch(() => {});

  // Clear database and item price cache
  // The deleteDatabase function calls clearCache() which clears the item price cache
  deleteDatabase().catch(() => {});

  // Explicitly clear the item price cache to ensure it's cleared
  // This is redundant with deleteDatabase but ensures the cache is cleared even if deleteDatabase fails
  try {
    clearCache(); // Clear all caches including item price cache
  } catch (error) {
    // Silently fail
  }

  // Logout from API
  apiClient.get(API.ENDPOINTS.LOGOUT).catch(() => {});
};

/**
 * Clear all authentication data from secure storage
 */
export const clearAuthData = async (keepUserEmail: boolean = true): Promise<void> => {
  await SecureStorage.clearSessionData(keepUserEmail);
};
