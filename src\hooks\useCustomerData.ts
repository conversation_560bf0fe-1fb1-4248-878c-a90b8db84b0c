import { useState, useEffect, useCallback, useRef } from 'react';
import { Customer } from '../types/business';
import { loadCustomers, refreshCustomers } from '../orchestrators/customer';
import useErrorHandler from './useErrorHandler';

export const useCustomerData = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true); // Start with loading true to show skeleton immediately
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 🚀 NAVIGATION PROTECTION: Prevent rapid state changes during navigation
  const isLoadingRef = useRef(false);

  const { handleError } = useErrorHandler();

  const loadCustomerData = useCallback(async () => {
    // 🚀 PREVENT CONCURRENT LOADS: Avoid multiple simultaneous data loads
    if (isLoadingRef.current) {
      return;
    }

    isLoadingRef.current = true;

    try {
      setError(null);
      // Keep loading state true until we have data to prevent empty state flash
      const customerData = await loadCustomers();
      setCustomers(customerData);
    } catch (err) {
      const error = err as Error;
      setError(error);
      handleError(error, {
        context: 'Loading Customers',
        onRetry: loadCustomerData
      });
    } finally {
      // Set loading to false immediately for fast performance
      setLoading(false);
      isLoadingRef.current = false;
    }
  }, [handleError]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      setError(null);
      const customerData = await refreshCustomers();
      setCustomers(customerData);
    } catch (err) {
      const error = err as Error;
      setError(error);
      handleError(error, {
        context: 'Refreshing Customers',
        onRetry: handleRefresh
      });
    } finally {
      setRefreshing(false);
    }
  }, [handleError]);

  useEffect(() => {
    // 🚀 ENSURE LOADING STATE: Always start with loading true on mount
    setLoading(true);
    loadCustomerData();
  }, [loadCustomerData]);

  return {
    customers,
    loading,
    refreshing,
    error,
    handleRefresh,
    reload: loadCustomerData
  };
};
