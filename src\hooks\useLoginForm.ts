
import { useState, useCallback, useMemo } from 'react';
import { Keyboard } from 'react-native';
import { useRouter } from 'expo-router';
import { LoginFormState, LoginFormData } from '../types/auth';
import { performLogin } from '../orchestrators/login';
import useError<PERSON>and<PERSON> from './useErrorHandler';

const initialFormState: LoginFormState = {
  data: {
    email: '',
    password: ''
  },
  errors: {
    email: '',
    password: '',
    general: ''
  },
  loading: false,
  secureTextEntry: true
};

export const useLoginForm = (initialEmail?: string) => {
  const [formState, setFormState] = useState<LoginFormState>(() => ({
    ...initialFormState,
    data: {
      ...initialFormState.data,
      email: initialEmail || ''
    }
  }));

  const router = useRouter();
  const { handleError, getErrorMessage } = useError<PERSON>andler();

  // Memoize validation to prevent unnecessary re-renders
  const isFormValid = useMemo(() => {
    return formState.data.email.trim().length > 0 &&
           formState.data.password.length > 0 &&
           !formState.loading;
  }, [formState.data.email, formState.data.password, formState.loading]);

  const setEmail = useCallback((email: string) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, email },
      errors: { ...prev.errors, email: '', general: '' }
    }));
  }, []);

  const setPassword = useCallback((password: string) => {
    setFormState(prev => ({
      ...prev,
      data: { ...prev.data, password },
      errors: { ...prev.errors, password: '', general: '' }
    }));
  }, []);

  const toggleSecureEntry = useCallback(() => {
    setFormState(prev => ({
      ...prev,
      secureTextEntry: !prev.secureTextEntry
    }));
  }, []);

  const clearErrors = () => {
    setFormState(prev => ({
      ...prev,
      errors: {
        email: '',
        password: '',
        general: ''
      }
    }));
  };

  const handleLogin = useCallback(async () => {
    if (!isFormValid) return;

    clearErrors();
    setFormState(prev => ({ ...prev, loading: true }));

    try {
      const result = await performLogin(formState.data);

      if (result.success && result.shouldNavigate && result.redirectTo) {
        // Use replace for better performance
        router.replace({
          pathname: result.redirectTo,
          params: { immediate: 'true' }
        });
      } else if (result.error) {
        setFormState(prev => ({
          ...prev,
          errors: { ...prev.errors, general: result.error! }
        }));
      }
    } catch (error) {
      const errorMessage = getErrorMessage(error);
      setFormState(prev => ({
        ...prev,
        errors: { ...prev.errors, general: errorMessage }
      }));

      handleError(error, {
        context: 'Login',
        silent: true
      });
    } finally {
      setFormState(prev => ({ ...prev, loading: false }));
    }
  }, [isFormValid, formState.data, clearErrors, performLogin, router, getErrorMessage, handleError]);

  // Handle login button press
  const onLoginPress = useCallback(() => {
    Keyboard.dismiss();
    handleLogin();
  }, [handleLogin]);

  return {
    formState,
    setEmail,
    setPassword,
    toggleSecureEntry,
    handleLogin,
    onLoginPress,
    isFormValid
  };
};
