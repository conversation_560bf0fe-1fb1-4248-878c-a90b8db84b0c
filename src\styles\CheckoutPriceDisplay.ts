import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const checkoutPriceDisplayStyles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  priceText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
  },
  discountedPrice: {
    color: BRAND.PRIMARY,
  },
  originalPrice: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textDecorationLine: 'line-through',
    marginTop: 2,
  },
});
