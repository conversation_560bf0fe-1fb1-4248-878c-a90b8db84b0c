/**
 * Update Dialog Component
 *
 * Simple dialog for both forced and optional updates
 * Shows information before download - no surprise downloads
 */

import React, { memo, useCallback, useState } from 'react';
import { View, Linking, Alert } from 'react-native';
import { Dialog, Portal, Text, Button, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { UpdateAction } from '../types/business';
import { updateDialogStyles } from '../styles/UpdateDialog';

interface UpdateDialogProps {
  visible: boolean;
  updateAction: UpdateAction;
  onDismiss?: () => void; // For optional updates
}

export const UpdateDialog: React.FC<UpdateDialogProps> = memo(({
  visible,
  updateAction,
  onDismiss,
}) => {
  const { versionInfo } = updateAction;
  const isForced = updateAction.type === 'forced';
  const [isDownloading, setIsDownloading] = useState(false);
  const theme = useTheme();

  const handleUpdate = useCallback(async () => {
    if (isDownloading) return;

    setIsDownloading(true);

    try {
      const supported = await Linking.canOpenURL(versionInfo.download_url);
      if (supported) {
        await Linking.openURL(versionInfo.download_url);
        // For forced updates, don't dismiss the dialog
        if (!isForced && onDismiss) {
          onDismiss();
        }
      } else {
        Alert.alert('Error', 'Cannot open download link. Please check your internet connection.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to open download link. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  }, [isDownloading, versionInfo.download_url, isForced, onDismiss]);

  const formatFileSize = (sizeInMB: number): string => {
    return `${sizeInMB.toFixed(1)} MB`;
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        dismissable={!isForced}
        onDismiss={onDismiss}
        style={updateDialogStyles.dialog}
      >
        <Dialog.Title style={[updateDialogStyles.dialogTitle, {
          color: isForced ? theme.colors.error : theme.colors.primary
        }]}>
          {isForced ? 'Update Required' : 'Update Available'}
        </Dialog.Title>

        <Dialog.Content style={updateDialogStyles.dialogContent}>
          {/* Version Info */}
          <Text variant="titleMedium" style={updateDialogStyles.versionText}>
            Version {versionInfo.current_version}
          </Text>

          <Text variant="bodySmall" style={updateDialogStyles.infoText}>
            Size: {formatFileSize(versionInfo.file_size_mb)} • {versionInfo.release_date}
          </Text>

          {/* Reason */}
          <Text variant="bodyMedium" style={updateDialogStyles.reasonText}>
            {updateAction.reason}
          </Text>

          {/* Release Notes */}
          {versionInfo.release_notes && versionInfo.release_notes.length > 0 && (
            <View style={updateDialogStyles.releaseNotesSection}>
              <Text variant="titleSmall" style={updateDialogStyles.releaseNotesTitle}>
                What's New:
              </Text>
              {versionInfo.release_notes.map((note, index) => (
                <Text key={index} variant="bodyMedium" style={updateDialogStyles.releaseNoteItem}>
                  • {note}
                </Text>
              ))}
            </View>
          )}

          {isForced && (
            <View style={updateDialogStyles.warningContainer}>
              <MaterialCommunityIcons
                name="alert-circle"
                size={20}
                color={theme.colors.error}
                style={updateDialogStyles.warningIcon}
              />
              <Text style={updateDialogStyles.warningText}>
                This update is required to continue using the app
              </Text>
            </View>
          )}
        </Dialog.Content>

        <Dialog.Actions style={updateDialogStyles.dialogActions}>
          {!isForced && (
            <Button
              mode="outlined"
              onPress={onDismiss}
              disabled={isDownloading}
              style={updateDialogStyles.laterButton}
              labelStyle={updateDialogStyles.laterButtonText}
            >
              Later
            </Button>
          )}
          <Button
            mode="contained"
            onPress={handleUpdate}
            loading={isDownloading}
            disabled={isDownloading}
            style={[
              updateDialogStyles.downloadButton,
              { backgroundColor: isForced ? theme.colors.error : theme.colors.primary }
            ]}
            labelStyle={updateDialogStyles.downloadButtonText}
          >
            {isDownloading ? 'Opening...' : 'Download'}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
});

UpdateDialog.displayName = 'UpdateDialog';
