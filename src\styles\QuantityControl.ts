import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const quantityControlStyles = StyleSheet.create({
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15, // Half of width/height for perfect circle
    backgroundColor: NEUTRAL.BACKGROUND,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: NEUTRAL.DIVIDER,
    elevation: 1, // Light elevation for buttons
  },
  quantityButtonDisabled: {
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  quantityButtonText: {
    fontSize: 14, // Reduced from 16
    fontWeight: 'bold',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  quantityButtonTextDisabled: {
    color: NEUTRAL.TEXT_DISABLED,
  },
  quantityInput: {
    width: 40,
    height: 32,
    borderWidth: 1,
    borderColor: NEUTRAL.DIVIDER,
    borderRadius: 4,
    textAlign: 'center',
    fontSize: 13,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    padding: 0,
    marginHorizontal: 6,
  },
  quantityInputDisabled: {
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    color: NEUTRAL.TEXT_DISABLED,
  },
  quantityValue: {
    width: 26,
    textAlign: 'center',
    fontSize: 13,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    marginHorizontal: 4,
  },
});
