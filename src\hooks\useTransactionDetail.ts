import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { fetchSalesOrderById, fetchSalesInvoiceById } from '../services/transactionService';
import { fetchCustomers } from '../services/customerService';
import { Transaction, Customer } from '../types/business';
import { isSalesOrder, isSalesInvoice } from '../utils/transactions';
import useErrorHandler from './useErrorHandler';

interface UseTransactionDetailParams {
  transactionId: string;
  transactionType: 'order' | 'invoice';
  onClose?: () => void;
  onBack?: () => void;
}

export const useTransactionDetail = ({ 
  transactionId, 
  transactionType, 
  onClose, 
  onBack 
}: UseTransactionDetailParams) => {
  // State variables
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isReordering, setIsReordering] = useState(false);

  // Get the error handler hook
  const { handleError } = useErrorHandler();

  // Determine transaction type flags
  const isOrder = transactionType === 'order';
  const isInvoice = transactionType === 'invoice';

  // Fetch transaction data
  const fetchTransactionData = useCallback(async (showLoading = true) => {
    if (!transactionId) return;

    try {
      if (showLoading) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      setError(null);

      let result: Transaction;
      if (isOrder) {
        result = await fetchSalesOrderById(transactionId);
      } else {
        result = await fetchSalesInvoiceById(transactionId);
      }

      setTransaction(result);
    } catch (error) {
      const errorMessage = `Failed to load ${transactionType} details`;
      setError(errorMessage);
      handleError(error, { context: errorMessage });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [transactionId, transactionType, isOrder, handleError]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    fetchTransactionData(false);
  }, [fetchTransactionData]);

  // Handle close action
  const handleClose = useCallback(() => {
    if (onClose) {
      onClose();
    } else if (onBack) {
      onBack();
    } else {
      router.back();
    }
  }, [onClose, onBack]);

  // Handle reorder action (for both orders and invoices)
  const handleReorder = useCallback(async (replaceCart: boolean = true) => {
    if (!transaction) return;

    try {
      setIsReordering(true);

      // Fetch customer details to get proper customer ID, territory, and customer group
      const customers = await fetchCustomers();
      const customer = customers.find((c: Customer) => c.customer_name === transaction.customer_name);

      if (!customer) {
        Alert.alert('Error', 'Customer details not found. Please try again.');
        setIsReordering(false);
        return;
      }

      const cartCacheKey = `cart_${customer.name}`;

      // Create cart items from the order with proper orderIndex
      const reorderItems = transaction.items?.map((item, index) => ({
        item_code: item.item_code,
        item_name: item.item_name,
        quantity: item.qty,
        selectedUom: item.uom,
        rate: item.rate,
        customerId: customer.name, // Use proper customer ID
        customerName: transaction.customer_name,
        // Assign orderIndex for reordered items
        orderIndex: index + 1
      })) || [];

      if (replaceCart) {
        // Replace existing cart completely
        await AsyncStorage.setItem(cartCacheKey, JSON.stringify(reorderItems));
      } else {
        // Append to existing cart
        try {
          const existingCartString = await AsyncStorage.getItem(cartCacheKey);
          let existingItems: any[] = [];
          let nextOrderIndex = 1;

          if (existingCartString) {
            existingItems = JSON.parse(existingCartString);
            // Find the highest orderIndex in existing cart
            nextOrderIndex = Math.max(
              ...existingItems.map(item => item.orderIndex || 0),
              0
            ) + 1;
          }

          // Update orderIndex for reordered items to append after existing items
          const appendedItems = reorderItems.map((item, index) => ({
            ...item,
            orderIndex: nextOrderIndex + index
          }));

          // Combine existing and new items
          const combinedItems = [...existingItems, ...appendedItems];
          await AsyncStorage.setItem(cartCacheKey, JSON.stringify(combinedItems));
        } catch (error) {
          // If there's an error reading existing cart, just replace it
          await AsyncStorage.setItem(cartCacheKey, JSON.stringify(reorderItems));
        }
      }

      // Navigate to create order screen with customer information
      router.push({
        pathname: '/create-order',
        params: {
          customerId: customer.name, // Use proper customer ID
          customerName: customer.customer_name,
          territory: customer.territory,
          customerGroup: customer.customer_group,
          from: 'reorder' // Indicate this is coming from reorder
        }
      });

      // Close the modal/screen
      handleClose();
    } catch (error) {
      handleError(error, { context: 'Failed to reorder items' });
    } finally {
      setIsReordering(false);
    }
  }, [transaction, handleClose, handleError]);

  // Show reorder confirmation with cart options
  const showReorderConfirmation = useCallback(async () => {
    if (!transaction) return;

    try {
      // Fetch customer details to get proper customer ID for cart cache key
      const customers = await fetchCustomers();
      const customer = customers.find((c: Customer) => c.customer_name === transaction.customer_name);

      if (!customer) {
        Alert.alert('Error', 'Customer details not found. Please try again.');
        return;
      }

      // Check if cart already exists for this customer
      const cartCacheKey = `cart_${customer.name}`;
      const existingCartString = await AsyncStorage.getItem(cartCacheKey);

      if (existingCartString) {
        const existingItems = JSON.parse(existingCartString);
        const hasActiveItems = existingItems.some((item: any) => item.quantity > 0);

        if (hasActiveItems) {
          // Cart exists with items - give user choice
          Alert.alert(
            'Cart Already Exists',
            `You already have items in your cart for ${transaction.customer_name}. What would you like to do?`,
            [
              {
                text: 'Cancel',
                style: 'cancel',
              },
              {
                text: 'Replace Cart',
                onPress: () => handleReorder(true),
                style: 'destructive',
              },
              {
                text: 'Add to Cart',
                onPress: () => handleReorder(false),
              },
            ]
          );
          return;
        }
      }

      // No existing cart or empty cart - simple reorder
      Alert.alert(
        'Reorder Items',
        `Do you want to reorder all items from ${transaction.name}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Reorder',
            onPress: () => handleReorder(true),
          },
        ]
      );
    } catch (error) {
      // If there's an error checking cart, proceed with simple reorder
      Alert.alert(
        'Reorder Items',
        `Do you want to reorder all items from ${transaction.name}?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Reorder',
            onPress: () => handleReorder(true),
          },
        ]
      );
    }
  }, [transaction, handleReorder]);

  // Load data on mount and when transactionId changes
  useEffect(() => {
    fetchTransactionData();
  }, [fetchTransactionData]);

  // Calculate derived values
  const canReorder = transaction && (isSalesOrder(transaction) || isSalesInvoice(transaction)) &&
    transaction.items && transaction.items.length > 0 &&
    transaction.status !== 'Cancelled';
  const hasItems = transaction?.items && transaction.items.length > 0;

  return {
    // Data
    transaction,
    loading,
    refreshing,
    error,
    isReordering,
    
    // Flags
    isOrder,
    isInvoice,
    canReorder,
    hasItems,
    
    // Actions
    handleRefresh,
    handleClose,
    handleReorder,
    showReorderConfirmation,
    
    // Utils
    fetchTransactionData,
  };
};
