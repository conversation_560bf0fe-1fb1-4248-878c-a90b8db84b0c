import React from 'react';
import { View } from 'react-native';
import { CartItem } from '../types/business';
import CheckoutCartItem from './CheckoutCartItem';
import { sortCartItemsForCheckout } from '../orchestrators/cart';

interface CheckoutItemsListProps {
  items: CartItem[];
  updateQuantity: (itemCode: string, quantity: number) => Promise<void>;
  changeUom: (itemCode: string) => void;
}

/**
 * Simple items list component for checkout screen
 */
const CheckoutItemsList: React.FC<CheckoutItemsListProps> = ({
  items,
  updateQuantity,
  changeUom
}) => {
  // Use the same sorting logic as cart page to maintain order items were added
  const filteredItems = sortCartItemsForCheckout(items);

  return (
    <View>
      {filteredItems.map((item, index) => (
        <CheckoutCartItem
          key={`${item.item_code}-${item.orderIndex || index}`}
          item={item}
          index={index + 1}
          updateQuantity={updateQuantity}
          changeUom={changeUom}
        />
      ))}
    </View>
  );
};

export default CheckoutItemsList;
