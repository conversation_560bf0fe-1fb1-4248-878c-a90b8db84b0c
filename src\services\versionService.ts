/**
 * Version Service
 *
 * Simple API service for version checking on login only.
 * No caching, no complexity - just check and respond.
 */

import { API } from '../constants/api';
import { apiClient } from './apiClient';
import { withErrorHandling } from '../utils/errors';
import { VersionInfo } from '../types/business';
import { VersionResponse } from '../types/api';

/**
 * Check version - simple API call
 */
export const checkVersion = async (): Promise<VersionInfo | null> => {
  return withErrorHandling(async () => {
    const response = await apiClient.get<VersionResponse>(
      API.ENDPOINTS.VERSION_CHECK,
      { timeout: 8000 }
    );

    return response?.data?.message || null;
  });
};
