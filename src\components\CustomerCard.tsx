import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { View, TouchableOpacity, Animated } from 'react-native';
import { Text, useTheme } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Customer } from '../types/business';
import { customerCardStyles } from '../styles/CustomerCard';
import { generateAccessibilityLabel, getAccessibilityHint } from '../utils/ui';
import FloatingActionMenu from './FloatingActionMenu';
import AddressModal from './AddressModal';

interface CustomerCardProps {
  customer: Customer;
  onPlaceOrder: (customer: Customer) => void;
  onNavigateToInvoices?: (customerId: string) => void;
}

const CustomerCardBase = ({
  customer,
  onPlaceOrder,
  onNavigateToInvoices
}: CustomerCardProps) => {
  const theme = useTheme();
  const [menuVisible, setMenuVisible] = useState(false);
  const [addressModalVisible, setAddressModalVisible] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const cardRef = useRef<View>(null);
  const highlightAnim = useRef(new Animated.Value(0)).current;

  const styles = customerCardStyles;

  const showMenu = useCallback(() => {
    // Use requestAnimationFrame for smoother UI response
    requestAnimationFrame(() => {
      cardRef.current?.measureInWindow((x, y, width, height) => {
        // Batch state updates to prevent multiple re-renders
        React.startTransition(() => {
          setMenuPosition({ x, y, width, height });
          setMenuVisible(true);
        });
      });
    });
  }, [customer]);

  const hideMenu = useCallback(() => {
    setMenuVisible(false);
  }, []);

  const showAddressModal = useCallback(() => {
    setAddressModalVisible(true);
  }, []);

  const hideAddressModal = useCallback(() => {
    setAddressModalVisible(false);
  }, []);

  const formatCurrency = useCallback((value: number | undefined): string => {
    const numValue = value || 0;
    return numValue > 0 ? Math.floor(numValue).toLocaleString('en-US') : "--";
  }, []);

  const creditLimitFormatted = useMemo(() =>
    formatCurrency(customer.custom_credit_limit),
    [customer.custom_credit_limit, formatCurrency]
  );

  const outstandingFormatted = useMemo(() =>
    formatCurrency(customer.outstanding),
    [customer.outstanding, formatCurrency]
  );

  const hasFinancialData = useMemo(() =>
    (customer.custom_credit_limit || 0) > 0 || (customer.outstanding || 0) > 0,
    [customer.custom_credit_limit, customer.outstanding]
  );

  const customerNameComponent = useMemo(() => (
    <Text style={styles.customerName} numberOfLines={1} ellipsizeMode="tail">
      {customer.customer_name}
    </Text>
  ), [customer.customer_name, styles.customerName]);

  const customerDetailsComponent = useMemo(() => {
    // If both territory and TIN exist, put TIN on a separate line to avoid overlap
    const shouldStackVertically = customer.territory && customer.tax_id;

    return (
      <View style={shouldStackVertically ? styles.customerDetailsVertical : styles.customerDetailsContainer}>
        {customer.territory && (
          <Text style={styles.customerTerritory} numberOfLines={1}>
            {customer.territory}
          </Text>
        )}
        {customer.tax_id ? (
          <Text style={shouldStackVertically ? styles.customerIdVertical : styles.customerId} numberOfLines={1}>
            {customer.tax_id}
          </Text>
        ) : null}
      </View>
    );
  }, [
    customer.territory,
    customer.tax_id,
    styles.customerDetailsContainer,
    styles.customerDetailsVertical,
    styles.customerTerritory,
    styles.customerId,
    styles.customerIdVertical
  ]);

  const primaryAddress = useMemo(() => {
    if (!customer.addresses || customer.addresses.length === 0) {
      return null;
    }

    if (customer.customer_primary_address) {
      const primary = customer.addresses.find(addr => addr.name === customer.customer_primary_address);
      if (primary) return primary;
    }

    return customer.addresses[0];
  }, [customer.addresses, customer.customer_primary_address]);

  const primaryAddressComponent = useMemo(() => {
    if (!primaryAddress) return null;

    const addressText = primaryAddress.address_line1 +
      (primaryAddress.city && primaryAddress.city !== '-' ? `, ${primaryAddress.city}` : '');

    return (
      <View style={styles.addressContainer}>
        <Text style={styles.addressText} numberOfLines={2} ellipsizeMode="tail">
          {addressText}
        </Text>
      </View>
    );
  }, [primaryAddress, styles.addressContainer, styles.addressText]);

  // Memoized accessibility label for the customer card
  const customerAccessibilityLabel = useMemo(() => {
    const details = [
      `Customer: ${customer.customer_name}`,
      customer.territory ? `Territory: ${customer.territory}` : null,
      customer.tax_id ? `Tax ID: ${customer.tax_id}` : null,
      hasFinancialData ? `Credit limit: ${creditLimitFormatted}` : null,
      hasFinancialData && (customer.outstanding || 0) > 0 ? `Outstanding: ${outstandingFormatted}` : null,
    ].filter(Boolean) as string[];

    return generateAccessibilityLabel('Customer card', details);
  }, [customer.customer_name, customer.territory, customer.tax_id, hasFinancialData, creditLimitFormatted, outstandingFormatted]);

  const customerAccessibilityHint = useMemo(() =>
    getAccessibilityHint('view customer options and place orders'),
    []
  );

  useEffect(() => {
    Animated.timing(highlightAnim, {
      toValue: menuVisible ? 1 : 0,
      duration: 120,
      useNativeDriver: false,
    }).start();
  }, [menuVisible, highlightAnim]);

  const highlightBackground = highlightAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [styles.listItem.backgroundColor, `${theme.colors.primary}10`]
  });

  return (
    <>
      <Animated.View
        style={[
          styles.listItem,
          {
            backgroundColor: highlightBackground,
          },
          menuVisible && styles.selectedItem
        ]}
        ref={cardRef}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={showMenu}
          style={styles.itemContent}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel={customerAccessibilityLabel}
          accessibilityHint={customerAccessibilityHint}
          delayPressIn={0}
          delayPressOut={0}
        >
          <View style={styles.cardHeader}>
            <View style={styles.nameContainer}>
              {customerNameComponent}
              {customerDetailsComponent}
              {primaryAddressComponent}
            </View>

            <View style={styles.headerFinancialContainer}>
              {hasFinancialData ? (
                <>
                  <View style={styles.headerFinancialItem}>
                    <MaterialCommunityIcons name="credit-card-outline" size={12} color="#666" style={styles.headerFinancialIcon} />
                    <View style={styles.valueContainer}>
                      <Text style={styles.headerFinancialValue} numberOfLines={1} ellipsizeMode="tail">
                        {creditLimitFormatted}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.headerFinancialItem}>
                    <MaterialCommunityIcons name="cash-multiple" size={12} color="#666" style={styles.headerFinancialIcon} />
                    <View style={styles.valueContainer}>
                      <Text
                        style={[
                          styles.headerFinancialValue,
                          (customer.outstanding || 0) > 0 && styles.outstandingValue
                        ]}
                        numberOfLines={1}
                        ellipsizeMode="tail"
                      >
                        {outstandingFormatted}
                      </Text>
                    </View>
                  </View>
                </>
              ) : (
                <View style={styles.emptyFinancialPlaceholder} />
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>

      <FloatingActionMenu
        visible={menuVisible}
        onClose={hideMenu}
        position={menuPosition}
        customer={customer}
        onPlaceOrder={onPlaceOrder}
        onNavigateToInvoices={onNavigateToInvoices}
        onShowAddresses={showAddressModal}
      />

      <AddressModal
        visible={addressModalVisible}
        onClose={hideAddressModal}
        addresses={customer.addresses || []}
        customerName={customer.customer_name}
      />
    </>
  );
};

const CustomerCard = React.memo(CustomerCardBase, (prevProps, nextProps) => {
  if (prevProps.customer === nextProps.customer &&
      prevProps.onPlaceOrder === nextProps.onPlaceOrder &&
      prevProps.onNavigateToInvoices === nextProps.onNavigateToInvoices) {
    return true;
  }

  // 🚀 HYPER-OPTIMIZED COMPARISON - Check in order of change frequency
  const prev = prevProps.customer;
  const next = nextProps.customer;

  // 🚀 FASTEST CHECKS FIRST
  // Primary key (most critical, fastest check)
  if (prev.name !== next.name) return false;

  // Financial data (changes most frequently)
  if (prev.outstanding !== next.outstanding) return false;
  if (prev.custom_credit_limit !== next.custom_credit_limit) return false;

  // Basic info (moderate frequency)
  if (prev.customer_name !== next.customer_name) return false;
  if (prev.territory !== next.territory) return false;
  if (prev.customer_group !== next.customer_group) return false;

  // Contact info (low frequency)
  if (prev.customer_primary_address !== next.customer_primary_address) return false;
  if (prev.tax_id !== next.tax_id) return false;
  if (prev.mobile_no !== next.mobile_no) return false;

  // 🚀 OPTIMIZED ARRAY COMPARISON - Most expensive check last
  const prevAddressCount = prev.addresses?.length || 0;
  const nextAddressCount = next.addresses?.length || 0;

  // Quick length check first
  if (prevAddressCount !== nextAddressCount) return false;

  // Only do deep comparison if lengths match and arrays exist
  if (prevAddressCount > 0 && nextAddressCount > 0) {
    // Use faster comparison for small arrays
    if (prevAddressCount <= 3) {
      for (let i = 0; i < prevAddressCount; i++) {
        if (JSON.stringify(prev.addresses![i]) !== JSON.stringify(next.addresses![i])) {
          return false;
        }
      }
    } else {
      // Fallback to full comparison for larger arrays
      if (JSON.stringify(prev.addresses) !== JSON.stringify(next.addresses)) return false;
    }
  }

  return true;
});

export default CustomerCard;
