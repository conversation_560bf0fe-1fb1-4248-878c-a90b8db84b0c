import { useState, useCallback, useMemo, useRef } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import { CartData, CartItem, CartScreenParams } from '../types/business';
import { STORAGE_KEYS } from '../constants/storage';
import { fetchCustomers } from '../services/customerService';

// Simplified cart management hook
export const useCartManager = (customerId?: string, customerName?: string) => {
  const [loading, setLoading] = useState(true);
  const [carts, setCarts] = useState<CartData[]>([]);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [expandedCart, setExpandedCart] = useState<string | null>(null);

  const cartCacheKey = customerId ? `${STORAGE_KEYS.CART_PREFIX}${customerId}` : '';

  // Simple cart loading
  const loadAllCarts = useCallback(async () => {
    try {
      setLoading(true);

      const keys = await AsyncStorage.getAllKeys();
      const cartKeys = keys.filter(key => key.startsWith(STORAGE_KEYS.CART_PREFIX));

      if (cartKeys.length === 0) {
        setCarts([]);
        setLoading(false);
        return;
      }

      // 🚀 PERFORMANCE FIX: Use parallel AsyncStorage operations instead of sequential loop
      const cartDataPromises = cartKeys.map(key => AsyncStorage.getItem(key));
      const cartDataResults = await Promise.allSettled(cartDataPromises);

      const loadedCarts: CartData[] = [];
      const emptyCartKeys: string[] = [];

      cartDataResults.forEach((result, index) => {
        const key = cartKeys[index];

        if (result.status === 'rejected' || !result.value) {
          return;
        }

        try {
          const items = JSON.parse(result.value);
          const activeItems = items.filter((item: CartItem) => item.quantity > 0);

          if (activeItems.length === 0) {
            // Mark for removal but don't block the loading process
            emptyCartKeys.push(key);
            return;
          }

          const customerId = key.replace(STORAGE_KEYS.CART_PREFIX, '');
          const customerName = items[0]?.customerName || 'Unknown Customer';

          loadedCarts.push({
            customerId,
            customerName,
            items: activeItems,
            timestamp: Date.now(),
            lastUpdated: new Date().toLocaleString(),
          });
        } catch (e) {
          if (__DEV__) console.warn('Skipping invalid cart data for key:', key, e);
        }
      });

      // 🚀 PERFORMANCE FIX: Remove empty carts in parallel, non-blocking
      if (emptyCartKeys.length > 0) {
        Promise.allSettled(emptyCartKeys.map(key => AsyncStorage.removeItem(key))).catch(() => {
          // Silent fail for cleanup operations
        });
      }

      setCarts(loadedCarts.sort((a, b) => b.timestamp - a.timestamp));
    } catch (e) {
      if (__DEV__) console.error('Failed to load carts:', e);
      Alert.alert('Error', 'Failed to load carts');
    } finally {
      setLoading(false);
    }
  }, []);

  // Simple cart operations
  const handleNewCartItems = useCallback(async (params: CartScreenParams) => {
    try {
      const customerId = params.customerId as string;
      const customerName = params.customerName as string;
      const items = JSON.parse(params.items as string) as CartItem[];

      // Ensure all items have customer info and orderIndex
      const itemsWithCustomer = items.map((item, index) => ({
        ...item,
        customerId,
        customerName,
        orderIndex: item.orderIndex || (index + 1) // Assign orderIndex if missing
      }));

      // Ensure AsyncStorage update completes before refreshing cart list
      await AsyncStorage.setItem(`${STORAGE_KEYS.CART_PREFIX}${customerId}`, JSON.stringify(itemsWithCustomer));

      // Add a small delay to ensure AsyncStorage write is fully committed
      await new Promise(resolve => setTimeout(resolve, 100));

      // Now refresh the cart list to reflect the updated data
      await loadAllCarts();
    } catch (error) {
      if (__DEV__) console.error('Failed to handle new cart items:', error);
      // Still try to refresh carts even if update failed
      loadAllCarts();
    }
  }, [loadAllCarts]);

  const saveCartData = useCallback(async (): Promise<CartItem[]> => {
    if (!cartCacheKey) return [];
    try {
      const itemsToSave = cartItems.filter(item => item.quantity > 0);
      if (itemsToSave.length === 0) {
        await AsyncStorage.removeItem(cartCacheKey);
        return [];
      }

      const itemsWithCustomerInfo = itemsToSave.map(item => ({
        ...item,
        customerId,
        customerName
      }));

      await AsyncStorage.setItem(cartCacheKey, JSON.stringify(itemsWithCustomerInfo));
      return itemsWithCustomerInfo;
    } catch (error) {
      return cartItems.filter(item => item.quantity > 0);
    }
  }, [cartItems, cartCacheKey, customerId, customerName]);

  const loadCartData = useCallback(async (): Promise<CartItem[]> => {
    if (!cartCacheKey) return [];
    try {
      const cachedCartString = await AsyncStorage.getItem(cartCacheKey);
      return cachedCartString ? JSON.parse(cachedCartString) : [];
    } catch (error) {
      return [];
    }
  }, [cartCacheKey]);

  const clearCart = useCallback(async () => {
    if (!cartCacheKey) return;
    Alert.alert('Clear Cart', 'Are you sure you want to remove all items?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Clear All',
        style: 'destructive',
        onPress: async () => {
          try {
            setCartItems(prev => prev.map(item => ({ ...item, quantity: 0 })));
            await AsyncStorage.removeItem(cartCacheKey);
          } catch (error) {
            // Silent fail
          }
        }
      }
    ]);
  }, [cartCacheKey]);

  // Simplified quantity update
  const updateQuantity = useCallback((itemCode: string, quantityOrFn: number | ((prevQty: number) => number)) => {
    setCartItems(prevItems => {
      const existingItemIndex = prevItems.findIndex(item => item.item_code === itemCode);
      if (existingItemIndex >= 0) {
        const newItems = [...prevItems];
        const currentQuantity = newItems[existingItemIndex].quantity;
        const newQuantity = typeof quantityOrFn === 'function' ? quantityOrFn(currentQuantity) : quantityOrFn;

        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: Math.max(0, newQuantity),
          customerId,
          customerName
        };
        return newItems;
      }
      return prevItems;
    });
  }, [customerId, customerName]);

  // Add new item to cart
  const addToCart = useCallback((item: CartItem) => {
    setCartItems(prevItems => {
      const existingItemIndex = prevItems.findIndex(cartItem => cartItem.item_code === item.item_code);

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const newItems = [...prevItems];
        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: newItems[existingItemIndex].quantity + item.quantity,
          customerId,
          customerName
        };
        return newItems;
      } else {
        // Add new item
        return [...prevItems, { ...item, customerId, customerName }];
      }
    });
  }, [customerId, customerName]);

  // Simplified checkout
  const handleCheckout = useCallback(async (cart: CartData) => {
    try {
      const customers = await fetchCustomers();
      const customer = customers.find(c => c.name === cart.customerId);
      if (!customer) {
        Alert.alert('Error', 'Customer not found');
        return;
      }

      // Simple address handling
      let primaryAddressText = '';
      if (customer.addresses?.[0]) {
        const addr = customer.addresses[0];
        primaryAddressText = addr.address_line1 + (addr.city ? `, ${addr.city}` : '');
      }

      router.push({
        pathname: '/checkout',
        params: {
          customerId: cart.customerId,
          customerName: customer.customer_name, // Use fresh customer name from API, not stored cart name
          from: 'cart',
          tax_id: customer.tax_id || '',
          custom_credit_limit: (customer.custom_credit_limit || 0).toString(),
          outstanding: (customer.outstanding || 0).toString(),
          default_price_list: customer.default_price_list || '',
          mobile_no: customer.mobile_no || '',
          customer_primary_address: primaryAddressText
        }
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to proceed to checkout');
    }
  }, []);

  // 🚀 PERFORMANCE FIX: Cache customer data to avoid repeated API calls
  const customerCache = useRef<Map<string, any>>(new Map());

  const handleContinueShopping = useCallback(async (cart: CartData) => {
    try {
      let customer = customerCache.current.get(cart.customerId);

      if (!customer) {
        // Only fetch if not in cache
        const customers = await fetchCustomers();
        customer = customers.find(c => c.name === cart.customerId);

        if (customer) {
          // Cache for future use
          customerCache.current.set(cart.customerId, customer);
        }
      }

      if (!customer) {
        Alert.alert('Error', 'Customer not found');
        return;
      }

      router.push({
        pathname: '/create-order',
        params: {
          customerId: cart.customerId,
          customerName: customer.customer_name,
          territory: customer.territory,
          customerGroup: customer.customer_group,
          from: 'cart'
        }
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to load customer details');
    }
  }, []);

  const handleRemoveCart = useCallback(async (cart: CartData) => {
    Alert.alert('Remove Cart', 'Are you sure you want to remove this cart?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Remove',
        style: 'destructive',
        onPress: async () => {
          try {
            await AsyncStorage.removeItem(`${STORAGE_KEYS.CART_PREFIX}${cart.customerId}`);
            const updatedCarts = carts.filter(c => c.customerId !== cart.customerId);
            setCarts(updatedCarts);

          } catch (error) {
            Alert.alert('Error', 'Failed to remove cart');
          }
        }
      }
    ]);
  }, [carts]);

  const handleToggleExpand = useCallback((cartId: string) => {
    setExpandedCart(prev => prev === cartId ? null : cartId);
  }, []);

  const cartCount = useMemo(() => cartItems.filter(item => item.quantity > 0).length, [cartItems]);

  // Force refresh function for when we need to ensure data consistency
  const forceRefreshCarts = useCallback(async () => {
    try {
      // Clear current state first
      setCarts([]);
      setLoading(true);

      // Wait a bit longer to ensure all AsyncStorage operations are complete
      await new Promise(resolve => setTimeout(resolve, 200));

      // Reload all carts
      await loadAllCarts();
    } catch (error) {
      if (__DEV__) console.error('Failed to force refresh carts:', error);
      setLoading(false);
    }
  }, [loadAllCarts]);

  return {
    // State
    loading,
    carts,
    cartItems,
    expandedCart,
    cartCount,
    cartCacheKey,

    // Setters
    setCarts,
    setCartItems,
    setExpandedCart,

    // Cart list operations
    loadAllCarts,
    forceRefreshCarts,
    handleNewCartItems,

    // Individual cart operations
    saveCartData,
    loadCartData,
    clearCart,
    updateQuantity,
    addToCart,

    // Cart actions
    handleCheckout,
    handleContinueShopping,
    handleRemoveCart,
    handleToggleExpand
  };
};
