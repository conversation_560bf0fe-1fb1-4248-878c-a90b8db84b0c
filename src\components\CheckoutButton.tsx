import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formatCurrency } from '../utils/formatting';
import { CheckoutButtonProps } from '../types/business';
import { checkoutButtonStyles } from '../styles/CheckoutButton';

// Simplified CheckoutButton - optimized with React.memo
const CheckoutButton: React.FC<CheckoutButtonProps> = React.memo(({
  cartCount,
  totalAmount,
  onPress,
  disabled = false
}) => {
  const styles = checkoutButtonStyles;
  const insets = useSafeAreaInsets();

  if (cartCount === 0) return null;

  return (
    <View style={[styles.footer, { paddingBottom: Math.max(insets.bottom, 16) }]}>
      <TouchableOpacity
        style={[styles.checkoutButton, disabled && styles.disabledButton]}
        onPress={onPress}
        disabled={disabled}
      >
        <Text style={styles.checkoutButtonText}>Checkout</Text>
        <Text style={styles.checkoutButtonAmount}>
          {formatCurrency(totalAmount)}
        </Text>
      </TouchableOpacity>
    </View>
  );
});

export default CheckoutButton;
