import React from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';
import { BRAND } from '../constants/colors';
import { SupplierSidebarProps } from '../types/business';
import { supplierSidebarStyles } from '../styles/SupplierSidebar';

const SupplierSidebar: React.FC<SupplierSidebarProps> = ({
  selectedSupplier,
  availableSuppliers,
  supplierCounts,
  onSupplierSelect
}) => {
  const styles = supplierSidebarStyles;

  return (
    <View style={styles.sidebarContainer}>
      <ScrollView
        style={styles.sidebarContent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* All Suppliers Tab */}
        <TouchableOpacity
          style={[
            styles.supplierTab,
            selectedSupplier === null && styles.selectedSupplierTab
          ]}
          onPress={() => onSupplierSelect(null)}
          activeOpacity={0.7}
        >
          <View style={styles.tabContent}>
            <Text style={[
              styles.supplierTabText,
              selectedSupplier === null && styles.selectedSupplierTabText
            ]}>
              All
            </Text>
            <Text style={[
              styles.supplierCount,
              selectedSupplier === null && styles.selectedSupplierCount
            ]}>
              {Object.values(supplierCounts).reduce((sum, count) => sum + count, 0)}
            </Text>
          </View>
          {selectedSupplier === null && <View style={styles.activeIndicator} />}
        </TouchableOpacity>

        {/* Divider after "All" tab */}
        {availableSuppliers.length > 0 && <View style={styles.supplierDivider} />}

        {/* Individual Supplier Tabs */}
        {availableSuppliers.map((supplier, index) => (
          <React.Fragment key={supplier}>
            <TouchableOpacity
              style={[
                styles.supplierTab,
                selectedSupplier === supplier && styles.selectedSupplierTab
              ]}
              onPress={() => onSupplierSelect(supplier)}
              activeOpacity={0.7}
            >
              <View style={styles.tabContent}>
                <Text style={[
                  styles.supplierTabText,
                  selectedSupplier === supplier && styles.selectedSupplierTabText
                ]} numberOfLines={2}>
                  {supplier}
                </Text>
                <Text style={[
                  styles.supplierCount,
                  selectedSupplier === supplier && styles.selectedSupplierCount
                ]}>
                  {supplierCounts[supplier] || 0}
                </Text>
              </View>
              {selectedSupplier === supplier && <View style={styles.activeIndicator} />}
            </TouchableOpacity>
            {/* Divider between suppliers (not after the last one) */}
            {index < availableSuppliers.length - 1 && <View style={styles.supplierDivider} />}
          </React.Fragment>
        ))}
      </ScrollView>
    </View>
  );
};

export default React.memo(SupplierSidebar, (prevProps, nextProps) => {
  // More aggressive memoization for better performance
  return (
    prevProps.selectedSupplier === nextProps.selectedSupplier &&
    prevProps.availableSuppliers.length === nextProps.availableSuppliers.length &&
    prevProps.availableSuppliers.every((supplier, index) =>
      supplier === nextProps.availableSuppliers[index] &&
      prevProps.supplierCounts[supplier] === nextProps.supplierCounts[supplier]
    )
  );
});
