import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { View, Text, Modal, TouchableOpacity, ScrollView } from 'react-native';
import { IconButton, useTheme, Surface } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { NEUTRAL, BRAND } from '../constants/colors';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import MD from '../constants/design';
import { sortModalStyles as styles } from '../styles/SortModal';

export interface SortGroup {
  id: string;
  title: string;
  icon: string;
  options: SortOption[];
}

export interface SortOption {
  id: string;
  label: string;
}

interface SortModalProps {
  visible: boolean;
  onClose: () => void;
  sortGroups: SortGroup[];
  selectedSort: string;
  onApplySort: (sortId: string) => void;
  onClearSort: () => void;
}

const SortModal: React.FC<SortModalProps> = ({
  visible,
  onClose,
  sortGroups,
  selectedSort,
  onApplySort,
  onClearSort
}) => {
  const theme = useTheme();
  const [localSelectedSort, setLocalSelectedSort] = useState<string>(selectedSort);
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);

  // Memoize sort groups to prevent unnecessary re-renders
  const memoizedSortGroups = useMemo(() => sortGroups, [sortGroups]);

  useEffect(() => {
    if (visible) {
      setLocalSelectedSort(selectedSort);
      if (memoizedSortGroups.length > 0 && !expandedGroup) {
        setExpandedGroup(memoizedSortGroups[0].id);
      }
    }
  }, [visible, selectedSort, memoizedSortGroups, expandedGroup]);

  const handleSortSelect = useCallback((sortId: string) => {
    setLocalSelectedSort(sortId);
  }, []);

  const handleApplySort = useCallback(() => {
    // Always apply sort, even if it hasn't changed
    // This ensures the UI updates properly
    onApplySort(localSelectedSort);
    onClose();
  }, [localSelectedSort, onApplySort, onClose]);

  const handleClearSort = useCallback(() => {
    setLocalSelectedSort('');
    onClearSort();
    onClose();
  }, [onClearSort, onClose]);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity
          style={styles.backdropTouchable}
          activeOpacity={1}
          onPress={onClose}
        />
        <Animated.View
          style={styles.modalContent}
          entering={FadeIn.duration(200)}
          exiting={FadeOut.duration(200)}
        >
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Sort By</Text>
            <IconButton
              icon="close"
              size={20}
              onPress={onClose}
              style={styles.closeButton}
              iconColor={NEUTRAL.TEXT_PRIMARY}
            />
          </View>

          {sortGroups.length === 0 ? (
            <View style={styles.noOptionsContainer}>
              <Text style={styles.noOptionsText}>No sort options available</Text>
            </View>
          ) : (
            <View style={styles.twoColumnContainer}>
              <View style={styles.tabColumn}>
                {sortGroups.map((group) => {
                  const isActive = expandedGroup === group.id;
                  const isSelected = group.options.some(option => option.id === localSelectedSort);

                  return (
                    <TouchableOpacity
                      key={group.id}
                      style={[
                        styles.tabItem,
                        isActive && styles.activeTabItem,
                        isSelected && styles.tabItemWithSelection
                      ]}
                      onPress={() => setExpandedGroup(group.id)}
                    >
                      <Text
                        style={[
                          styles.tabItemText,
                          isActive && styles.activeTabItemText,
                          isSelected && styles.tabItemTextWithSelection
                        ]}
                      >
                        {group.title}
                      </Text>
                    </TouchableOpacity>
                  );
                })}
              </View>

              <View style={styles.optionsColumn}>
                <ScrollView
                  style={styles.optionsScrollContainer}
                  contentContainerStyle={styles.optionsContainer}
                  showsVerticalScrollIndicator={false}
                >
                  {expandedGroup && (() => {
                    const activeGroup = sortGroups.find(g => g.id === expandedGroup);
                    if (!activeGroup) return null;

                    return activeGroup.options.map((option) => {
                      const isSelected = localSelectedSort === option.id;
                      const cardStyle = [
                        styles.optionCard,
                        isSelected && styles.selectedOptionCard
                      ];
                      const textStyle = [
                        styles.optionText,
                        isSelected && styles.selectedOptionText
                      ];
                      const iconName = isSelected ? "radiobox-marked" : "radiobox-blank";
                      const iconColor = isSelected ? theme.colors.primary : NEUTRAL.TEXT_SECONDARY;

                      return (
                        <Surface
                          key={option.id}
                          style={cardStyle}
                          elevation={0}
                        >
                          <TouchableOpacity
                            onPress={() => handleSortSelect(option.id)}
                            style={styles.optionTouchable}
                            activeOpacity={0.7}
                          >
                            <View style={styles.optionContent}>
                              <View style={styles.optionIconContainer}>
                                <MaterialCommunityIcons
                                  name={iconName}
                                  size={20}
                                  color={iconColor}
                                />
                              </View>
                              <View style={styles.optionTextContainer}>
                                <Text style={textStyle}>
                                  {option.label}
                                </Text>
                              </View>
                            </View>
                          </TouchableOpacity>
                        </Surface>
                      );
                    });
                  })()}
                </ScrollView>
              </View>
            </View>
          )}

          <View style={styles.buttonsContainer}>
            <TouchableOpacity
              style={[
                styles.clearButton,
                !localSelectedSort && styles.disabledClearButton
              ]}
              onPress={handleClearSort}
              disabled={!localSelectedSort}
            >
              <Text style={[
                styles.clearButtonText,
                !localSelectedSort && styles.disabledClearButtonText
              ]}>
                Clear All
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.applyButton}
              onPress={handleApplySort}
            >
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

export default memo(SortModal);
