import React from 'react';
import { View, Text } from 'react-native';
import { orderPriceDisplayStyles } from '../styles/OrderPriceDisplay';

interface PriceDisplayProps {
  label: string;
  value: string;
  color: string;
}

const PriceDisplay: React.FC<PriceDisplayProps> = React.memo(({ label, value, color }) => (
  <View style={orderPriceDisplayStyles.container}>
    <Text style={[orderPriceDisplayStyles.priceText, { color: '#666' }]} numberOfLines={1}>{label}</Text>
    <Text style={[orderPriceDisplayStyles.priceText, { color }]} numberOfLines={1}>{value}</Text>
  </View>
));

PriceDisplay.displayName = 'PriceDisplay';

export default PriceDisplay;
