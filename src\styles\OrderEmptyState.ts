import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const orderEmptyStateStyles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.XLARGE,
    paddingVertical: MD.SPACING.XLARGE * 2,
    minHeight: 300,
  },
  emptyIcon: {
    backgroundColor: `${BRAND.PRIMARY}20`,
    marginBottom: MD.SPACING.LARGE,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL,
    letterSpacing: MD.TYPOGRAPHY.H2.letterSpacing,
  },
  emptySubText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: MD.SPACING.LARGE,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  clearButton: {
    marginTop: MD.SPACING.MEDIUM,
    borderColor: BRAND.PRIMARY,
    borderWidth: 1,
  },
});
