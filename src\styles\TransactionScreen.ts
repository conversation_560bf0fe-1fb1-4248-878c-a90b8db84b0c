import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const transactionScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE,
  },
  content: {
    flex: 1,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.XLARGE,
    paddingVertical: MD.SPACING.XLARGE,
  },
  errorText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginTop: MD.SPACING.MEDIUM,
    marginBottom: MD.SPACING.LARGE,
  },
  retryButton: {
    marginTop: MD.SPACING.MEDIUM,
    minWidth: 120,
  },
});


