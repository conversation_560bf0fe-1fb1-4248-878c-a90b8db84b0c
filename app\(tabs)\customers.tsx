import React, { useState, useCallback, useMemo, useRef } from 'react';
import { View, InteractionManager } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from 'expo-router';
import { CustomerList } from '../../src/components/CustomerList';
import FilterModal from '../../src/components/FilterModal';
import SortModal from '../../src/components/SortModal';
import FilterBar, { FilterOption } from '../../src/components/FilterBar';
import PageHeader from '../../src/components/PageHeader';

import { useSearchFilter } from '../../src/hooks/useSearchFilter';
import { useCustomerData } from '../../src/hooks/useCustomerData';
import { useCustomerNavigation } from '../../src/hooks/useCustomerNavigation';
import { Customer } from '../../src/types/business';
import { customerScreenStyles } from '../../src/styles/CustomerScreen';
import {
  CUSTOMER_FILTER_FUNCTIONS,
  CUSTOMER_SEARCH_CONFIG,
  getActiveCustomerFilters,
  getActiveCustomerSort,
  generateCustomerFilterGroups,
  generateCustomerSortGroups,
  sortCustomers
} from '../../src/constants/filters';

const CustomersScreen = () => {
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [sortModalVisible, setSortModalVisible] = useState(false);
  const [selectedSort, setSelectedSort] = useState<string>('');
  const insets = useSafeAreaInsets();

  const [isNavigationLoading, setIsNavigationLoading] = useState(false);
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { customers, loading, refreshing, handleRefresh } = useCustomerData();
  const { handlePlaceOrder, handleNavigateToInvoices } = useCustomerNavigation();


  const {
    filteredData: filteredCustomers,
    selectedFilters,
    handleApplyFilters,
    handleClearAll,
    handleRemoveFilter: handleRemoveFilterOriginal,
    searchProps,
  } = useSearchFilter<Customer>({
    mode: 'local',
    data: customers,
    searchFields: CUSTOMER_SEARCH_CONFIG.fields,
    searchFormatter: CUSTOMER_SEARCH_CONFIG.formatter,
    filterFunctions: CUSTOMER_FILTER_FUNCTIONS,

    placeholder: CUSTOMER_SEARCH_CONFIG.placeholder,
    debounceTime: CUSTOMER_SEARCH_CONFIG.debounceTime,
  });

  const activeFilters = useMemo<FilterOption[]>(() => {
    const filters = getActiveCustomerFilters(selectedFilters);
    const sortFilter = getActiveCustomerSort(selectedSort);

    return sortFilter ? [...filters, sortFilter] : filters;
  }, [selectedFilters, selectedSort]);

  const filterGroups = useMemo(() => {
    return generateCustomerFilterGroups(customers);
  }, [customers]);

  const sortGroups = useMemo(() => {
    return generateCustomerSortGroups();
  }, []);

  const sortedAndFilteredCustomers = useMemo(() => {
    return sortCustomers(filteredCustomers, selectedSort);
  }, [filteredCustomers, selectedSort]);

  const handleSortApply = useCallback((sortId: string) => {
    setSelectedSort(sortId);
  }, []);

  const handleSortClear = useCallback(() => {
    setSelectedSort('');
  }, []);

  const handleRemoveFilter = useCallback((filterId: string) => {
    if (filterId === selectedSort) {
      setSelectedSort('');
    } else {
      handleRemoveFilterOriginal(filterId);
    }
  }, [selectedSort, handleRemoveFilterOriginal]);

  const handleClearAllFilters = useCallback(() => {
    // Clear sort
    setSelectedSort('');
    // Clear filters and search (handleClearAll now clears search too)
    handleClearAll();
  }, [handleClearAll]);

  useFocusEffect(
    useCallback(() => {
      // Only show navigation loading if we're actually loading and have no data
      // This prevents flickering when returning from other screens
      if (loading && (!customers || customers.length === 0)) {
        setIsNavigationLoading(true);

        if (navigationTimeoutRef.current) {
          clearTimeout(navigationTimeoutRef.current);
        }

        navigationTimeoutRef.current = setTimeout(() => {
          setIsNavigationLoading(false);
        }, 100);
      } else {
        // If we have data, don't show loading state
        setIsNavigationLoading(false);
      }

      return () => {
        if (navigationTimeoutRef.current) {
          clearTimeout(navigationTimeoutRef.current);
        }
      };
    }, [loading, customers])
  );

  return (
    <View style={[customerScreenStyles.container, { paddingTop: insets.top }]}>
      <View style={customerScreenStyles.headerContainer}>
        <PageHeader
          title="Customers"
          features={{
            search: searchProps,
            sort: {
              onPress: () => setSortModalVisible(true),
              isActive: selectedSort !== '',
            },
            filter: {
              onPress: () => setFilterModalVisible(true),
              isActive: selectedFilters.length > 0,
            },
          }}
        />
      </View>

      {activeFilters.length > 0 && (
        <FilterBar filters={activeFilters} onRemoveFilter={handleRemoveFilter} />
      )}

      <View style={{ flex: 1 }}>
        <CustomerList
          customers={(loading || isNavigationLoading) ? [] : sortedAndFilteredCustomers}
          loading={loading || isNavigationLoading}
          refreshing={refreshing}
          onRefresh={handleRefresh}
          onPlaceOrder={handlePlaceOrder}
          onNavigateToInvoices={handleNavigateToInvoices}
          searchQuery={searchProps?.value}
          selectedFilters={selectedFilters}
          selectedSort={selectedSort}
          onClearFilters={handleClearAllFilters}
        />
      </View>

      <SortModal
        visible={sortModalVisible}
        onClose={() => setSortModalVisible(false)}
        sortGroups={sortGroups}
        selectedSort={selectedSort}
        onApplySort={handleSortApply}
        onClearSort={handleSortClear}
      />

      <FilterModal
        visible={filterModalVisible}
        onClose={() => setFilterModalVisible(false)}
        filterGroups={filterGroups}
        selectedFilters={selectedFilters}
        onApplyFilters={(filters: string[]) => {
          InteractionManager.runAfterInteractions(() => {
            handleApplyFilters(filters);
          });
        }}
        onClearFilters={() => {
          handleClearAll();
        }}
      />
    </View>
  );
};

export default CustomersScreen;
