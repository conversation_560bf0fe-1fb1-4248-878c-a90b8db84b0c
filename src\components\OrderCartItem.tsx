import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { OrderItem, CartItem } from '../types/business';

import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import QuantityControl from './QuantityControl';
import OrderPriceDisplay from './OrderPriceDisplay';
import OrderUomButton from './OrderUomButton';

interface CartItemComponentProps {
  item: CartItem;
  itemDetails?: OrderItem;
  updateQuantity: (itemCode: string, quantityOrFn: number | ((prevQty: number) => number)) => void;
  changeUom: (itemCode: string) => void;
  styles: any; // We'll pass styles from the parent for now
}

// Optimized CartItemComponent with React.memo
const CartItemComponent: React.FC<CartItemComponentProps> = React.memo(({
  item,
  itemDetails,
  updateQuantity,
  changeUom,
  styles
}) => {
  if (!itemDetails) return null;

  const { item_code: itemCode, item_name: itemName, quantity, selectedUom: displayUom } = item;

  // Simple UOM info calculation
  const uomInfo = itemDetails.uoms?.find(u => u.uom === displayUom);
  const rate = uomInfo?.rate || 0;
  const amount = quantity * rate;

  // Simple formatting
  const rateText = rate === 0 ? '0' : Math.floor(rate).toLocaleString();
  const amountText = amount === 0 ? '-' : Math.floor(amount).toLocaleString();

  // Simple actual quantity calculation
  const actualQty = itemDetails.actual_qty || 0;
  const conversionFactor = uomInfo?.conversion_factor || 1;
  const convertedActualQty = actualQty / conversionFactor;
  const actualQtyText = convertedActualQty === 0 ? '0' :
    Number.isInteger(convertedActualQty) ? convertedActualQty.toString() : convertedActualQty.toFixed(2);

  // Simple handlers
  const handleIncrement = () => updateQuantity(itemCode, quantity + 1);
  const handleDecrement = () => quantity > 0 && updateQuantity(itemCode, quantity - 1);
  const handleDirectInput = (newQuantity: number) => updateQuantity(itemCode, newQuantity);
  const handleUomChange = () => changeUom(itemCode);
  const handleDelete = () => updateQuantity(itemCode, 0);

  // Simple render
  return (
    <View style={styles.itemContainer}>
      {quantity > 0 && (
        <TouchableOpacity onPress={handleDelete} style={styles.deleteButton}>
          <MaterialCommunityIcons name="delete-outline" size={20} color={SEMANTIC.ERROR} />
        </TouchableOpacity>
      )}

      <Text style={styles.itemName} numberOfLines={2} ellipsizeMode="tail">{itemName}</Text>

      <View style={styles.itemDetailsRow}>
        <OrderPriceDisplay label="Rate" value={rateText} color={BRAND.PRIMARY} />
        <OrderPriceDisplay label="Amount" value={amountText} color={amount > 0 ? SEMANTIC.SUCCESS : NEUTRAL.TEXT_SECONDARY} />
      </View>

      <View style={styles.controlsContainer}>
        <OrderUomButton uom={displayUom} onPress={handleUomChange} />
        <View style={styles.centerControls}>
          <QuantityControl
            quantity={quantity}
            onIncrement={handleIncrement}
            onDecrement={handleDecrement}
            onDirectInput={handleDirectInput}
            allowDirectInput={true}
          />
        </View>
        <View style={styles.actualQtyContainer}>
          <Text style={styles.actualQtyLabel}>Act Qty</Text>
          <Text style={styles.actualQtyValue}>{actualQtyText}</Text>
        </View>
      </View>
    </View>
  );
}, (prevProps, nextProps) => {
  // Only re-render if these specific properties change
  return (
    prevProps.item.item_code === nextProps.item.item_code &&
    prevProps.item.quantity === nextProps.item.quantity &&
    prevProps.item.selectedUom === nextProps.item.selectedUom &&
    prevProps.itemDetails?.actual_qty === nextProps.itemDetails?.actual_qty
  );
});

export default CartItemComponent;
