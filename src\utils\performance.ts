export class PerformanceTracker {
  static start(label: string): void {
    if (__DEV__) console.time(label);
  }

  static end(label: string): void {
    if (__DEV__) console.timeEnd(label);
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
