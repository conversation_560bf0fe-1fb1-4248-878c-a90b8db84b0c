/**
 * Material Design Constants
 *
 * This file contains constants for Material Design specifications.
 * These constants ensure consistent styling across the application.
 */

import { NEUTRAL, BRAND } from './colors';

/**
 * Material Design constants for spacing, typography, and other design elements
 */
const MD = {
  // Elevation
  ELEVATION: {
    NONE: 0,
    LIGHT: 1,
    MEDIUM: 2,
    HIGH: 3,
    DIALOG: 5,
  },

  // Border radius
  BORDER_RADIUS: {
    NONE: 0,
    XSMALL: 2,
    SMALL: 4,
    MEDIUM: 8,
    LARGE: 16,
    XLARGE: 24,
    PILL: 9999,
  },

  // Spacing
  SPACING: {
    NONE: 0,
    XXSMALL: 2,
    XSMALL: 4,
    SMALL: 8,
    MEDIUM: 12,
    LARGE: 16,
    XLARGE: 24,
    XXLARGE: 32,
    XXXLARGE: 48,
  },

  // Typography
  TYPOGRAPHY: {
    H1: {
      fontSize: 20,
      fontWeight: '500',
      letterSpacing: 0.15,
    },
    H2: {
      fontSize: 16,
      fontWeight: '500',
      letterSpacing: 0.15,
    },
    SUBTITLE1: {
      fontSize: 16,
      fontWeight: '400',
      letterSpacing: 0.15,
    },
    SUBTITLE2: {
      fontSize: 14,
      fontWeight: '500',
      letterSpacing: 0.1,
    },
    BODY1: {
      fontSize: 14,
      fontWeight: '400',
      letterSpacing: 0.15,
    },
    BODY2: {
      fontSize: 12,
      fontWeight: '400',
      letterSpacing: 0.25,
    },
    BUTTON: {
      fontSize: 14,
      fontWeight: '500',
      letterSpacing: 0.25,
      textTransform: 'uppercase',
    },
    CAPTION: {
      fontSize: 10,
      fontWeight: '400',
      letterSpacing: 0.4,
    },
    OVERLINE: {
      fontSize: 10,
      fontWeight: '500',
      letterSpacing: 1.5,
      textTransform: 'uppercase',
    },
  },

  // Dividers
  DIVIDER: 'rgba(0, 0, 0, 0.06)',
  DIVIDER_DARK: 'rgba(0, 0, 0, 0.12)',

  // Skeleton
  SKELETON_BG: 'rgba(0, 0, 0, 0.12)',

  // Shadows
  SHADOW: {
    LIGHT: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.18,
      shadowRadius: 1.0,
      elevation: 1,
    },
    MEDIUM: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
      elevation: 2,
    },
    HIGH: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.27,
      shadowRadius: 4.65,
      elevation: 3,
    },
    DIALOG: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 5 },
      shadowOpacity: 0.34,
      shadowRadius: 6.27,
      elevation: 5,
    },
  },

  // Note: Animation constants moved to ui.ts for better organization

  // Colors
  COLORS: {
    PRIMARY: BRAND.PRIMARY,
    SURFACE: NEUTRAL.SURFACE,
    BACKGROUND: NEUTRAL.BACKGROUND,
    TEXT_PRIMARY: NEUTRAL.TEXT_PRIMARY,
    TEXT_SECONDARY: NEUTRAL.TEXT_SECONDARY,
    TEXT_DISABLED: NEUTRAL.TEXT_DISABLED,
    DIVIDER: NEUTRAL.DIVIDER,
  },
};

export default MD;
