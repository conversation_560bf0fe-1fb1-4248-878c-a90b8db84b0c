import { ROUTES } from '../constants/routes';
import { hasStoredSession, validateSession } from './session';
import { initializeSecurity } from '../utils/securityInitializer';
import { AppInitializationOptions, InitializationState } from '../types/initialization';
import { ErrorType } from '../utils/errors';
import { cacheService } from '../utils/cacheService';
import { initializeDatabaseOnStartup } from '../utils/sqliteService';
import { performanceMonitor } from '../utils/performanceMonitor';

interface InitializationMetrics {
  startTime: number;
  sessionCheckTime?: number;
  validationTime?: number;
  totalTime?: number;
}

const isNetworkError = (error: any): boolean => {
  return error?.type === ErrorType.NETWORK || error?.type === ErrorType.TIMEOUT;
};

export const initializeApp = async (options: AppInitializationOptions = {}): Promise<string> => {
  const { onError } = options;
  const metrics: InitializationMetrics = { startTime: Date.now() };

  performanceMonitor.start('APP_LAUNCH');

  try {
    const [hasSession, securityResult, databaseResult] = await Promise.allSettled([
      hasStoredSession(),
      initializeSecurity().catch(() => null),
      initializeDatabaseOnStartup().catch(() => null)
    ]);

    metrics.sessionCheckTime = Date.now() - metrics.startTime;

    if (hasSession.status === 'fulfilled' && !hasSession.value) {
      return ROUTES.LOGIN;
    }

    if (hasSession.status === 'rejected') {
      return ROUTES.LOGIN;
    }

    const validationStart = Date.now();
    const sessionResult = await Promise.race([
      validateSession(),
      new Promise<{ isValid: false; shouldRedirectToLogin: true; error: Error }>((_, reject) =>
        setTimeout(() => reject(new Error('Session validation timeout')), 5000)
      )
    ]);

    metrics.validationTime = Date.now() - validationStart;

    if (!sessionResult.isValid) {
      if (sessionResult.shouldRedirectToLogin) {
        return ROUTES.LOGIN;
      }

      if (sessionResult.error) {
        onError?.(sessionResult.error, 'Session Validation');
        if (isNetworkError(sessionResult.error)) throw sessionResult.error;
      }
      return ROUTES.LOGIN;
    }

    cacheService.preloadCriticalData().catch(() => {});

    metrics.totalTime = Date.now() - metrics.startTime;

    performanceMonitor.end('APP_LAUNCH');

    return ROUTES.HOME_FULL;
  } catch (error) {
    onError?.(error as Error, 'App Initialization');

    if (isNetworkError(error)) throw error;
    return ROUTES.LOGIN;
  }
};

export const createInitialState = (): InitializationState => ({
  isInitializing: false,
  isInitialized: false,
  error: null,
  redirectTo: null
});




