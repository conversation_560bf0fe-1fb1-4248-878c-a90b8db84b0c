import React from 'react';
import { View, Text } from 'react-native';

interface PriceDisplayProps {
  label: string;
  value: string;
  color: string;
  styles: {
    itemDetailColumn: any;
    itemDetailLabel: any;
    itemDetailValue: any;
  };
}

/**
 * Pure component for displaying price information
 * Used in both cart and checkout screens
 */
const PriceDisplay = React.memo<PriceDisplayProps>(({ label, value, color, styles }) => (
  <View style={styles.itemDetailColumn}>
    <Text style={styles.itemDetailLabel}>{label}</Text>
    <Text style={[styles.itemDetailValue, { color }]}>{value}</Text>
  </View>
));

PriceDisplay.displayName = 'PriceDisplay';

export default PriceDisplay;
