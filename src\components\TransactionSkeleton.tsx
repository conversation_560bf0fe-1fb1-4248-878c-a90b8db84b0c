import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';
import { transactionSkeletonStyles } from '../styles/TransactionSkeleton';

interface TransactionSkeletonProps {
  count?: number;
}

const TransactionSkeleton: React.FC<TransactionSkeletonProps> = ({ count = 5 }) => {
  // Optimized skeleton item component
  const SkeletonItem = useCallback(() => (
    <View style={transactionSkeletonStyles.skeletonListItem}>
      <View style={transactionSkeletonStyles.skeletonCardContent}>
        {/* Transaction ID and Date */}
        <View style={transactionSkeletonStyles.skeletonCardHeader}>
          <View style={transactionSkeletonStyles.skeletonIdContainer}>
            <View style={transactionSkeletonStyles.skeletonTransactionId} />
          </View>
          <View style={transactionSkeletonStyles.skeletonDate} />
        </View>

        {/* Customer name */}
        <View style={transactionSkeletonStyles.skeletonCustomer} />

        {/* Status and Amount */}
        <View style={transactionSkeletonStyles.skeletonStatusAmountContainer}>
          <View style={transactionSkeletonStyles.skeletonStatusChip} />
          <View style={transactionSkeletonStyles.skeletonAmountContainer}>
            <View style={transactionSkeletonStyles.skeletonAmount} />
          </View>
        </View>
      </View>
    </View>
  ), []);

  // Memoized skeleton items array
  const skeletonItems = useMemo(() =>
    Array.from({ length: count }, (_, index) => (
      <SkeletonItem key={`skeleton-${index}`} />
    )), [count, SkeletonItem]);

  return <>{skeletonItems}</>;
};

export default React.memo(TransactionSkeleton);
