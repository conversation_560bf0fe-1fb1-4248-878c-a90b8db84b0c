import React, { useMemo, useState } from 'react';
import { View, ScrollView, TouchableOpacity, Text } from 'react-native';
import { SimplifiedSidebarProps } from '../types/business';
import { unifiedSidebarStyles } from '../styles/UnifiedSidebar';
import UnifiedSidebarSkeleton from './UnifiedSidebarSkeleton';

const UnifiedSidebar: React.FC<SimplifiedSidebarProps> = React.memo(({
  selectedValue,
  availableItems,
  itemCounts,
  onItemSelect,
  loading = false
}) => {
  const styles = unifiedSidebarStyles;
  const [pressedItem, setPressedItem] = useState<string | null>(null);

  // Calculate total count for "All" option - MUST be before any conditional returns
  const totalCount = useMemo(() => {
    return Object.values(itemCounts).reduce((sum, count) => sum + count, 0);
  }, [itemCounts]);

  // Only show skeleton when actively loading - AFTER all hooks
  if (loading) {
    return <UnifiedSidebarSkeleton />;
  }

  const handleItemPress = (item: string | null) => {
    // AGGRESSIVE: Immediate visual feedback with zero delay
    setPressedItem(item);

    // Call the actual item selection immediately
    onItemSelect(item);

    // Clear pressed state faster for more responsive feel
    requestAnimationFrame(() => {
      setTimeout(() => setPressedItem(null), 100);
    });
  };

  // No header needed - filter type is shown in tabs above

  return (
    <View style={styles.sidebarContainer}>
      {/* Scrollable Content - no redundant header needed */}
      <ScrollView
        style={styles.sidebarContent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* All Items Tab */}
        <TouchableOpacity
          style={[
            styles.itemTab,
            selectedValue === null && styles.selectedItemTab,
            pressedItem === null && styles.pressedItemTab
          ]}
          onPress={() => handleItemPress(null)}
          activeOpacity={0.7}
        >
          <View style={styles.tabContent}>
            <Text style={[
              styles.itemTabText,
              selectedValue === null && styles.selectedItemTabText
            ]}>
              All
            </Text>
            <Text style={[
              styles.itemCount,
              selectedValue === null && styles.selectedItemCount
            ]}>
              {totalCount}
            </Text>
          </View>
          {selectedValue === null && <View style={styles.activeIndicator} />}
        </TouchableOpacity>

        {/* Divider after "All" tab */}
        {availableItems.length > 0 && <View style={styles.itemDivider} />}

        {/* Individual Items */}
        {availableItems.map((item, index) => (
          <React.Fragment key={item}>
            <TouchableOpacity
              style={[
                styles.itemTab,
                selectedValue === item && styles.selectedItemTab,
                pressedItem === item && styles.pressedItemTab
              ]}
              onPress={() => handleItemPress(item)}
              activeOpacity={0.7}
            >
              <View style={styles.tabContent}>
                <Text style={[
                  styles.itemTabText,
                  selectedValue === item && styles.selectedItemTabText
                ]} numberOfLines={2}>
                  {item}
                </Text>
                <Text style={[
                  styles.itemCount,
                  selectedValue === item && styles.selectedItemCount
                ]}>
                  {itemCounts[item] || 0}
                </Text>
              </View>
              {selectedValue === item && <View style={styles.activeIndicator} />}
            </TouchableOpacity>
            {/* Divider between items (not after the last one) */}
            {index < availableItems.length - 1 && <View style={styles.itemDivider} />}
          </React.Fragment>
        ))}
      </ScrollView>
    </View>
  );
}, (prevProps, nextProps) => {
  // Fast equality checks for primitive values
  if (prevProps.selectedValue !== nextProps.selectedValue) return false;
  if (prevProps.onItemSelect !== nextProps.onItemSelect) return false;
  if (prevProps.loading !== nextProps.loading) return false;

  // Check array lengths first (fastest)
  if (prevProps.availableItems.length !== nextProps.availableItems.length) return false;

  // Check array contents
  for (let i = 0; i < prevProps.availableItems.length; i++) {
    if (prevProps.availableItems[i] !== nextProps.availableItems[i]) return false;
  }

  // Check counts for current items
  for (const item of prevProps.availableItems) {
    if (prevProps.itemCounts[item] !== nextProps.itemCounts[item]) return false;
  }

  return true;
});

export default UnifiedSidebar;
