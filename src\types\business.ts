import React from 'react';

// ===== Customer Types =====
export interface Address {
  name: string;
  address_line1: string;
  city: string;
  custom_is_gps_verified: number;
  custom_latitude: string;
  custom_longitude: string;
}

export interface Customer {
  name: string;
  customer_name: string;
  territory: string;
  customer_group: string;
  tax_id?: string | null;
  mobile_no?: string | null;
  custom_credit_limit?: number;
  outstanding?: number;
  customer_primary_address?: string;
  addresses?: Address[];
  default_price_list?: string;
}

// Customer financial info for checkout
export interface CustomerInfo {
  tax_id?: string;
  custom_credit_limit: number;
  outstanding: number;
  default_price_list?: string;
  mobile_no?: string;
  customer_primary_address?: string;
}

// ===== Item Types =====
export interface Item {
  item_code: string;
  item_name: string;
  item_group: string;
  brand: string;
  sales_uom: string;
  stock_uom: string;
  default_supplier: string;
  supplier_short_name: string;
  actual_qty?: number;
  uoms: { uom: string; conversion_factor: number; rate: number }[];
}

// ===== Item Price Types =====
export interface ItemPrice {
  conversion_factor: number;
  customer_group: string;
  item_code: string;
  rate: number;
  territory: string;
  uom: string;
}

// ===== Transaction Types =====
// Transaction fetch parameters
export interface FetchTransactionsParams {
  page?: number;
  pageSize?: number;
  filters?: any[];
  searchQuery?: string;
  selectedStatuses?: string[];
}

// Common base transaction type
export interface BaseTransaction {
  name: string;
  customer_name: string;
  status: string;
  base_total: number;
  base_net_total: number;
  base_total_taxes_and_charges: number;
  base_grand_total: number;
  additional_discount_percentage?: number;
  discount_amount?: number;
  apply_discount_on?: string;
  tax_id?: string;
}

// Common transaction item type
export interface TransactionItem {
  name: string;
  item_code: string;
  item_name: string;
  qty: number;
  rate: number;
  amount: number;
  uom: string;
  base_net_rate?: number;
  idx?: number;
}

// Sales Order Types
export interface SalesOrderItem extends TransactionItem {
  // No additional fields needed beyond TransactionItem
}

export interface SalesOrder extends BaseTransaction {
  transaction_date: string;
  items?: SalesOrderItem[];
  po_no?: string;
  payment_terms_template?: string;
}

export interface SalesOrderPayload {
  customer: string;
  company: string;
  items: { item_code: string; qty: number; uom: string }[];
  transaction_date: string;
  delivery_date: string;
  taxes_and_charges?: string;
  selling_price_list?: string;
}

// Order creation response
export interface OrderCreationResponse {
  name: string;
  customer: string;
  total: number;
  status: string;
}

// Order Creation Types
export interface OrderItem {
  item_code: string;
  item_name: string;
  item_group: string;
  brand: string;
  sales_uom: string;
  stock_uom: string;
  default_supplier: string;
  supplier_short_name: string;
  actual_qty?: number; // Actual quantity from bin data
  uoms: {
    uom: string;
    conversion_factor: number;
    rate: number;
  }[];
}

export interface CartItem {
  item_code: string;
  item_name: string;
  item_group: string;
  quantity: number;
  selectedUom: string;
  conversionFactor: number;
  rate?: number | string;
  orderIndex?: number; // Index to maintain order of items as they were added
  customerId?: string;
  customerName?: string;
}

export interface CartData {
  customerId: string;
  customerName: string;
  items: CartItem[];
  timestamp: number;
  lastUpdated?: string;
}

// Sales Invoice Types
export type SalesInvoiceItem = TransactionItem;

export interface SalesInvoice extends BaseTransaction {
  posting_date: string;
  due_date: string;
  outstanding_amount: number;
  items?: SalesInvoiceItem[];
  fiscal_document_number?: string;
  payment_terms_template?: string;
}

// Union type for transactions (used in components)
export type Transaction = SalesOrder | SalesInvoice;

// ===== Bin Data Types =====
export interface BinData {
  item_code: string;
  actual_qty: number;
}

// ===== Version Check Types =====
export interface VersionInfo {
  current_version: string;
  download_url: string;
  forced_update: boolean;
  release_notes: string[];
  file_size_mb: number;
  release_date: string; // Format: "14-05-2025"
}

export interface UpdateAction {
  type: 'none' | 'optional' | 'forced';
  reason: string;
  versionInfo: VersionInfo;
}

// ===== Navigation Types =====
export interface NavigationOptions {
  fromCart?: boolean;
  originalSource?: string;
  customerId: string;
  customerName: string;
  items: CartItem[];
}

// ===== Screen Parameter Types =====
export interface CartScreenParams {
  customerId?: string;
  customerName?: string;
  items?: string; // JSON string of CartItem[]
}

export interface CheckoutScreenParams {
  customerId: string;
  customerName: string;
  items: string; // JSON string of CartItem[]
  tax_id?: string;
  custom_credit_limit?: string;
  outstanding?: string;
  default_price_list?: string;
  mobile_no?: string;
  customer_primary_address?: string;
  from?: 'cart' | 'create-order';
  originalSource?: string;
}

export interface CreateOrderScreenParams {
  customerId: string;
  customerName: string;
  territory?: string;
  customerGroup?: string;
  from?: 'cart' | 'customer';
}

// ===== Function Types =====
export type QuantityUpdater = (prevQty: number) => number;
export type QuantityUpdateFunction = (
  itemCode: string,
  newQuantityOrFn: number | QuantityUpdater
) => Promise<void>;
export type UomChangeFunction = (itemCode: string) => void;

// Cart operation function types
export type CartCheckoutFunction = (cart: CartData) => Promise<void>;
export type CartContinueShoppingFunction = (customerId: string, customerName: string) => void;
export type CartRemoveFunction = (customerId: string) => Promise<void>;
export type CartToggleExpandFunction = (customerId: string) => void;

// ===== Hook Return Types =====
export interface UseCartManagerReturn {
  // State
  loading: boolean;
  carts: CartData[];
  cartItems: CartItem[];
  expandedCart: string | null;
  cartCount: number;
  cartCacheKey: string;

  // Setters
  setCarts: React.Dispatch<React.SetStateAction<CartData[]>>;
  setCartItems: React.Dispatch<React.SetStateAction<CartItem[]>>;
  setExpandedCart: React.Dispatch<React.SetStateAction<string | null>>;

  // Cart list operations
  loadAllCarts: () => Promise<void>;
  handleNewCartItems: (params: CartScreenParams) => Promise<void>;

  // Individual cart operations
  saveCartData: () => Promise<CartItem[]>;
  loadCartData: () => Promise<CartItem[]>;
  clearCart: () => Promise<void>;
  updateQuantity: (itemCode: string, quantityOrFn: number | ((prevQty: number) => number)) => void;
  addToCart: (item: CartItem) => void;

  // Cart actions
  handleCheckout: (cart: CartData) => Promise<void>;
  handleContinueShopping: (cart: CartData) => void;
  handleRemoveCart: (cart: CartData) => Promise<void>;
  handleToggleExpand: (cartId: string) => void;
}

export interface CartLoadingResult {
  customerId: string;
  customerName: string;
  items: CartItem[];
  timestamp: number;
  lastUpdated: string;
}

export interface CartValidationResult {
  isValid: boolean;
  hasActiveItems: boolean;
  itemCount: number;
  totalQuantity: number;
}

export interface CartDisplayInfo {
  itemCount: number;
  totalQuantity: number;
  totalAmount: number;
  lastUpdated: string;
  timestamp: number;
}



export interface UseCheckoutCartReturn {
  cartItems: CartItem[];
  updateQuantity: QuantityUpdateFunction;
  clearCart: () => void;
  totalItems: number;
  totalAmount: number;
  cartEmpty: boolean;
}

export interface UseCheckoutOrderReturn {
  createOrder: () => Promise<void>;
  loading: boolean;
  confirmPlaceOrder: () => void;
  orderPlaced: boolean;
  placedOrderName: string;
}

export interface UseCheckoutNavigationReturn {
  handleBackPress: () => void;
}

// ===== Component Props Types =====
export interface CartHeaderProps {
  cart: CartData;
  isExpanded: boolean;
  onToggleExpand: () => void;
  totalAmount: number;
}

export interface CartContentProps {
  cart: CartData;
  onCheckout: () => void;
  onContinueShopping: () => void;
  onRemove: () => void;
  styles: any;
}

export interface CartActionsProps {
  onCheckout: () => void;
  onContinueShopping: () => void;
  onRemove: () => void;
  styles: any;
}

export interface CartEmptyStateProps {
  onGoToCustomers: () => void;
  styles: any;
}

export interface CheckoutHeaderProps {
  onBackPress: () => void;
  title: string;
}

export interface CheckoutCustomerInfoProps {
  customerName: string;
  customerInfo: CustomerInfo;
}

export interface CheckoutItemsListProps {
  items: CartItem[];
  updateQuantity: QuantityUpdateFunction;
  changeUom: UomChangeFunction;
  styles: any;
}

export interface CheckoutButtonProps {
  cartCount: number;
  totalAmount: number;
  onPress: () => void;
  disabled?: boolean;
}

export interface CheckoutFooterProps {
  onPlaceOrder: () => void;
  loading: boolean;
  disabled: boolean;
  totalAmount: number;
}



export interface SupplierFilterProps {
  selectedSupplier: string | null;
  availableSuppliers: string[];
  supplierCounts: Record<string, number>;
  onSupplierSelect: (supplier: string | null) => void;
}

export interface SupplierSidebarProps {
  selectedSupplier: string | null;
  availableSuppliers: string[];
  supplierCounts: Record<string, number>;
  onSupplierSelect: (supplier: string | null) => void;
}

export interface UnifiedSidebarProps {
  selectedSupplier: string | null;
  selectedBrand: string | null;
  availableSuppliers: string[];
  availableBrands: string[];
  supplierCounts: Record<string, number>;
  brandCounts: Record<string, number>;
  onSupplierSelect: (supplier: string | null) => void;
  onBrandSelect: (brand: string | null) => void;
  filterMode: 'supplier' | 'brand';
  onFilterModeChange: (mode: 'supplier' | 'brand') => void;
  loading?: boolean;
}

// Simplified sidebar props for the new single-filter approach
export interface SimplifiedSidebarProps {
  selectedValue: string | null;
  availableItems: string[];
  itemCounts: Record<string, number>;
  onItemSelect: (item: string | null) => void;
  loading?: boolean;
}




// ===== Create Order Hook Types =====
export interface UseCreateOrderDataProps {
  customerId: string;
  customerTerritory: string;
  customerGroup: string;
  paramTerritory: string;
  paramCustomerGroup: string;
  cartCacheKey: string;
  onCartItemsReady?: (cartItems: CartItem[]) => void;
}

// Simplified UseCreateOrderDataReturn - only return what's actually needed
export interface UseCreateOrderDataReturn {
  items: OrderItem[];
  loading: boolean;
  error: string | null;
  availableSuppliers: string[];
  supplierCounts: Record<string, number>;
  availableBrands: string[];
  brandCounts: Record<string, number>;
  availableItemGroups: string[];
  itemGroupCounts: Record<string, number>;
  customerTerritory: string;
  customerGroup: string;
  isFilterTransition: boolean;
  allDataReady: boolean;
  fetchItemsList: () => Promise<void>;
  fetchCustomerDetails: () => Promise<any>;
  setItems: React.Dispatch<React.SetStateAction<OrderItem[]>>;
  setIsFilterTransition: React.Dispatch<React.SetStateAction<boolean>>;
}

// Removed unused filter interfaces - now using inline filtering logic

export interface UseCreateOrderNavigationProps {
  customerId: string;
  customerName: string;
  cartItems: CartItem[];
  items: OrderItem[];
  params: any;
  saveCartData: () => Promise<CartItem[]>;
}

export interface UseCreateOrderNavigationReturn {
  isNavigating: boolean;
  handleCheckout: () => Promise<void>;
  handleGoToCart: () => void;
  setupBackHandler: () => () => void;
}
