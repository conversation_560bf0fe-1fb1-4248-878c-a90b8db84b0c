import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';

export const loginFormStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: MD.SPACING.LARGE,
    minHeight: '100%'
  },
  card: {
    padding: MD.SPACING.SMALL,
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
    elevation: MD.ELEVATION.MEDIUM,
    backgroundColor: NEUTRAL.WHITE,
    width: '100%',
    maxWidth: 450,
    alignSelf: 'center'
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: MD.SPACING.LARGE,
    marginTop: MD.SPACING.SMALL
  },
  logo: {
    width: 80,
    height: 80,
  },
  title: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize,
    letterSpacing: MD.TYPOGRAPHY.H1.letterSpacing,
    fontWeight: '700',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.SMALL
  },
  subtitle: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: MD.SPACING.LARGE
  },
  inputContainer: {
    marginBottom: MD.SPACING.MEDIUM
  },
  input: {
    backgroundColor: NEUTRAL.WHITE,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    marginBottom: MD.SPACING.LARGE
  },
  passwordContainer: {
    marginBottom: MD.SPACING.SMALL,
    marginTop: MD.SPACING.SMALL
  },
  rememberMeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: MD.SPACING.LARGE,
    paddingHorizontal: MD.SPACING.XXSMALL
  },
  rememberMeText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    marginLeft: MD.SPACING.SMALL
  },
  loginButton: {
    borderRadius: MD.BORDER_RADIUS.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
    marginBottom: MD.SPACING.MEDIUM
  },
  loginButtonContent: {
    paddingVertical: MD.SPACING.XXSMALL
  },
  loginButtonLabel: {
    fontSize: MD.TYPOGRAPHY.BUTTON.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BUTTON.letterSpacing,
    fontWeight: '600'
  },
  errorContainer: {
    backgroundColor: `${SEMANTIC.ERROR}10`,
    borderLeftWidth: 4,
    borderLeftColor: SEMANTIC.ERROR,
    padding: MD.SPACING.MEDIUM,
    marginBottom: MD.SPACING.MEDIUM,
    borderRadius: MD.BORDER_RADIUS.SMALL
  },
  errorText: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    color: SEMANTIC.ERROR,
    textAlign: 'center'
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  loadingText: {
    fontSize: MD.TYPOGRAPHY.BUTTON.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BUTTON.letterSpacing,
    fontWeight: '600',
    color: NEUTRAL.WHITE,
    marginLeft: MD.SPACING.SMALL
  },
  // Additional styles for LoginForm component compatibility
  inputIcon: {
    position: 'absolute',
    right: MD.SPACING.MEDIUM,
    top: '50%',
    transform: [{ translateY: -12 }],
    zIndex: 1
  },
  button: {
    marginTop: MD.SPACING.XLARGE,
    width: '100%',
    paddingVertical: MD.SPACING.XSMALL,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  buttonContent: {
    height: 48,
  },
  buttonLabel: {
    fontSize: MD.TYPOGRAPHY.BUTTON.fontSize + 2,
    fontWeight: 'bold',
    letterSpacing: MD.TYPOGRAPHY.BUTTON.letterSpacing
  },
  error: {
    textAlign: 'center',
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize + 2,
    fontWeight: 'bold',
    color: SEMANTIC.ERROR,
    marginVertical: MD.SPACING.SMALL,
  },
  versionText: {
    textAlign: 'center',
    marginTop: MD.SPACING.XXLARGE,
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize + 2,
    opacity: 0.6,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing
  },
  linkText: {
    color: BRAND.PRIMARY,
    textAlign: 'center',
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    marginTop: MD.SPACING.MEDIUM,
  }
});
