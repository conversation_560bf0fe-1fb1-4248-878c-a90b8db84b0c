import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CartActionsProps } from '../types/business';
import { ICON_SIZES } from '../constants/ui';
import { BUTTON_TEXT } from '../constants/messages';
import { BRAND, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';


const CartActions: React.FC<CartActionsProps> = ({
  onCheckout,
  onContinueShopping,
  onRemove,
  styles
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={styles.bottomActionsContainer}>
      <View style={[
        styles.cartActions,
        {
          paddingBottom: Math.max(insets.bottom, 16)
        }
      ]}>
        {/* Delete Cart Button - Now on the left */}
        <TouchableOpacity
          style={styles.removeCartButton}
          onPress={onRemove}
        >
          <MaterialCommunityIcons 
            name="delete-outline" 
            size={ICON_SIZES.SMALL}
            color={SEMANTIC.ERROR} 
          />
          <Text style={styles.removeCartText}>{BUTTON_TEXT.CART.DELETE}</Text>
        </TouchableOpacity>

        {/* Continue Shopping Button - Secondary Action */}
        <TouchableOpacity
          style={styles.continueButton}
          onPress={onContinueShopping}
        >
          <MaterialCommunityIcons 
            name="cart-plus" 
            size={ICON_SIZES.SMALL}
            color={BRAND.PRIMARY} 
            style={styles.buttonIcon} 
          />
          <Text style={styles.continueButtonText}>{BUTTON_TEXT.CART.CONTINUE}</Text>
        </TouchableOpacity>

        {/* Checkout Button - Primary Action */}
        <TouchableOpacity
          style={styles.checkoutButton}
          onPress={onCheckout}
        >
          <Text style={styles.checkoutButtonText}>{BUTTON_TEXT.CART.CHECKOUT}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default React.memo(CartActions);
