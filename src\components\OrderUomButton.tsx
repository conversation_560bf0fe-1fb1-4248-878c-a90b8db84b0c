import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import { orderUomButtonStyles } from '../styles/OrderUomButton';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

interface UomButtonProps {
  uom: string;
  onPress: () => void;
}

const UomButton: React.FC<UomButtonProps> = React.memo(({ uom, onPress }) => (
  <TouchableOpacity onPress={onPress} style={[
    orderUomButtonStyles.uomButton,
    {
      backgroundColor: NEUTRAL.BACKGROUND,
      elevation: 1, // Light elevation for buttons
    }
  ]}>
    <Text style={[
      orderUomButtonStyles.uomButtonText,
      {
        color: NEUTRAL.TEXT_PRIMARY,
        letterSpacing: 0.25, // Material Design button text letter spacing
      }
    ]}>{uom}</Text>
  </TouchableOpacity>
));

UomButton.displayName = 'UomButton';

export default UomButton;
