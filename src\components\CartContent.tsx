import React, { useMemo } from 'react';
import { View, Text } from 'react-native';
import { CartContentProps } from '../types/business';
import { sortCartItemsForCheckout } from '../orchestrators/cart';
import CartItemRenderer from './CartItemRenderer';
import CartActions from './CartActions';

/**
 * Reusable cart content component showing items and actions
 */
const CartContent: React.FC<CartContentProps> = ({
  cart,
  onCheckout,
  onContinueShopping,
  onRemove,
  styles
}) => {
  // 🚀 PERFORMANCE FIX: Memoize sorted items to avoid calling sortCartItemsForCheckout twice
  const sortedItems = useMemo(() => sortCartItemsForCheckout(cart.items), [cart.items]);

  return (
    <View style={styles.cartContent}>
      {/* Cart Items */}
      <View style={styles.itemsContainer}>
        {sortedItems.length > 0 ? (
          sortedItems.map((item, index) => (
            <CartItemRenderer
              key={`${cart.customerId}-${item.item_code}`}
              item={item}
              index={index}
              styles={styles}
            />
          ))
        ) : (
          <Text style={styles.emptyItemsText}>No items in cart</Text>
        )}
      </View>

      {/* Bottom action buttons */}
      <CartActions
        onCheckout={onCheckout}
        onContinueShopping={onContinueShopping}
        onRemove={onRemove}
        styles={styles}
      />
    </View>
  );
};

export default React.memo(CartContent);
