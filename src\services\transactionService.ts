import { apiClient } from './apiClient';
import { SalesOrderPayload, SalesOrder, SalesInvoice, FetchTransactionsParams } from '../types/business';
import { buildTransactionFilters } from '../utils/transactions';
import { API } from '../constants/api';
import { withErrorHandling } from '../utils/errors';

// Resource type definition
type TransactionResourceType = 'Sales Order' | 'Sales Invoice';

// Common fields for transaction items
const TRANSACTION_ITEM_FIELDS = [
  'name', 'item_code', 'item_name', 'qty', 'rate', 'amount', 'uom', 'idx', 'base_net_rate'
];

// Fields for sales orders list view
const SALES_ORDER_LIST_FIELDS = [
  'name',
  'customer',
  'customer_name',
  'transaction_date',
  'base_grand_total',
  'status',
  'creation',
  'total_qty'
];

// Fields for sales invoices list view
const SALES_INVOICE_LIST_FIELDS = [
  'name',
  'customer',
  'customer_name',
  'posting_date',
  'due_date',
  'base_grand_total',
  'status',
  'creation',
  'total_qty',
  'outstanding_amount'
];

// Fields for sales order detail view
const SALES_ORDER_DETAIL_FIELDS = [
  'name', 'customer_name', 'status',
  'transaction_date', 'po_no', 'payment_terms_template',
  'base_total', 'base_net_total', 'base_total_taxes_and_charges', 'base_grand_total',
  'additional_discount_percentage', 'discount_amount', 'apply_discount_on', 'tax_id',
  `items.${TRANSACTION_ITEM_FIELDS.join(',')}`
];

// Fields for sales invoice detail view
const SALES_INVOICE_DETAIL_FIELDS = [
  'name', 'customer_name', 'status',
  'posting_date', 'due_date', 'outstanding_amount',
  'fiscal_document_number', 'payment_terms_template',
  'base_total', 'base_net_total', 'base_total_taxes_and_charges', 'base_grand_total',
  'additional_discount_percentage', 'discount_amount', 'apply_discount_on', 'tax_id',
  `items.${TRANSACTION_ITEM_FIELDS.join(',')}`
];

/**
 * Common function to fetch transactions (orders or invoices)
 */
async function fetchTransactionsCommon<T>(
  resourceType: TransactionResourceType,
  params: FetchTransactionsParams,
  fields: string[]
): Promise<{ data: T[]; total: number }> {
  return withErrorHandling(async () => {
    const {
      page = 1,
      pageSize = 20,
      filters = [],
      searchQuery = '',
      selectedStatuses = []
    } = params;

    const limit_start = (page - 1) * pageSize;
    const finalFilters = filters.length > 0 ? filters : buildTransactionFilters(searchQuery, selectedStatuses);
    const resourceEndpoint = resourceType === 'Sales Order' ? API.ENDPOINTS.SALES_ORDERS : API.ENDPOINTS.SALES_INVOICES;

    // Only log in development mode
    if (__DEV__) {
      console.log(`Fetching ${resourceType} for page ${page}, limit_start=${limit_start}, pageSize=${pageSize}`);
      console.log(`Filters: ${JSON.stringify(finalFilters)}`);
    }

    const url = `${resourceEndpoint}?fields=${encodeURIComponent(JSON.stringify(fields))}&filters=${encodeURIComponent(JSON.stringify(finalFilters))}&limit_start=${limit_start}&limit_page_length=${pageSize}&order_by=${encodeURIComponent('creation desc')}`;

    const response = await apiClient.get(url);

    // Check if we got a valid response
    if (__DEV__ && !response?.data?.data) {
      console.error(`Invalid response for ${resourceType}:`, response?.data);
    }

    const result = {
      data: response?.data?.data || [],
      total: response?.data?.total || (response?.data?.data ? response.data.data.length : 0)
    };

    if (__DEV__) {
      console.log(`Received ${result.data.length} ${resourceType} items for page ${page}, total: ${result.total}`);
    }

    return result;
  });
}

/**
 * Common function to fetch a transaction by ID
 */
async function fetchTransactionById<T>(
  resourceType: TransactionResourceType,
  transactionId: string
): Promise<T> {
  return withErrorHandling(async () => {
    const fields = resourceType === 'Sales Order' ? SALES_ORDER_DETAIL_FIELDS : SALES_INVOICE_DETAIL_FIELDS;
    const resourceEndpoint = resourceType === 'Sales Order' ? API.ENDPOINTS.SALES_ORDERS : API.ENDPOINTS.SALES_INVOICES;
    const url = `${resourceEndpoint}/${encodeURIComponent(transactionId)}?fields=${encodeURIComponent(JSON.stringify(fields))}`;

    const response = await apiClient.get(url);
    return response?.data?.data;
  });
}

/**
 * Fetch sales orders with pagination and filtering
 */
export const fetchSalesOrders = async (params: FetchTransactionsParams): Promise<{ data: SalesOrder[]; total: number }> => {
  return fetchTransactionsCommon<SalesOrder>('Sales Order', params, SALES_ORDER_LIST_FIELDS);
};

/**
 * Fetch a specific sales order by ID
 */
export const fetchSalesOrderById = async (orderId: string): Promise<SalesOrder> => {
  return fetchTransactionById<SalesOrder>('Sales Order', orderId);
};

/**
 * Fetch sales invoices with pagination and filtering
 */
export const fetchSalesInvoices = async (params: FetchTransactionsParams): Promise<{ data: SalesInvoice[]; total: number }> => {
  const result = await fetchTransactionsCommon<SalesInvoice>('Sales Invoice', params, SALES_INVOICE_LIST_FIELDS);

  // Debug log to check the response (development only)
  if (__DEV__) {
    console.log(`Fetched ${result.data.length} invoices for page ${params.page || 1}`,
      result.data.map(invoice => invoice.name).join(', '));
  }

  return result;
};

/**
 * Fetch a specific sales invoice by ID
 */
export const fetchSalesInvoiceById = async (invoiceId: string): Promise<SalesInvoice> => {
  return fetchTransactionById<SalesInvoice>('Sales Invoice', invoiceId);
};

/**
 * Create a new sales order
 */
export const createSalesOrder = async (orderData: SalesOrderPayload): Promise<SalesOrder> => {
  return withErrorHandling(async () => {
    const response = await apiClient.post(API.ENDPOINTS.SALES_ORDERS, orderData, {
      headers: {
        'X-No-Retry': 'true' // Signal to our middleware not to retry this request
      }
    });

    if (response?.data?.data) return response.data.data;
    if (response?.data?.message) return response.data.message;
    if (response?.data) return response.data;
    return {} as SalesOrder;
  });
};
