import React from 'react';
import { TouchableOpacity, Text, View } from 'react-native';

interface UomButtonProps {
  uom: string;
  onPress?: () => void;
  styles: {
    uomButton: any;
    uomButtonText: any;
  };
  disabled?: boolean;
}

// Simplified <PERSON><PERSON> Button - removed unnecessary memo
const UomButton: React.FC<UomButtonProps> = ({ uom, onPress, styles, disabled = false }) => {
  if (onPress && !disabled) {
    return (
      <TouchableOpacity onPress={onPress} style={styles.uomButton}>
        <Text style={styles.uomButtonText}>{uom}</Text>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.uomButton}>
      <Text style={styles.uomButtonText}>{uom}</Text>
    </View>
  );
};

UomButton.displayName = 'UomButton';

export default UomButton;
