import { useState, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { fetchCustomers } from '../services/customerService';
import { getItemsWithPricesWithCache } from '../utils/sqliteService';
import { OrderItem, CartItem, UseCreateOrderDataProps, UseCreateOrderDataReturn } from '../types/business';

// Ultra-fast data processing function - optimized for maximum performance with Sets
const processItemsData = (data: OrderItem[]) => {
  if (!data?.length) {
    return {
      suppliers: [],
      supplierCounts: {},
      brands: [],
      brandCounts: {},
      itemGroups: [],
      itemGroupCounts: {}
    };
  }

  const supplierCounts: Record<string, number> = {};
  const brandCounts: Record<string, number> = {};
  const itemGroupCounts: Record<string, number> = {};
  const supplierSet = new Set<string>();
  const brandSet = new Set<string>();
  const itemGroupSet = new Set<string>();

  // Ultra-fast single-pass processing with Sets for deduplication
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    const supplier = item.supplier_short_name || item.default_supplier || '';
    const brand = item.brand || '';
    const itemGroup = item.item_group || '';

    // Count and collect suppliers (optimized)
    if (supplier) {
      supplierCounts[supplier] = (supplierCounts[supplier] || 0) + 1;
      supplierSet.add(supplier);
    }

    // Count and collect brands (optimized)
    if (brand) {
      brandCounts[brand] = (brandCounts[brand] || 0) + 1;
      brandSet.add(brand);
    }

    // Count and collect item groups (optimized)
    if (itemGroup) {
      itemGroupCounts[itemGroup] = (itemGroupCounts[itemGroup] || 0) + 1;
      itemGroupSet.add(itemGroup);
    }
  }

  // Fast sorting using Sets (avoid Object.keys overhead)
  const suppliers = Array.from(supplierSet).sort((a, b) => supplierCounts[b] - supplierCounts[a]);
  const brands = Array.from(brandSet).sort((a, b) => brandCounts[b] - brandCounts[a]);
  const itemGroups = Array.from(itemGroupSet).sort((a, b) => itemGroupCounts[b] - itemGroupCounts[a]);

  return {
    suppliers,
    supplierCounts,
    brands,
    brandCounts,
    itemGroups,
    itemGroupCounts
  };
};

export const useCreateOrderData = ({
  customerId,
  customerTerritory: initialCustomerTerritory,
  customerGroup: initialCustomerGroup,
  paramTerritory,
  paramCustomerGroup,
  cartCacheKey,
  onCartItemsReady
}: Omit<UseCreateOrderDataProps, 'processItemsData'>): UseCreateOrderDataReturn => {
  const [items, setItems] = useState<OrderItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [availableSuppliers, setAvailableSuppliers] = useState<string[]>([]);
  const [supplierCounts, setSupplierCounts] = useState<Record<string, number>>({});
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [brandCounts, setBrandCounts] = useState<Record<string, number>>({});
  const [availableItemGroups, setAvailableItemGroups] = useState<string[]>([]);
  const [itemGroupCounts, setItemGroupCounts] = useState<Record<string, number>>({});
  const [customerTerritory, setCustomerTerritory] = useState<string>(initialCustomerTerritory || '');
  const [customerGroup, setCustomerGroup] = useState<string>(initialCustomerGroup || '');
  const [isFilterTransition, setIsFilterTransition] = useState(false);
  const [allDataReady, setAllDataReady] = useState(false);

  // Simple fetch customer details
  const fetchCustomerDetails = useCallback(async () => {
    if (!customerId) return null;
    try {
      const customers = await fetchCustomers();
      return customers.find(c => c.name === customerId) || null;
    } catch (error) {
      return null;
    }
  }, [customerId]);

  // Ultra-fast fetch items function with parallel operations
  const fetchItemsList = useCallback(async () => {
    // Use a ref to check current items length to avoid dependency issues
    if (items.length > 0) return;

    try {
      setLoading(true);
      let territory = customerTerritory || paramTerritory;
      let group = customerGroup || paramCustomerGroup;

      // Enhanced parallel operations for maximum speed
      const promises: Promise<any>[] = [];

      // Get customer details if needed (parallel with other operations)
      if (!territory || !group) {
        promises.push(fetchCustomerDetails());
      }

      // Start items fetch immediately if we have territory and group
      if (territory && group) {
        promises.push(getItemsWithPricesWithCache(territory, group));
      }

      // Also start cart loading in parallel (non-blocking)
      const cartPromise = AsyncStorage.getItem(cartCacheKey).catch(() => null);

      // Wait for all parallel operations
      const results = await Promise.all(promises);

      // Process results
      if (!territory || !group) {
        const customer = results[0];
        if (!customer) {
          throw new Error('Customer not found');
        }
        territory = customer.territory;
        group = customer.customer_group;
        setCustomerTerritory(territory);
        setCustomerGroup(group);

        // If we didn't fetch items yet, fetch them now
        if (results.length === 1) {
          const itemsData = await getItemsWithPricesWithCache(territory, group);
          results.push(itemsData);
        }
      }

      const itemsData = results[results.length - 1];
      if (!itemsData || itemsData.length === 0) {
        throw new Error('No items found');
      }

      // AGGRESSIVE: Process data in chunks to avoid blocking UI
      const processedData = await new Promise<any>((resolve) => {
        // Use setTimeout to yield control back to UI thread
        setTimeout(() => {
          resolve(processItemsData(itemsData));
        }, 0);
      });

      // Get cached cart items (use the parallel cart promise)
      let existingCartMap: Record<string, CartItem> = {};
      try {
        const cachedCartString = await cartPromise;
        if (cachedCartString) {
          const parsedCart = JSON.parse(cachedCartString) as CartItem[];
          existingCartMap = parsedCart.reduce((map, item) => {
            map[item.item_code] = item;
            return map;
          }, {} as Record<string, CartItem>);
        }
      } catch (error) {
        // Silent fail
      }

      // Create cart items
      const cartItemsResult = itemsData.map((item: OrderItem) => {
        const existingCartItem = existingCartMap[item.item_code];
        const defaultUom = item.uoms.find(u => u.uom === item.sales_uom) || item.uoms[0];

        if (existingCartItem) {
          return {
            ...existingCartItem,
            item_name: item.item_name,
            item_group: item.item_group,
          };
        } else {
          return {
            item_code: item.item_code,
            item_name: item.item_name,
            item_group: item.item_group,
            quantity: 0,
            selectedUom: defaultUom.uom,
            conversionFactor: defaultUom.conversion_factor,
            rate: defaultUom.rate, // Include rate from UOM data
          };
        }
      });

      // Update state
      setAvailableSuppliers(processedData.suppliers);
      setSupplierCounts(processedData.supplierCounts);
      setAvailableBrands(processedData.brands);
      setBrandCounts(processedData.brandCounts);
      setAvailableItemGroups(processedData.itemGroups);
      setItemGroupCounts(processedData.itemGroupCounts);
      setItems(itemsData);

      if (onCartItemsReady) {
        onCartItemsReady(cartItemsResult);
      }

      setAllDataReady(true);
      setLoading(false);

    } catch (error) {
      if (__DEV__) console.error('Error fetching items:', error);
      setError(error instanceof Error ? error.message : 'Failed to load items');
      setLoading(false);

      Alert.alert(
        'Error',
        'Failed to load items. Please try again.',
        [
          { text: 'Retry', onPress: () => fetchItemsList() },
          { text: 'Go Back', onPress: () => router.back() }
        ]
      );
    }
  }, [cartCacheKey, customerTerritory, customerGroup, customerId, paramTerritory, paramCustomerGroup, fetchCustomerDetails]); // Removed items.length and onCartItemsReady to prevent infinite loops

  return {
    items,
    loading,
    error,
    availableSuppliers,
    supplierCounts,
    availableBrands,
    brandCounts,
    availableItemGroups,
    itemGroupCounts,
    customerTerritory,
    customerGroup,
    isFilterTransition,
    allDataReady,
    fetchItemsList,
    fetchCustomerDetails,
    setItems,
    setIsFilterTransition
  };
};
