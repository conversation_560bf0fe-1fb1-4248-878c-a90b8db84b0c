/**
 * Validation Utilities
 * 
 * Consolidated validation functions extracted from various utility files.
 * Provides common validation patterns used throughout the application.
 */

// ===== String Validation =====

/**
 * Check if string is not empty or whitespace only
 */
export const isNotEmpty = (value: string): boolean => {
  return typeof value === 'string' && value.trim().length > 0;
};

/**
 * Validate string length
 */
export const isValidLength = (value: string, min: number, max?: number): boolean => {
  if (typeof value !== 'string') return false;
  
  const length = value.trim().length;
  if (length < min) return false;
  if (max !== undefined && length > max) return false;
  
  return true;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number (Uganda format)
 */
export const isValidPhoneNumber = (phone: string): boolean => {
  if (!phone) return false;
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check for valid Uganda phone number formats
  return (
    (cleaned.length === 10 && cleaned.startsWith('0')) || // 0701234567
    (cleaned.length === 12 && cleaned.startsWith('256'))  // 256701234567
  );
};

// ===== Number Validation =====

/**
 * Check if value is a valid positive number
 */
export const isPositiveNumber = (value: any): boolean => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  return typeof num === 'number' && !isNaN(num) && num > 0;
};

/**
 * Check if value is a valid non-negative number
 */
export const isNonNegativeNumber = (value: any): boolean => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  return typeof num === 'number' && !isNaN(num) && num >= 0;
};

/**
 * Validate number within range
 */
export const isNumberInRange = (value: any, min: number, max: number): boolean => {
  const num = typeof value === 'string' ? parseFloat(value) : value;
  return typeof num === 'number' && !isNaN(num) && num >= min && num <= max;
};

/**
 * Check if value is a valid integer
 */
export const isValidInteger = (value: any): boolean => {
  const num = typeof value === 'string' ? parseInt(value, 10) : value;
  return typeof num === 'number' && !isNaN(num) && Number.isInteger(num);
};

// ===== Date Validation =====

/**
 * Validate date string format
 */
export const isValidDateString = (dateString: string): boolean => {
  if (typeof dateString !== 'string') return false;
  
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * Check if date is in the future
 */
export const isFutureDate = (dateString: string): boolean => {
  if (!isValidDateString(dateString)) return false;
  
  const date = new Date(dateString);
  const now = new Date();
  return date > now;
};

/**
 * Check if date is in the past
 */
export const isPastDate = (dateString: string): boolean => {
  if (!isValidDateString(dateString)) return false;
  
  const date = new Date(dateString);
  const now = new Date();
  return date < now;
};

/**
 * Validate date range
 */
export const isDateInRange = (dateString: string, startDate: string, endDate: string): boolean => {
  if (!isValidDateString(dateString) || !isValidDateString(startDate) || !isValidDateString(endDate)) {
    return false;
  }
  
  const date = new Date(dateString);
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  return date >= start && date <= end;
};

// ===== Array Validation =====

/**
 * Check if array is not empty
 */
export const isNonEmptyArray = (arr: any): boolean => {
  return Array.isArray(arr) && arr.length > 0;
};

/**
 * Validate array length
 */
export const isValidArrayLength = (arr: any, min: number, max?: number): boolean => {
  if (!Array.isArray(arr)) return false;
  
  if (arr.length < min) return false;
  if (max !== undefined && arr.length > max) return false;
  
  return true;
};

/**
 * Check if all array items are valid
 */
export const areAllItemsValid = <T>(arr: T[], validator: (item: T) => boolean): boolean => {
  if (!Array.isArray(arr)) return false;
  return arr.every(validator);
};

// ===== Object Validation =====

/**
 * Check if object has required properties
 */
export const hasRequiredProperties = (obj: any, requiredProps: string[]): boolean => {
  if (!obj || typeof obj !== 'object') return false;
  
  return requiredProps.every(prop => prop in obj);
};

/**
 * Check if object is not empty
 */
export const isNonEmptyObject = (obj: any): boolean => {
  return obj && typeof obj === 'object' && Object.keys(obj).length > 0;
};

// ===== Business Logic Validation =====

/**
 * Validate customer ID format
 */
export const isValidCustomerId = (customerId: string): boolean => {
  return isNotEmpty(customerId) && isValidLength(customerId, 1, 50);
};

/**
 * Validate customer name
 */
export const isValidCustomerName = (customerName: string): boolean => {
  return isNotEmpty(customerName) && isValidLength(customerName, 2, 100);
};

/**
 * Validate item code format
 */
export const isValidItemCode = (itemCode: string): boolean => {
  return isNotEmpty(itemCode) && isValidLength(itemCode, 1, 50);
};

/**
 * Validate quantity
 */
export const isValidQuantity = (quantity: any): boolean => {
  return isPositiveNumber(quantity) && isValidInteger(quantity);
};

/**
 * Validate rate/price
 */
export const isValidRate = (rate: any): boolean => {
  return isNonNegativeNumber(rate);
};

/**
 * Validate UOM (Unit of Measure)
 */
export const isValidUOM = (uom: string): boolean => {
  return isNotEmpty(uom) && isValidLength(uom, 1, 20);
};

// ===== Search and Filter Validation =====

/**
 * Validate search query
 */
export const isValidSearchQuery = (query: string): boolean => {
  return typeof query === 'string' && query.length <= 100;
};

/**
 * Validate pagination parameters
 */
export const areValidPaginationParams = (page: number, pageSize: number): boolean => {
  return (
    isValidInteger(page) &&
    isValidInteger(pageSize) &&
    page > 0 &&
    pageSize > 0 &&
    pageSize <= 100
  );
};

// ===== Sanitization Helpers =====

/**
 * Sanitize string input
 */
export const sanitizeString = (input: string, maxLength: number = 255): string => {
  if (typeof input !== 'string') return '';
  return input.trim().substring(0, maxLength);
};

/**
 * Sanitize search query
 */
export const sanitizeSearchQuery = (query: string): string => {
  return sanitizeString(query, 100);
};

/**
 * Sanitize number input
 */
export const sanitizeNumber = (input: any, defaultValue: number = 0): number => {
  const num = typeof input === 'string' ? parseFloat(input) : input;
  return typeof num === 'number' && !isNaN(num) ? num : defaultValue;
};

// ===== Composite Validation =====

/**
 * Validate cart item data
 */
export const isValidCartItem = (item: any): boolean => {
  return (
    hasRequiredProperties(item, ['item_code', 'item_name', 'quantity', 'selectedUom']) &&
    isValidItemCode(item.item_code) &&
    isNotEmpty(item.item_name) &&
    isValidQuantity(item.quantity) &&
    isValidUOM(item.selectedUom)
  );
};

/**
 * Validate customer data
 */
export const isValidCustomerData = (customer: any): boolean => {
  return (
    hasRequiredProperties(customer, ['customer_id', 'customer_name']) &&
    isValidCustomerId(customer.customer_id) &&
    isValidCustomerName(customer.customer_name)
  );
};
