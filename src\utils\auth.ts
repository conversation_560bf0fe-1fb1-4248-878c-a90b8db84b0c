/**
 * Authentication and Security Utilities
 * 
 * Consolidated authentication and security utilities combining functionality from:
 * - appSecurity.ts
 * - loginValidator.ts
 * - secureStorage.ts
 */

import * as SecureStore from 'expo-secure-store';
import { STORAGE_KEYS } from '../constants/storage';
import { LoginFormData, LoginValidationResult, LoginFormErrors } from '../types/auth';

// ===== Secure Storage =====

// List of keys that contain sensitive information
const SENSITIVE_KEYS = [
  STORAGE_KEYS.SID,
  STORAGE_KEYS.FIRST_NAME,
  STORAGE_KEYS.LAST_EMAIL
];

/**
 * Store data securely with encryption for sensitive keys
 */
export const setSecureItem = async (key: string, value: string): Promise<void> => {
  try {
    if (SENSITIVE_KEYS.includes(key)) {
      // Use secure storage for sensitive data
      await SecureStore.setItemAsync(key, value);
    } else {
      // Use regular storage for non-sensitive data
      await SecureStore.setItemAsync(key, value);
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Secure storage error:', error);
    }
    throw error;
  }
};

/**
 * Retrieve data securely with decryption for sensitive keys
 */
export const getSecureItem = async (key: string): Promise<string | null> => {
  try {
    return await SecureStore.getItemAsync(key);
  } catch (error) {
    if (__DEV__) {
      console.warn('Secure storage retrieval error:', error);
    }
    return null;
  }
};

/**
 * Remove item from secure storage
 */
export const removeSecureItem = async (key: string): Promise<void> => {
  try {
    await SecureStore.deleteItemAsync(key);
  } catch (error) {
    if (__DEV__) {
      console.warn('Secure storage removal error:', error);
    }
  }
};

/**
 * Check if secure storage is available
 */
export const isSecureStorageAvailable = async (): Promise<boolean> => {
  try {
    await SecureStore.isAvailableAsync();
    return true;
  } catch {
    return false;
  }
};

/**
 * Initialize device binding for security
 */
export const initializeDeviceBinding = async (): Promise<void> => {
  try {
    // Simple device binding check
    const deviceId = await getSecureItem('device_id');
    if (!deviceId) {
      // Generate a simple device identifier
      const newDeviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      await setSecureItem('device_id', newDeviceId);
    }
  } catch (error) {
    if (__DEV__) {
      console.warn('Device binding initialization failed:', error);
    }
  }
};

// ===== Application Security =====

/**
 * Initialize application security features
 * 
 * Sets up minimal client-side security features
 * without duplicating what's already handled by Frappe backend
 */
export const initializeAppSecurity = async (): Promise<void> => {
  return initializeDeviceBinding()
    .catch(() => {
      // Silently handle security initialization errors (non-critical)
    });
};

// ===== Login Validation =====

/**
 * Validate login form inputs
 */
export const validateLoginForm = (data: LoginFormData): LoginValidationResult => {
  const errors: Partial<LoginFormErrors> = {};
  let isValid = true;

  // Validate email
  if (!data.email.trim()) {
    errors.email = 'Email or username is required';
    isValid = false;
  }

  // Validate password
  if (!data.password) {
    errors.password = 'Password is required';
    isValid = false;
  }

  return {
    isValid,
    errors
  };
};

/**
 * Check if login response indicates success
 */
export const isLoginResponseValid = (response: any): boolean => {
  return response &&
    (response.message === 'Logged In' ||
     response.message === 'No App' ||
     (typeof response.message === 'string' &&
      response.message.toLowerCase().includes('logged in')));
};

/**
 * Extract first name from full name
 */
export const extractFirstName = (fullName: string): string => {
  return (fullName || '').split(' ')[0];
};

// ===== Email Validation =====

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate email or username (more lenient)
 */
export const isValidEmailOrUsername = (input: string): boolean => {
  if (!input || input.trim().length === 0) {
    return false;
  }
  
  // Allow usernames (no @ symbol) or valid emails
  return !input.includes('@') || isValidEmail(input);
};

// ===== Password Validation =====

/**
 * Validate password strength
 */
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// ===== Session Management =====

/**
 * Store session data securely
 */
export const storeSessionData = async (sessionId: string, userData?: any): Promise<void> => {
  await setSecureItem(STORAGE_KEYS.SID, sessionId);
  
  if (userData) {
    if (userData.first_name) {
      await setSecureItem(STORAGE_KEYS.FIRST_NAME, userData.first_name);
    }
    if (userData.email) {
      await setSecureItem(STORAGE_KEYS.LAST_EMAIL, userData.email);
    }
  }
};

/**
 * Retrieve session data
 */
export const getSessionData = async (): Promise<{
  sessionId: string | null;
  firstName: string | null;
  email: string | null;
}> => {
  const [sessionId, firstName, email] = await Promise.all([
    getSecureItem(STORAGE_KEYS.SID),
    getSecureItem(STORAGE_KEYS.FIRST_NAME),
    getSecureItem(STORAGE_KEYS.LAST_EMAIL)
  ]);
  
  return {
    sessionId,
    firstName,
    email
  };
};

/**
 * Clear session data
 */
export const clearSessionData = async (keepEmail: boolean = true): Promise<void> => {
  await removeSecureItem(STORAGE_KEYS.SID);
  await removeSecureItem(STORAGE_KEYS.FIRST_NAME);
  
  if (!keepEmail) {
    await removeSecureItem(STORAGE_KEYS.LAST_EMAIL);
  }
};

/**
 * Check if user has valid session
 */
export const hasValidSession = async (): Promise<boolean> => {
  const sessionId = await getSecureItem(STORAGE_KEYS.SID);
  return !!sessionId && sessionId.trim().length > 0;
};
