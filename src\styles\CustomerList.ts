import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const customerListStyles = StyleSheet.create({
  container: {
    flex: 1,
    height: '100%',
    backgroundColor: NEUTRAL.WHITE,
  },
  listContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 24, // TODO: Replace 24 with the correct value if needed. Extra padding to ensure last item is accessible above bottom navigation
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: MD.SPACING.XLARGE,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: MD.SPACING.MEDIUM,
  },
});
