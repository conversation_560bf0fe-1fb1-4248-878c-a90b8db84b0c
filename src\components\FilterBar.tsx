import React, { memo, useMemo, useCallback } from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { Chip, useTheme } from 'react-native-paper';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';
import { filterBarStyles as styles } from '../styles/FilterBar';
import { generateAccessibilityLabel, getAccessibilityHint } from '../utils/ui';

export interface FilterOption {
  id: string;
  label: string;
  icon?: string;
  type: 'sort' | 'filter';
}

interface FilterBarProps {
  filters: FilterOption[];
  onRemoveFilter: (filterId: string) => void;
}


const EnhancedChip = memo(({
  label,
  onClose,
  style,
  textStyle,
  backgroundColor,
  borderColor,
  textColor
}: {
  label: string;
  onClose: () => void;
  style?: any;
  textStyle?: any;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
}) => {

  return (
    <View style={styles.relativeContainer}>
      <Chip
        mode="flat"
        style={[
          style,
          {
            backgroundColor,
            borderColor
          }
        ]}
        textStyle={[
          textStyle,
          { color: textColor }
        ]}
        closeIconAccessibilityLabel="Remove filter"
        closeIcon="close"
        onClose={onClose}
      >
        {label}
      </Chip>

      <TouchableOpacity
        onPress={onClose}
        style={styles.enhancedCloseButton}
        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={`Remove ${label} filter`}
        accessibilityHint={getAccessibilityHint(`remove the ${label} filter`)}
      >
        <View style={styles.closeButtonSpacer} />
      </TouchableOpacity>
    </View>
  );
});

const FilterBar: React.FC<FilterBarProps> = ({
  filters,
  onRemoveFilter
}) => {
  const theme = useTheme();

  const handleRemoveFilter = useCallback((id: string) => {
    // Remove filter without platform-specific animation
    onRemoveFilter(id);
  }, [onRemoveFilter]);


  const filterChips = useMemo(() => {
    if (filters.length === 0) {
      return [];
    }

    const chipStyles = {
      backgroundColor: `${BRAND.PRIMARY}15`,
      color: BRAND.PRIMARY,
      borderColor: `${BRAND.PRIMARY}30`
    };

    return filters.map((filter) => {
      const styleSet = chipStyles;

      return (
        <View key={filter.id} style={styles.chipWrapper}>
          <EnhancedChip
            label={filter.label}
            onClose={() => handleRemoveFilter(filter.id)}
            style={styles.chip}
            textStyle={styles.chipText}
            backgroundColor={styleSet.backgroundColor}
            borderColor={styleSet.borderColor}
            textColor={styleSet.color}
          />
        </View>
      );
    });
  }, [filters, handleRemoveFilter, theme.colors.primary]);

  if (filters.length === 0) {
    return null;
  }

  // Generate accessibility label for the filter bar
  const filterBarAccessibilityLabel = generateAccessibilityLabel(
    'Active filters',
    [`${filters.length} filter${filters.length === 1 ? '' : 's'} applied`]
  );

  return (
    <View
      style={styles.container}
      accessible={true}
      accessibilityRole="list"
      accessibilityLabel={filterBarAccessibilityLabel}
      accessibilityHint="Swipe left and right to browse active filters. Double tap on any filter to remove it."
    >
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.chipsContainer}
        removeClippedSubviews={true}
        accessible={false} // Let individual chips handle accessibility
      >
        {filterChips}
      </ScrollView>
    </View>
  );
};


export default memo(FilterBar);
