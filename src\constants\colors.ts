// Main brand colors
export const BRAND = {
  PRIMARY: '#a81d3f',
  PRIMARY_10: '#fef7f8',
  PRIMARY_50: '#fce4ea',
  PRIMARY_100: '#f8bccb',
  PRIMARY_200: '#f08fa9',
  PRIMARY_300: '#e76287',
  PRIMARY_400: '#e0406e',
  PRIMARY_500: '#a81d3f',
  PRIMARY_600: '#9c1a39',
  PRIMARY_700: '#8d1632',
  PRIMARY_800: '#7f122b',
  PRIMARY_900: '#640a1f',
  SECONDARY: '#03DAC6',
  ACCENT: '#FF4081',
};

// Neutral colors
export const NEUTRAL = {
  WHITE: '#FFFFFF',
  BLACK: '#000000',
  BACKGROUND: '#F5F5F5',
  BACKGROUND_LIGHT: '#F9F9F9',
  SURFACE: '#FFFFFF',
  TEXT_PRIMARY: '#212121',
  TEXT_SECONDARY: '#757575',
  TEXT_DISABLED: '#9E9E9E',
  DIVIDER: '#E0E0E0',
};

// Semantic colors
export const SEMANTIC = {
  SUCCESS: '#4CAF50',
  ERROR: '#B00020',
  WARNING: '#FFC107',
  INFO: '#2196F3',
};

// Status colors
export const STATUS = {
  DRAFT: '#9E9E9E',
  ON_HOLD: '#795548',
  TO_DELIVER_AND_BILL: '#FF9800',
  TO_BILL: '#2196F3',
  TO_DELIVER: '#9C27B0',
  COMPLETED: '#4CAF50',
  CANCELLED: '#F44336',
  CLOSED: '#607D8B',
  PARTLY_DELIVERED: '#00BCD4',
  PARTLY_BILLED: '#FFEB3B',
  OVERDUE: '#E91E63',
  RETURN: '#FF5722',
  CREDIT_NOTE_ISSUED: '#673AB7',
  SUBMITTED: '#03A9F4',
  PAID: '#8BC34A',
  PARTLY_PAID: '#CDDC39',
  UNPAID: '#FFC107',
  UNPAID_AND_DISCOUNTED: '#FF9800',
  PARTLY_PAID_AND_DISCOUNTED: '#FFB74D',
  OVERDUE_AND_DISCOUNTED: '#F57C00',
  INTERNAL_TRANSFER: '#607D8B',
};

// Helper function to add opacity to any color
export const withOpacity = (color: string, opacity: number): string => {
  const validOpacity = Math.max(0, Math.min(1, opacity));
  const hexOpacity = Math.round(validOpacity * 255).toString(16).padStart(2, '0');
  return `${color}${hexOpacity}`;
};
