import React, { useState, useEffect } from 'react';
import { View, ScrollView, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useLocalSearchParams } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { checkoutScreenStyles } from '../src/styles/CheckoutScreen';
import { useCheckoutCart } from '../src/hooks/useCheckoutCart';
import { useCheckoutOrder } from '../src/hooks/useCheckoutOrder';
import { useCheckoutNavigation } from '../src/hooks/useCheckoutNavigation';
import {
  extractCustomerInfo,
  generateCartCacheKey,
  createNavigationOptions
} from '../src/orchestrators/cart';
import { CartItem } from '../src/types/business';
import CheckoutCustomerInfo from '../src/components/CheckoutCustomerInfo';
import CheckoutItemsList from '../src/components/CheckoutItemsList';
import CheckoutSuccessState from '../src/components/CheckoutSuccessState';
import { formatCurrency } from '../src/utils/formatting';
import { NEUTRAL } from '../src/constants/colors';
import { ICON_SIZES } from '../src/constants/ui';

function CheckoutScreen() {
  const params = useLocalSearchParams();
  const insets = useSafeAreaInsets();

  const customerId = params.customerId as string;
  const customerName = params.customerName as string;

  // Load cart items from AsyncStorage instead of params for consistency
  const [initialCartItems, setInitialCartItems] = useState<CartItem[]>([]);
  const [cartLoaded, setCartLoaded] = useState(false);

  const customerInfo = extractCustomerInfo(params);
  const cartCacheKey = generateCartCacheKey(customerId);

  // Load cart from AsyncStorage on mount
  useEffect(() => {
    const loadCartFromStorage = async () => {
      try {
        const cachedCartString = await AsyncStorage.getItem(cartCacheKey);
        if (cachedCartString) {
          const parsedCart = JSON.parse(cachedCartString) as CartItem[];
          setInitialCartItems(parsedCart);
        }
      } catch (error) {
        if (__DEV__) console.warn('Error loading cart for checkout:', error);
      } finally {
        setCartLoaded(true);
      }
    };

    loadCartFromStorage();
  }, [cartCacheKey]);

  const navigationOptions = createNavigationOptions(params, customerId, customerName, initialCartItems);

  // Always call hooks - never conditionally
  const { cartItems, updateQuantity, clearCart, totalAmount, cartEmpty } = useCheckoutCart(
    initialCartItems,
    customerId,
    customerName,
    navigationOptions
  );

  const { loading, confirmPlaceOrder, orderPlaced, placedOrderName } = useCheckoutOrder(
    cartItems,
    customerId,
    customerName,
    cartCacheKey,
    customerInfo.default_price_list,
    clearCart
  );

  const { handleBackPress, changeUom } = useCheckoutNavigation(
    customerId,
    customerName,
    navigationOptions
  );

  // Conditional rendering AFTER all hooks are called
  if (!cartLoaded) {
    return (
      <View style={[checkoutScreenStyles.container, { paddingTop: insets.top, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" />
        <Text style={{ marginTop: 10 }}>Loading cart...</Text>
      </View>
    );
  }

  // Show success state when order is placed (while alert is showing)
  if (orderPlaced) {
    return (
      <View style={[checkoutScreenStyles.container, { paddingTop: insets.top }]}>
        <View style={checkoutScreenStyles.header}>
          <View style={checkoutScreenStyles.headerRow}>
            <TouchableOpacity onPress={handleBackPress} style={checkoutScreenStyles.backButton}>
              <MaterialCommunityIcons name="arrow-left" size={ICON_SIZES.MEDIUM} color={NEUTRAL.TEXT_PRIMARY} />
            </TouchableOpacity>
            <Text style={checkoutScreenStyles.headerTitle}>
              Order Placed
            </Text>
          </View>
        </View>
        <CheckoutSuccessState
          customerName={customerName}
          orderName={placedOrderName}
        />
      </View>
    );
  }

  return (
    <View style={[checkoutScreenStyles.container, { paddingTop: insets.top }]}>
      <View style={checkoutScreenStyles.header}>
        <View style={checkoutScreenStyles.headerRow}>
          <TouchableOpacity onPress={handleBackPress} style={checkoutScreenStyles.backButton}>
            <MaterialCommunityIcons name="arrow-left" size={ICON_SIZES.MEDIUM} color={NEUTRAL.TEXT_PRIMARY} />
          </TouchableOpacity>
          <Text style={checkoutScreenStyles.headerTitle}>
            Checkout
          </Text>
        </View>
      </View>

      <ScrollView
        style={checkoutScreenStyles.content}
        contentContainerStyle={checkoutScreenStyles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        <CheckoutCustomerInfo
          customerName={customerName}
          customerInfo={customerInfo}
        />

        <CheckoutItemsList
          items={cartItems}
          updateQuantity={updateQuantity}
          changeUom={changeUom}
        />
      </ScrollView>
      <View style={[
        checkoutScreenStyles.footer,
        { paddingBottom: Math.max(insets.bottom, 16) }
      ]}>
        <TouchableOpacity
          style={[
            checkoutScreenStyles.placeOrderButton,
            cartEmpty && checkoutScreenStyles.disabledButton
          ]}
          onPress={confirmPlaceOrder}
          disabled={cartEmpty || loading}
        >
          <Text style={checkoutScreenStyles.buttonText}>
            {loading ? 'Placing Order...' : 'Place Order'}
          </Text>
          <Text style={checkoutScreenStyles.buttonAmount}>
            {formatCurrency(totalAmount)}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CheckoutScreen;
