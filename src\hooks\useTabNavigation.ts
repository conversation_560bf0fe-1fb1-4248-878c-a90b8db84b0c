import { useEffect, useCallback, useRef } from 'react';
import { BackHandler, Alert } from 'react-native';
import { router, usePathname } from 'expo-router';
import { ROUTES } from '../constants/routes';

/**
 * Tab navigation hook for handling back button behavior
 *
 * Behavior:
 * 1. If user is on home tab and presses back -> Show exit confirmation
 * 2. If user is on any other tab and presses back -> Navigate to home
 * 3. Only active when on tab screens (not auth screens)
 */
export const useTabNavigation = () => {
  const pathname = usePathname();
  const lastBackPressRef = useRef<number>(0);

  // Determine if this is home tab based on current pathname
  const isHomeTab = pathname === ROUTES.HOME;

  // Check if we're on a tab screen (not auth or other screens)
  const isOnTabScreen = pathname.startsWith('/(tabs)/');

  const handleBackPress = useCallback(() => {
    // Only handle back press if we're on a tab screen
    // This automatically excludes auth screens without expensive SID checks
    if (!isOnTabScreen) {
      return false; // Let default back behavior handle it
    }

    const currentTime = Date.now();

    if (isHomeTab) {
      // If on home tab, check for double-tap to exit
      if (currentTime - lastBackPressRef.current < 2000) {
        // Double tap within 2 seconds - exit app
        BackHandler.exitApp();
        return true;
      } else {
        // First tap on home - show alert with exit option
        lastBackPressRef.current = currentTime;
        Alert.alert(
          'Exit App',
          'Do you want to exit the app?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Exit',
              style: 'destructive',
              onPress: () => BackHandler.exitApp()
            }
          ],
          { cancelable: true }
        );
        return true;
      }
    } else {
      // If on any other tab, navigate to home
      router.push(ROUTES.HOME_FULL);
      return true;
    }
  }, [isHomeTab, isOnTabScreen]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      backHandler.remove();
    };
  }, [handleBackPress]);


};
