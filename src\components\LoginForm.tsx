
import React, { useRef, memo } from 'react';
import { View, TouchableOpacity, Image } from 'react-native';
import { TextInput, Button, Card, HelperText, useTheme, Text as PaperText } from 'react-native-paper';
import { MaterialCommunityIcons } from '@expo/vector-icons';

import { LoginFormState } from '../types/auth';
import { APP_INFO } from '../constants/config';
import { getHostname } from '../constants/api';
import { loginFormStyles } from '../styles/LoginForm';

interface LoginFormProps {
  formState: LoginFormState;
  onEmailChange: (email: string) => void;
  onPasswordChange: (password: string) => void;
  onToggleSecureEntry: () => void;
  onLoginPress: () => void;
  onEmailSubmit?: () => void;
  onPasswordSubmit?: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = memo(({
  formState,
  onEmailChange,
  onPasswordChange,
  onToggleSecureEntry,
  onLoginPress,
  onEmailSubmit,
  onPasswordSubmit
}) => {
  const theme = useTheme();
  const passwordInputRef = useRef<any>(null);

  const handleEmailSubmit = () => {
    passwordInputRef.current?.focus();
    onEmailSubmit?.();
  };

  const handlePasswordSubmit = () => {
    onPasswordSubmit?.();
  };

  return (
    <Card style={loginFormStyles.card}>
      <Card.Content>
        <View style={loginFormStyles.logoContainer}>
          <Image
            source={require('../../assets/images/icon.png')}
            style={loginFormStyles.logo}
            resizeMode="contain"
          />
          <PaperText style={[loginFormStyles.versionText, { color: theme.colors.outline, marginTop: 8 }]}>
            {getHostname()} ({APP_INFO.VERSION})
          </PaperText>
        </View>

        <TextInput
          label="Email or Username"
          value={formState.data.email}
          onChangeText={onEmailChange}
          style={loginFormStyles.input}
          mode="outlined"
          disabled={formState.loading}
          autoCapitalize="none"
          keyboardType="email-address"
          returnKeyType="next"
          onSubmitEditing={handleEmailSubmit}
          error={!!formState.errors.email}
          left={<TextInput.Icon icon="account" />}
          accessibilityLabel="Email or username input"
        />
        {formState.errors.email ? (
          <HelperText type="error">{formState.errors.email}</HelperText>
        ) : null}

        <View style={loginFormStyles.inputContainer}>
          <TextInput
            ref={passwordInputRef}
            label="Password"
            value={formState.data.password}
            onChangeText={onPasswordChange}
            secureTextEntry={formState.secureTextEntry}
            style={loginFormStyles.input}
            mode="outlined"
            disabled={formState.loading}
            returnKeyType="done"
            onSubmitEditing={handlePasswordSubmit}
            error={!!formState.errors.password}
            left={<TextInput.Icon icon="lock" />}
            accessibilityLabel="Password input"
          />
          <TouchableOpacity
            onPress={onToggleSecureEntry}
            style={loginFormStyles.inputIcon}
            activeOpacity={0.7}
            accessibilityLabel={formState.secureTextEntry ? "Show password" : "Hide password"}
          >
            <MaterialCommunityIcons
              name={formState.secureTextEntry ? 'eye-off' : 'eye'}
              size={24}
              color={theme.colors.primary}
            />
          </TouchableOpacity>
        </View>
        {formState.errors.password ? (
          <HelperText type="error">{formState.errors.password}</HelperText>
        ) : null}

        {formState.errors.general ? (
          <HelperText type="error" style={loginFormStyles.error}>
            {formState.errors.general}
          </HelperText>
        ) : null}

        <Button
          mode="contained"
          onPress={onLoginPress}
          loading={formState.loading}
          disabled={formState.loading || !formState.data.email.trim() || !formState.data.password}
          style={[
            loginFormStyles.button,
            (formState.loading || !formState.data.email.trim() || !formState.data.password) && {
              backgroundColor: '#CCCCCC'
            }
          ]}
          contentStyle={loginFormStyles.buttonContent}
          labelStyle={loginFormStyles.buttonLabel}
        >
          {formState.loading ? 'Signing in...' : 'Login'}
        </Button>
      </Card.Content>
    </Card>
  );
});

LoginForm.displayName = 'LoginForm';
