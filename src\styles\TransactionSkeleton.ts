import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const transactionSkeletonStyles = StyleSheet.create({
  skeletonListItem: {
    backgroundColor: NEUTRAL.WHITE,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.LARGE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  skeletonCardContent: {
    width: '100%',
  },
  skeletonCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: MD.SPACING.XXSMALL,
  },
  skeletonIdContainer: {
    flex: 1,
  },
  skeletonTransactionId: {
    height: 16, // Match MD.TYPOGRAPHY.BODY1.fontSize
    width: 90,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonDate: {
    height: 12, // Match MD.TYPOGRAPHY.CAPTION.fontSize + 1
    width: 70,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
  skeletonCustomer: {
    height: 14, // Match MD.TYPOGRAPHY.BODY2.fontSize + 1
    width: '75%',
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL,
  },
  skeletonStatusAmountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  skeletonStatusChip: {
    height: 22, // Match exact height from TransactionCard
    width: 80,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.LARGE - 4, // Match exact border radius
  },
  skeletonAmountContainer: {
    alignItems: 'flex-end',
  },
  skeletonAmount: {
    height: 16, // Match MD.TYPOGRAPHY.BODY1.fontSize
    width: 85,
    backgroundColor: MD.SKELETON_BG,
    borderRadius: MD.BORDER_RADIUS.SMALL,
  },
});
