import React, { memo, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  StatusBar,
  Modal,
  ActivityIndicator,
  FlatList,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Button, useTheme } from 'react-native-paper';
import { Transaction } from '../types/business';
import { formatCurrency, formatDate } from '../utils/formatting';
import { isSalesOrder, isSalesInvoice } from '../utils/transactions';
import TransactionItemCard from './TransactionItemCard';
import { useTransactionDetail } from '../hooks/useTransactionDetail';
import { getTransactionTitle, getStatusColorKey, hasOutstandingAmount, getOutstandingAmount } from '../utils/transactions';

import { TRANSACTION } from '../constants/business';
import { BRAND } from '../constants/colors';
import { transactionDetailStyles } from '../styles/TransactionDetail';
import TransactionDetailSkeleton from './TransactionDetailSkeleton';

interface TransactionDetailScreenProps {
  // Common props
  transactionId: string;
  transactionType: 'order' | 'invoice';

  // Modal specific props (optional)
  isModal?: boolean;
  visible?: boolean;
  onClose?: () => void;

  // Standalone screen specific props (optional)
  onBack?: () => void;
}

const TransactionDetailScreenBase: React.FC<TransactionDetailScreenProps> = ({
  transactionId,
  transactionType,
  isModal = false,
  visible = true,
  onClose,
  onBack,
}) => {
  const insets = useSafeAreaInsets();
  const theme = useTheme();

  // Use the transaction detail hook
  const {
    transaction,
    loading,
    refreshing,
    error,
    isReordering,
    isOrder,
    isInvoice,
    canReorder,
    hasItems,
    handleRefresh,
    handleClose,
    showReorderConfirmation,
  } = useTransactionDetail({
    transactionId,
    transactionType,
    onClose,
    onBack,
  });

  // Optimized render function for items
  const renderTransactionItem = React.useCallback(({ item, index }: { item: any; index: number }) => {
    if (!item) return null;

    return (
      <TransactionItemCard
        key={item.name || `item-${index}`}
        item={item}
        index={index}
      />
    );
  }, []);

  // Memoized key extractor
  const keyExtractor = React.useCallback((item: any, index: number) => {
    return item?.name || `item-${index}`;
  }, []);

  // Memoized status color calculation
  const statusColor = React.useMemo(() => {
    if (!transaction?.status) return TRANSACTION.STATUS_COLORS['Draft'];
    const colorKey = getStatusColorKey(transaction.status);
    if (colorKey && colorKey in TRANSACTION.STATUS_COLORS) {
      return TRANSACTION.STATUS_COLORS[colorKey as keyof typeof TRANSACTION.STATUS_COLORS];
    }
    return TRANSACTION.STATUS_COLORS['Draft'];
  }, [transaction?.status]);

  // Prepare the content based on loading/error state
  const renderContent = React.useCallback(() => {
    // Render loading state
    if (loading && !refreshing) {
      return (
        <View style={[transactionDetailStyles.container, { paddingTop: insets.top }]}>
          <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
          {/* Header - shown immediately */}
          <View style={transactionDetailStyles.header}>
            <View style={transactionDetailStyles.headerLeft}>
              {!isModal && (
                <TouchableOpacity onPress={handleClose}>
                  <MaterialCommunityIcons
                    name="arrow-left"
                    size={24}
                    color="#1A1A1A"
                  />
                </TouchableOpacity>
              )}
              <Text style={transactionDetailStyles.headerTitle}>
                {transaction ? `${getTransactionTitle(transactionType)} #${transaction.name}` : getTransactionTitle(transactionType)}
              </Text>
            </View>
            {isModal && (
              <TouchableOpacity onPress={handleClose}>
                <MaterialCommunityIcons
                  name="close"
                  size={28}
                  color="#1A1A1A"
                />
              </TouchableOpacity>
            )}
          </View>

          <TransactionDetailSkeleton />
        </View>
      );
    }

    // Render error state
    if (error) {
      return (
        <View style={[transactionDetailStyles.container, transactionDetailStyles.errorContainer, { paddingTop: insets.top }]}>
          <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
          <MaterialCommunityIcons name="alert-circle-outline" size={64} color="#E53E3E" />
          <Text style={transactionDetailStyles.errorText}>{error}</Text>
          <Button
            mode="contained"
            onPress={handleRefresh}
            style={transactionDetailStyles.retryButton}
          >
            Retry
          </Button>
          <Button
            mode="outlined"
            onPress={handleClose}
            style={[transactionDetailStyles.actionButton, { marginTop: 16 }]}
          >
            Go Back
          </Button>
        </View>
      );
    }

    // Render not found state
    if (!transaction) {
      return (
        <View style={[transactionDetailStyles.container, transactionDetailStyles.errorContainer, { paddingTop: insets.top }]}>
          <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
          <MaterialCommunityIcons name="file-search-outline" size={64} color="#6B7280" />
          <Text style={transactionDetailStyles.errorText}>{getTransactionTitle(transactionType)} not found</Text>
          <Button
            mode="outlined"
            onPress={handleClose}
            style={transactionDetailStyles.actionButton}
          >
            Go Back
          </Button>
        </View>
      );
    }

    // Status color is already memoized above

    // Render main content
    return (
      <View style={[transactionDetailStyles.container, { paddingTop: insets.top }]}>
        <StatusBar backgroundColor="#FFFFFF" barStyle="dark-content" />
        {/* Header */}
        <View style={transactionDetailStyles.header}>
          <View style={transactionDetailStyles.headerLeft}>
            {!isModal && (
              <TouchableOpacity onPress={handleClose}>
                <MaterialCommunityIcons
                  name="arrow-left"
                  size={24}
                  color="#1A1A1A"
                />
              </TouchableOpacity>
            )}
            <Text style={transactionDetailStyles.headerTitle}>{getTransactionTitle(transactionType)} #{transaction.name}</Text>
          </View>
          {isModal && (
            <TouchableOpacity onPress={handleClose}>
              <MaterialCommunityIcons
                name="close"
                size={28}
                color="#1A1A1A"
              />
            </TouchableOpacity>
          )}
        </View>

        <ScrollView
          style={transactionDetailStyles.scrollView}
          contentContainerStyle={transactionDetailStyles.scrollViewContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={[theme.colors.primary]}
              progressBackgroundColor="#ffffff"
              tintColor={theme.colors.primary}
            />
          }
        >
          {/* Transaction Header - Flat Design */}
          <View style={transactionDetailStyles.section}>
            <View style={transactionDetailStyles.cardHeader}>
              <View style={[transactionDetailStyles.statusChip, { backgroundColor: `${statusColor}20` }]}>
                <View style={[transactionDetailStyles.statusIndicator, { backgroundColor: statusColor }]} />
                <Text style={[transactionDetailStyles.statusText, { color: statusColor }]}>{transaction.status}</Text>
              </View>

              {/* Reorder Button - show for both orders and invoices */}
              {canReorder && (
                <TouchableOpacity
                  onPress={showReorderConfirmation}
                  style={[transactionDetailStyles.actionButton, isReordering && { opacity: 0.7 }]}
                  disabled={isReordering}
                  activeOpacity={0.8}
                >
                  {isReordering ? (
                    <ActivityIndicator size="small" color={BRAND.PRIMARY} />
                  ) : (
                    <MaterialCommunityIcons
                      name="refresh"
                      size={12}
                      color={BRAND.PRIMARY}
                    />
                  )}
                  <Text style={transactionDetailStyles.actionButtonText}>
                    {isReordering ? 'Reordering...' : 'Reorder'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>

              {/* Customer Information */}
              <View style={transactionDetailStyles.detailRow}>
                <Text style={transactionDetailStyles.detailLabel}>Customer</Text>
                <Text style={transactionDetailStyles.detailValue} numberOfLines={2}>{transaction.customer_name}</Text>
              </View>

              {/* Date Information */}
              <View style={transactionDetailStyles.detailRow}>
                <Text style={transactionDetailStyles.detailLabel}>Date</Text>
                <Text style={transactionDetailStyles.detailValue}>
                  {formatDate(
                    isSalesOrder(transaction)
                      ? transaction.transaction_date
                      : isSalesInvoice(transaction)
                        ? transaction.posting_date
                        : ''
                  )}
                </Text>
              </View>

              {/* Tax ID */}
              {transaction.tax_id && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Tax ID</Text>
                  <Text style={transactionDetailStyles.detailValue}>{transaction.tax_id}</Text>
                </View>
              )}

              {/* Payment Terms Template - for both orders and invoices */}
              {transaction.payment_terms_template && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Payment Terms</Text>
                  <Text style={transactionDetailStyles.detailValue}>{transaction.payment_terms_template}</Text>
                </View>
              )}

              {/* PO Number for orders */}
              {isSalesOrder(transaction) && transaction.po_no && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>PO Number</Text>
                  <Text style={transactionDetailStyles.detailValue}>{transaction.po_no}</Text>
                </View>
              )}

              {/* Due Date for invoices */}
              {isSalesInvoice(transaction) && transaction.due_date && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Due Date</Text>
                  <Text style={transactionDetailStyles.detailValue}>{formatDate(transaction.due_date)}</Text>
                </View>
              )}

              {/* Fiscal Document Number for invoices */}
              {isSalesInvoice(transaction) && transaction.fiscal_document_number && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>FDN</Text>
                  <Text style={transactionDetailStyles.detailValue}>{transaction.fiscal_document_number}</Text>
                </View>
              )}

              {/* Outstanding Amount for invoices */}
              {hasOutstandingAmount(transaction) && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Outstanding</Text>
                  <Text style={transactionDetailStyles.detailValue}>{formatCurrency(getOutstandingAmount(transaction))}</Text>
                </View>
              )}
          </View>

          {/* Summary - Flat Design */}
          <View style={transactionDetailStyles.section}>
              <View style={transactionDetailStyles.cardHeader}>
                <Text style={transactionDetailStyles.cardTitle}>Summary</Text>
              </View>

              <View style={transactionDetailStyles.detailRow}>
                <Text style={transactionDetailStyles.detailLabel}>Total</Text>
                <Text style={transactionDetailStyles.detailValue}>
                  {formatCurrency(transaction.base_total || 0)}
                </Text>
              </View>

              <View style={transactionDetailStyles.detailRow}>
                <Text style={transactionDetailStyles.detailLabel}>Taxes & Charges</Text>
                <Text style={transactionDetailStyles.detailValue}>
                  {formatCurrency(transaction.base_total_taxes_and_charges || 0)}
                </Text>
              </View>

              <View style={transactionDetailStyles.detailRow}>
                <Text style={transactionDetailStyles.detailLabel}>Net Total</Text>
                <Text style={transactionDetailStyles.detailValue}>
                  {formatCurrency(transaction.base_net_total || 0)}
                </Text>
              </View>

              {/* Apply Discount On - only shown if present AND there's a discount */}
              {transaction.apply_discount_on &&
               ((transaction.additional_discount_percentage !== undefined && transaction.additional_discount_percentage > 0) ||
                (transaction.discount_amount !== undefined && transaction.discount_amount > 0)) && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Applied On</Text>
                  <Text style={transactionDetailStyles.detailValue}>
                    {transaction.apply_discount_on}
                  </Text>
                </View>
              )}

              {/* Discount - show percentage and/or amount */}
              {((transaction.additional_discount_percentage !== undefined && transaction.additional_discount_percentage > 0) ||
                (transaction.discount_amount !== undefined && transaction.discount_amount > 0)) && (
                <View style={transactionDetailStyles.detailRow}>
                  <Text style={transactionDetailStyles.detailLabel}>Discount</Text>
                  <Text style={transactionDetailStyles.detailValue}>
                    {(() => {
                      const hasPercentage = transaction.additional_discount_percentage !== undefined && transaction.additional_discount_percentage > 0;
                      const hasAmount = transaction.discount_amount !== undefined && transaction.discount_amount > 0;

                      if (hasPercentage && hasAmount) {
                        return `${transaction.additional_discount_percentage}% (${formatCurrency(transaction.discount_amount)})`;
                      } else if (hasPercentage) {
                        return `${transaction.additional_discount_percentage}%`;
                      } else if (hasAmount) {
                        return formatCurrency(transaction.discount_amount);
                      }
                      return '';
                    })()}
                  </Text>
                </View>
              )}

              <View style={transactionDetailStyles.totalRow}>
                <Text style={transactionDetailStyles.totalLabel}>Grand Total</Text>
                <Text style={transactionDetailStyles.totalValue}>
                  {formatCurrency(transaction.base_grand_total || 0)}
                </Text>
              </View>
          </View>

          {/* Items - Flat Design */}
          <View style={transactionDetailStyles.section}>
            <View style={transactionDetailStyles.itemsHeader}>
              <Text style={transactionDetailStyles.itemsTitle}>Items</Text>
              {hasItems && (
                <Text style={transactionDetailStyles.itemsCount}>
                  {transaction.items?.length || 0} items
                </Text>
              )}
            </View>

            {hasItems ? (
              <FlatList
                data={transaction.items}
                renderItem={renderTransactionItem}
                keyExtractor={keyExtractor}
                scrollEnabled={false}
                style={transactionDetailStyles.itemsContainer}
                removeClippedSubviews={true}
                getItemLayout={(_, index) => ({
                  length: 75, // Optimized height
                  offset: 75 * index,
                  index,
                })}
              />
            ) : (
              <Text style={transactionDetailStyles.noItemsText}>No items found</Text>
            )}
          </View>


        </ScrollView>
      </View>
    );
  }, [loading, refreshing, error, transaction, insets.top, isModal, isOrder, isInvoice, handleClose, handleRefresh, theme.colors.primary, showReorderConfirmation, isReordering, hasItems, canReorder, renderTransactionItem, keyExtractor, statusColor]);

  // Render as modal or standalone screen based on isModal prop
  if (isModal) {
    return (
      <Modal
        visible={visible}
        animationType="slide"
        transparent={false}
        onRequestClose={handleClose}
        statusBarTranslucent={false}
        presentationStyle="pageSheet"
        onShow={() => {
          // Ensure status bar is properly configured when modal shows
          StatusBar.setBarStyle('dark-content', true);
          StatusBar.setBackgroundColor('#FFFFFF', true);
        }}
        onDismiss={() => {
          // Restore status bar when modal is dismissed
          StatusBar.setBarStyle('dark-content', true);
          StatusBar.setBackgroundColor('#FFFFFF', true);
        }}
      >
        {renderContent()}
      </Modal>
    );
  }

  // Render as standalone screen
  return renderContent();
};

// Memoize the component to prevent unnecessary re-renders
const TransactionDetailScreen = memo(TransactionDetailScreenBase, (prevProps, nextProps) => {
  // Only re-render if these specific properties change
  return (
    prevProps.visible === nextProps.visible &&
    prevProps.transactionId === nextProps.transactionId &&
    prevProps.transactionType === nextProps.transactionType &&
    prevProps.isModal === nextProps.isModal
  );
});

export default TransactionDetailScreen;
