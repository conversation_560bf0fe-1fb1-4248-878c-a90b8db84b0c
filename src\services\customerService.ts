import { apiClient } from './apiClient';
import { Customer } from '../types/business';
import { FetchCustomersResponse } from '../types/api';
import { API } from '../constants/api';
import { withErrorHandling } from '../utils/errors';
import { cacheService, CacheKey } from '../utils/cacheService';

const fetchCustomersFromAPI = async (): Promise<Customer[]> => {
  const response = await apiClient.get<FetchCustomersResponse>(API.ENDPOINTS.CUSTOMERS);
  return response?.data?.message || [];
};

export const fetchCustomers = async (forceRefresh = false): Promise<Customer[]> => {
  return withErrorHandling(async () => {
    return await cacheService.fetchWithCache(
      CacheKey.CUSTOMERS,
      fetchCustomersFromAPI,
      forceRefresh
    );
  });
};
