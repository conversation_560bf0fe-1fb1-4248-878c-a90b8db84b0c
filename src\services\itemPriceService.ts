import { apiClient } from './apiClient';
import { API } from '../constants/api';
import { withErrorHandling } from '../utils/errors';
import { ItemPrice } from '../types/business';
import { FetchItemPricesResponse } from '../types/api';

/**
 * Fetch item prices from the API
 *
 * @returns Array of item prices
 * @throws Error if the API call fails
 */
export const fetchItemPrices = async (): Promise<ItemPrice[]> => {
  return withErrorHandling(async () => {
    const response = await apiClient.get<FetchItemPricesResponse>(API.ENDPOINTS.ITEM_PRICES);
    return response?.data?.message || [];
  });
};
