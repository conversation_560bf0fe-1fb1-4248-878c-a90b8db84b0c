import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const checkoutItemsListStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
  },
  listContainer: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: MD.SPACING.SMALL,
  },
  sectionHeader: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  sectionTitle: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
});
