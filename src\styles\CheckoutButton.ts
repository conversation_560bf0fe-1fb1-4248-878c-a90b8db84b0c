import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const checkoutButtonStyles = StyleSheet.create({
  footer: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.MEDIUM,
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    elevation: 4, // Match create-order footer elevation
  },
  checkoutButton: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    height: 48,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: MD.SPACING.LARGE,
    elevation: 2, // Match create-order button elevation
  },
  disabledButton: {
    backgroundColor: BRAND.PRIMARY_100,
    opacity: 0.7,
  },
  checkoutButtonText: {
    fontSize: MD.TYPOGRAPHY.SUBTITLE2.fontSize, // Increased from 14px to 14px but using SUBTITLE2
    fontWeight: '600', // Increased from 500 to 600 for better prominence
    color: NEUTRAL.WHITE,
    letterSpacing: MD.TYPOGRAPHY.SUBTITLE2.letterSpacing,
  },
  checkoutButtonAmount: {
    fontSize: MD.TYPOGRAPHY.H2.fontSize, // Increased from 15px to 16px for better visibility
    color: NEUTRAL.WHITE,
    fontWeight: '600', // Increased from 500 to 600 for consistency
  },
});
