/**
 * UI Utilities
 * 
 * Consolidated UI-related utilities combining functionality from:
 * - TabBarUtils.ts
 * - itemFilters.ts
 * - UI-related functions from other files
 */

import { Dimensions } from 'react-native';
import { CartItem, OrderItem } from '../types/business';

// ===== Tab Bar Utilities =====

/**
 * Tab Bar Utilities
 * 
 * These functions help standardize spacing and positioning related to the bottom tab bar
 */
export const TabBarUtils = {
  /**
   * Calculate the standard tab bar height for Android
   * @param insets Safe area insets from useSafeAreaInsets()
   * @returns The calculated tab bar height
   */
  getTabBarHeight: (insets: { bottom: number }) => {
    return Math.max(70, 60 + insets.bottom);
  },

  /**
   * Get the bottom padding needed to account for the tab bar
   * @param insets Safe area insets from useSafeAreaInsets()
   * @returns The bottom padding value
   */
  getBottomPadding: (insets: { bottom: number }) => {
    return TabBarUtils.getTabBarHeight(insets) + 10; // Extra 10px for spacing
  },

  /**
   * Get the content container style for ScrollView with tab bar
   * @param insets Safe area insets from useSafeAreaInsets()
   * @returns Style object for ScrollView contentContainerStyle
   */
  getScrollViewContentStyle: (insets: { bottom: number }) => ({
    paddingBottom: TabBarUtils.getBottomPadding(insets),
  }),

  /**
   * Get the safe area style for views that need to avoid tab bar
   * @param insets Safe area insets from useSafeAreaInsets()
   * @returns Style object with bottom margin
   */
  getSafeAreaStyle: (insets: { bottom: number }) => ({
    marginBottom: TabBarUtils.getTabBarHeight(insets),
  }),

  /**
   * Get content padding for views that need to account for tab bar
   * @param insets Safe area insets from useSafeAreaInsets()
   * @param additionalPadding Optional additional padding to add
   * @returns The content padding value
   */
  getContentPadding: (insets: { bottom: number }, additionalPadding: number = 0) => {
    return TabBarUtils.getBottomPadding(insets) + additionalPadding;
  },
};

// ===== Screen Dimensions =====

/**
 * Get screen dimensions
 */
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  return { width, height };
};

/**
 * Check if device is in landscape mode
 */
export const isLandscape = (): boolean => {
  const { width, height } = getScreenDimensions();
  return width > height;
};

/**
 * Check if device is a tablet
 */
export const isTablet = (): boolean => {
  const { width, height } = getScreenDimensions();
  const aspectRatio = Math.max(width, height) / Math.min(width, height);
  return aspectRatio < 1.6; // Tablets typically have aspect ratios closer to 4:3
};

/**
 * Get responsive font size based on screen width
 */
export const getResponsiveFontSize = (baseSize: number): number => {
  const { width } = getScreenDimensions();
  const scale = width / 360; // Base width (Android standard)
  return Math.round(baseSize * scale);
};

// ===== Item Filtering =====

/**
 * Filter functions for cart items
 */
export const createFilterFunctions = (items: OrderItem[]) => {

  // Create a lookup map for better performance
  const itemLookup = new Map(items.map(item => [item.item_code, item]));

  return {
    supplier: (item: CartItem, value: string) => {
      const itemDetails = itemLookup.get(item.item_code);
      return !!itemDetails && itemDetails.supplier_short_name === value;
    },
    brand: (item: CartItem, value: string) => {
      const itemDetails = itemLookup.get(item.item_code);
      return !!itemDetails && itemDetails.brand === value;
    },
    itemGroup: (item: CartItem, value: string) => {
      const itemDetails = itemLookup.get(item.item_code);
      return !!itemDetails && itemDetails.item_group === value;
    }
  };
};

// Removed complex filtering functions - now using simplified single-filter approach

/**
 * Filter items by search query
 */
export const filterItemsBySearch = (items: any[], searchQuery: string, searchFields: string[]): any[] => {
  if (!searchQuery.trim()) return items;
  
  const query = searchQuery.toLowerCase().trim();
  
  return items.filter(item => 
    searchFields.some(field => 
      item[field]?.toString().toLowerCase().includes(query)
    )
  );
};

/**
 * Sort items by specified field
 */
export const sortItems = <T>(items: T[], sortField: keyof T, sortOrder: 'asc' | 'desc' = 'asc'): T[] => {
  return [...items].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

// ===== Color Utilities =====

/**
 * Get status color based on status value
 */
export const getStatusColor = (status: string, statusColors: Record<string, string>): string => {
  return statusColors[status] || '#9E9E9E'; // Default to grey if status not found
};

/**
 * Generate a color based on string hash (for consistent colors)
 */
export const getColorFromString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 50%)`;
};

/**
 * Check if color is light or dark
 */
export const isLightColor = (color: string): boolean => {
  // Simple check for hex colors
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    
    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5;
  }
  
  return false; // Default to dark if can't determine
};

// ===== Layout Utilities =====

/**
 * Calculate grid layout for items
 */
export const calculateGridLayout = (
  containerWidth: number,
  itemMinWidth: number,
  spacing: number = 10
): { numColumns: number; itemWidth: number } => {
  const availableWidth = containerWidth - spacing;
  const numColumns = Math.floor(availableWidth / (itemMinWidth + spacing));
  const actualNumColumns = Math.max(1, numColumns);
  const itemWidth = (availableWidth - (actualNumColumns - 1) * spacing) / actualNumColumns;
  
  return {
    numColumns: actualNumColumns,
    itemWidth: Math.floor(itemWidth)
  };
};

/**
 * Get optimal number of columns for grid based on screen size
 */
export const getOptimalColumns = (itemWidth: number = 150): number => {
  const { width } = getScreenDimensions();
  const padding = 20; // Total horizontal padding
  const spacing = 10; // Space between items
  
  const availableWidth = width - padding;
  const columns = Math.floor(availableWidth / (itemWidth + spacing));
  
  return Math.max(1, Math.min(columns, isTablet() ? 4 : 2));
};

// ===== Animation Utilities =====

/**
 * Get standard animation duration for Android
 */
export const getAnimationDuration = (type: 'fast' | 'normal' | 'slow' = 'normal'): number => {
  const durations = {
    fast: 150,
    normal: 250,
    slow: 400,
  };

  return durations[type];
};

/**
 * Get standard easing curve for animations
 */
export const getEasingCurve = (): string => {
  return 'ease-out';
};

// ===== Accessibility Utilities =====

/**
 * Generate accessibility label for screen readers
 */
export const generateAccessibilityLabel = (
  mainText: string,
  additionalInfo?: string[]
): string => {
  let label = mainText;
  
  if (additionalInfo && additionalInfo.length > 0) {
    label += '. ' + additionalInfo.join('. ');
  }
  
  return label;
};

/**
 * Get accessibility hint for interactive elements
 */
export const getAccessibilityHint = (action: string): string => {
  return `Double tap to ${action}`;
};


