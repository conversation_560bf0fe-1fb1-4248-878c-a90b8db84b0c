import { StyleSheet } from 'react-native';
import { NEUTRAL } from '../constants/colors';
import MD from '../constants/design';

export const floatingActionMenuStyles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  menuContainer: {
    position: 'absolute',
    width: 240,
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    paddingVertical: MD.SPACING.SMALL,
    elevation: 2,
  },
  menuItemsContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    width: '100%',
  },
  menuItem: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: MD.SPACING.MEDIUM,
    flex: 1,
    minHeight: 60,
  },
  disabledMenuItem: {
    opacity: 0.38,
  },
  menuItemText: {
    marginTop: MD.SPACING.SMALL,
    fontSize: 14,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center',
  },
  disabledMenuItemText: {
    color: '#ccc',
  },
});
