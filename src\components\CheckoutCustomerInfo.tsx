import React from 'react';
import { View, Text } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { CheckoutCustomerInfoProps } from '../types/business';
import { FINANCIAL } from '../constants/config';
import { ICON_SIZES } from '../constants/ui';
import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import { formatCurrency } from '../utils/formatting';
import { checkoutCustomerInfoStyles } from '../styles/CheckoutCustomerInfo';


const CheckoutCustomerInfo: React.FC<CheckoutCustomerInfoProps> = ({
  customerName,
  customerInfo
}) => {
  const { tax_id, custom_credit_limit, outstanding, mobile_no, customer_primary_address } = customerInfo;

  return (
    <View style={checkoutCustomerInfoStyles.container}>
      <Text style={checkoutCustomerInfoStyles.customerName} numberOfLines={2}>
        {customerName}
      </Text>

      {tax_id ? (
        <Text style={checkoutCustomerInfoStyles.customerDetails}>
          {FINANCIAL.LABELS.TIN}{tax_id}
        </Text>
      ) : null}

      {/* Contact Information */}
      {mobile_no ? (
        <View style={checkoutCustomerInfoStyles.contactRow}>
          <MaterialCommunityIcons
            name="phone"
            size={ICON_SIZES.SMALL}
            color={NEUTRAL.TEXT_SECONDARY}
            style={checkoutCustomerInfoStyles.icon}
          />
          <Text style={checkoutCustomerInfoStyles.customerDetails}>
            {mobile_no}
          </Text>
        </View>
      ) : null}

      {customer_primary_address ? (
        <View style={checkoutCustomerInfoStyles.contactRow}>
          <MaterialCommunityIcons
            name="map-marker"
            size={ICON_SIZES.SMALL}
            color={NEUTRAL.TEXT_SECONDARY}
            style={checkoutCustomerInfoStyles.icon}
          />
          <Text style={checkoutCustomerInfoStyles.customerDetails} numberOfLines={2}>
            {customer_primary_address}
          </Text>
        </View>
      ) : null}

      {(custom_credit_limit !== undefined || outstanding !== undefined) && (
        <View style={checkoutCustomerInfoStyles.financialRow}>
          <View style={checkoutCustomerInfoStyles.financialItem}>
            <MaterialCommunityIcons
              name="credit-card-outline"
              size={ICON_SIZES.SMALL}
              color={NEUTRAL.TEXT_SECONDARY}
              style={checkoutCustomerInfoStyles.icon}
            />
            <Text style={checkoutCustomerInfoStyles.customerDetails}>
              {custom_credit_limit > 0
                ? formatCurrency(custom_credit_limit).replace(FINANCIAL.CURRENCY_REPLACE, '')
                : FINANCIAL.PLACEHOLDER
              }
            </Text>
          </View>

          <View style={checkoutCustomerInfoStyles.financialItem}>
            <MaterialCommunityIcons
              name="cash-multiple"
              size={ICON_SIZES.SMALL}
              color={NEUTRAL.TEXT_SECONDARY}
              style={checkoutCustomerInfoStyles.icon}
            />
            <Text style={[
              checkoutCustomerInfoStyles.customerDetails,
              outstanding > 0 ? checkoutCustomerInfoStyles.outstandingValue : null
            ]}>
              {outstanding > 0
                ? formatCurrency(outstanding).replace(FINANCIAL.CURRENCY_REPLACE, '')
                : FINANCIAL.PLACEHOLDER
              }
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};


export default React.memo(CheckoutCustomerInfo);
