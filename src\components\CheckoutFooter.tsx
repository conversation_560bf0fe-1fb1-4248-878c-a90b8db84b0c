import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CheckoutFooterProps } from '../types/business';
import { BUTTON_TEXT } from '../constants/messages';
import { formatCurrency } from '../utils/formatting';
import { checkoutFooterStyles } from '../styles/CheckoutFooter';

/**
 * Reusable footer component for checkout screen with place order button
 */
const CheckoutFooter: React.FC<CheckoutFooterProps> = ({
  onPlaceOrder,
  loading,
  disabled,
  totalAmount
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[
      checkoutFooterStyles.footer,
      { paddingBottom: Math.max(insets.bottom, 16) }
    ]}>
      <TouchableOpacity
        onPress={onPlaceOrder}
        disabled={loading || disabled}
        style={[
          checkoutFooterStyles.placeOrderButton,
          (loading || disabled) ? checkoutFooterStyles.disabledButton : {}
        ]}
      >
        {loading ? (
          <ActivityIndicator color="#fff" size="small" />
        ) : (
          <>
            <Text style={checkoutFooterStyles.placeOrderButtonText}>
              {BUTTON_TEXT.CHECKOUT.PLACE_ORDER}
            </Text>
            <Text style={checkoutFooterStyles.placeOrderButtonAmount}>
              {formatCurrency(totalAmount)}
            </Text>
          </>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default React.memo(CheckoutFooter);
