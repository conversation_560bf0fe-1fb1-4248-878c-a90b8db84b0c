import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';
import { AppState, AppStateStatus } from 'react-native';
import { fetchItemPrices } from '../services/itemPriceService';
import { fetchItems } from '../services/itemService';
import { DATABASE } from '../constants/storage';
import { backgroundOperationsManager, BackgroundOperationType } from '../orchestrators/backgroundOperations';

const { NAME: DATABASE_NAME, TABLES: { ITEMS: ITEMS_TABLE, ITEM_PRICES: ITEM_PRICES_TABLE } } = DATABASE;

// Type definitions for database entities
interface Item {
  item_code: string;
  item_name: string;
  item_group: string;
  brand: string;
  stock_uom: string;
  sales_uom: string;
  supplier_short_name: string;
  default_supplier: string;
}

interface ItemPrice {
  id?: number;
  item_code: string;
  uom: string;
  territory: string;
  customer_group: string;
  rate: number;
  conversion_factor: number;
}

interface UomPrice {
  uom: string;
  conversion_factor: number;
  rate: number;
}

// Combined type for item with its prices
interface ItemWithPrices extends Item {
  uoms: UomPrice[];
}

// Database state
let dbInstance: SQLite.SQLiteDatabase | null = null;
let isInitialized = false;
let isUsingInMemoryFallback = false; // Flag to track if we're using the fallback

// Simplified in-memory database structure
interface InMemoryDatabase {
  items: Map<string, Item>;
  prices: Map<string, ItemPrice>;

  // Get stats
  getStats(): { itemCount: number; priceCount: number };

  // Clear data
  clear(): void;
}

// In-memory database implementation
const inMemoryDb: InMemoryDatabase = {
  items: new Map<string, Item>(),
  prices: new Map<string, ItemPrice>(),

  getStats(): { itemCount: number; priceCount: number } {
    return {
      itemCount: this.items.size,
      priceCount: this.prices.size
    };
  },

  clear(): void {
    this.items.clear();
    this.prices.clear();
  }
};

/**
 * Get the database connection
 * Uses in-memory storage as a fallback when SQLite fails
 */
export const getDatabase = async (): Promise<SQLite.SQLiteDatabase> => {
  // Return existing instance if available
  if (dbInstance) {
    try {
      // Test if the connection is still valid
      await dbInstance.execAsync('SELECT 1');
      return dbInstance;
    } catch (error) {
      // Connection lost, clean up and recreate
      try {
        if (dbInstance) {
          await dbInstance.closeAsync();
        }
      } catch (closeError) {
        // Ignore close errors
      }
      dbInstance = null;
    }
  }

  try {
    // If we know SQLite is broken, use in-memory fallback
    if (isUsingInMemoryFallback) {
      dbInstance = createInMemoryDatabase();
      if (__DEV__) console.log('Created in-memory database');
      return dbInstance;
    }

    // Try to open SQLite database
    try {
      dbInstance = await SQLite.openDatabaseAsync(DATABASE_NAME);
      await dbInstance.execAsync('SELECT 1'); // Test connection

      // 🚀 WORLD-CLASS SQLite optimization pragmas for maximum performance
      await dbInstance.execAsync(`
        PRAGMA journal_mode=WAL;
        PRAGMA synchronous=NORMAL;
        PRAGMA cache_size=20000;
        PRAGMA temp_store=MEMORY;
        PRAGMA mmap_size=536870912;
        PRAGMA page_size=4096;
        PRAGMA auto_vacuum=INCREMENTAL;
        PRAGMA wal_autocheckpoint=1000;
        PRAGMA busy_timeout=30000;
        PRAGMA foreign_keys=ON;
        PRAGMA optimize;
        PRAGMA analysis_limit=1000;
        PRAGMA threads=4;
      `);

      isInitialized = true;
      // Database opened successfully
      return dbInstance;
    } catch (error) {
      if (__DEV__) {
        console.log('SQLite error, switching to in-memory database');
      }

      // Close the database if it was opened
      if (dbInstance) {
        try {
          await dbInstance.closeAsync();
        } catch (closeError) {
          // Ignore close errors
        }
      }

      // Switch to in-memory database
      isUsingInMemoryFallback = true;
      dbInstance = createInMemoryDatabase();
      if (__DEV__) console.log('Created in-memory fallback database');
      return dbInstance;
    }
  } catch (error) {
    // 🚀 CRITICAL: Log database initialization errors
    if (__DEV__) console.error('Error initializing database:', error);
    dbInstance = null;
    throw error;
  }
};

/**
 * Create a simplified in-memory database implementation
 */
function createInMemoryDatabase(): SQLite.SQLiteDatabase {
  // Create a mock database that implements the minimal SQLite interface
  const mockDb: any = {
    _isInMemoryDb: true,

    // Handle SQL commands
    execAsync: async (sql: string) => {
      if (sql.includes('DROP TABLE')) {
        const tableName = sql.match(/DROP TABLE IF EXISTS (\w+)/)?.[1];
        if (tableName === ITEMS_TABLE) {
          inMemoryDb.items.clear();
        } else if (tableName === ITEM_PRICES_TABLE) {
          inMemoryDb.prices.clear();
        }
      }
      return [];
    },

    // Execute transaction callback directly
    withTransactionAsync: async (callback: () => Promise<any>) => {
      return await callback();
    },

    getAllAsync: async (sql: string, params: any[] = []) => {
      if ((sql.includes(`FROM ${ITEMS_TABLE}`) && sql.includes(`JOIN ${ITEM_PRICES_TABLE}`)) ||
          (sql.includes(`FROM ${ITEM_PRICES_TABLE}`) && sql.includes(`JOIN ${ITEMS_TABLE}`))) {
        if (params.length >= 2) {
          const territory = params[0];
          const customerGroup = params[1];
          const pricePrefix = `${territory}:${customerGroup}:`;
          const results: any[] = [];

          for (const [key, price] of inMemoryDb.prices.entries()) {
            if (key.startsWith(pricePrefix)) {
              const item = inMemoryDb.items.get(price.item_code);
              if (item) {
                results.push({
                  item_code: item.item_code,
                  item_name: item.item_name,
                  item_group: item.item_group,
                  supplier_short_name: item.supplier_short_name,
                  stock_uom: item.stock_uom,
                  sales_uom: item.sales_uom,
                  uom: price.uom,
                  rate: price.rate,
                  conversion_factor: price.conversion_factor
                });
              }
            }
          }

          return results;
        }
      }
      return [];
    },

    // Handle single-row queries
    getFirstAsync: async (sql: string, params: any[] = []) => {
      // Handle count queries
      if (sql.includes('COUNT')) {
        if (sql.includes(`FROM ${ITEM_PRICES_TABLE}`)) {
          return { count: inMemoryDb.prices.size };
        } else if (sql.includes(`FROM ${ITEMS_TABLE}`)) {
          return { count: inMemoryDb.items.size };
        }
      }
      // Handle price lookup
      else if (sql.includes(`FROM ${ITEM_PRICES_TABLE}`) && sql.includes('rate')) {
        if (params.length >= 4) {
          const compositeKey = `${params[2]}:${params[3]}:${params[0]}:${params[1]}`;
          const price = inMemoryDb.prices.get(compositeKey);
          return price ? { rate: price.rate } : null;
        }
      }
      return null;
    },

    // No-op close
    closeAsync: async () => {}
  };

  isInitialized = true;
  return mockDb as SQLite.SQLiteDatabase;
}

/**
 * Check if the database is initialized and ready to use
 */
export const isDatabaseInitialized = (): boolean => isInitialized && dbInstance !== null;

/**
 * Close the database connection and clean up resources
 */
export const closeDatabase = async (): Promise<void> => {
  try {
    // If no database instance, nothing to close
    if (!dbInstance) {
      isInitialized = false;
      return;
    }

    // Close database with timeout
    try {
      await Promise.race([
        dbInstance.closeAsync().catch(() => {}),
        new Promise(resolve => setTimeout(resolve, 1000))
      ]);
    } catch (error) {
      // Ignore errors during close
    }
  } catch (error) {
    if (__DEV__) console.warn('Error in closeDatabase:', error);
  } finally {
    // Always reset state
    dbInstance = null;
    isInitialized = false;
  }
};

// This function has been removed and replaced with direct calls

/**
 * Create the database tables with basic structure
 */
export const createTables = async (): Promise<void> => {
  try {
    const db = await getDatabase();

    // Create items table
    try {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS ${ITEMS_TABLE} (
          item_code TEXT PRIMARY KEY,
          item_name TEXT NOT NULL,
          item_group TEXT,
          brand TEXT,
          stock_uom TEXT,
          sales_uom TEXT,
          default_supplier TEXT,
          supplier_short_name TEXT
        );
      `);

      // Create basic index
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_items_supplier ON ${ITEMS_TABLE}(supplier_short_name);`);
    } catch (error) {
      if (!isUsingInMemoryFallback) {
        await closeDatabase();
        isUsingInMemoryFallback = true;
        dbInstance = null;

        const newDb = await getDatabase();
        await newDb.execAsync(`
          CREATE TABLE IF NOT EXISTS ${ITEMS_TABLE} (
            item_code TEXT PRIMARY KEY,
            item_name TEXT NOT NULL,
            item_group TEXT,
            brand TEXT,
            stock_uom TEXT,
            sales_uom TEXT,
            default_supplier TEXT,
            supplier_short_name TEXT
          );
        `);
        await newDb.execAsync(`CREATE INDEX IF NOT EXISTS idx_items_supplier ON ${ITEMS_TABLE}(supplier_short_name);`);
      } else {
        throw error;
      }
    }

    // Create item_prices table
    try {
      await db.execAsync(`
        CREATE TABLE IF NOT EXISTS ${ITEM_PRICES_TABLE} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          item_code TEXT NOT NULL,
          uom TEXT NOT NULL,
          territory TEXT NOT NULL,
          customer_group TEXT NOT NULL,
          rate REAL NOT NULL,
          conversion_factor REAL NOT NULL,
          FOREIGN KEY(item_code) REFERENCES ${ITEMS_TABLE}(item_code),
          UNIQUE(item_code, uom, territory, customer_group)
        );
      `);

      // Create basic index
      await db.execAsync(`CREATE INDEX IF NOT EXISTS idx_item_prices_territory_customer_group ON ${ITEM_PRICES_TABLE}(territory, customer_group);`);
    } catch (error) {
      if (!isUsingInMemoryFallback) {
        await closeDatabase();
        isUsingInMemoryFallback = true;
        dbInstance = null;

        const newDb = await getDatabase();
        await newDb.execAsync(`
          CREATE TABLE IF NOT EXISTS ${ITEM_PRICES_TABLE} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_code TEXT NOT NULL,
            uom TEXT NOT NULL,
            territory TEXT NOT NULL,
            customer_group TEXT NOT NULL,
            rate REAL NOT NULL,
            conversion_factor REAL NOT NULL,
            FOREIGN KEY(item_code) REFERENCES ${ITEMS_TABLE}(item_code),
            UNIQUE(item_code, uom, territory, customer_group)
          );
        `);
        await newDb.execAsync(`CREATE INDEX IF NOT EXISTS idx_item_prices_territory_customer_group ON ${ITEM_PRICES_TABLE}(territory, customer_group);`);
      } else {
        throw error;
      }
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Recreate the database tables
 * This will delete all existing data
 * Works with both SQLite and in-memory database
 */
export const recreateTables = async (): Promise<void> => {
  try {
    // First close any existing database connection to prevent issues
    await closeDatabase();

    // Get a fresh database connection
    const db = await getDatabase();

    if ((db as any)._isInMemoryDb) {
      inMemoryDb.items.clear();
      inMemoryDb.prices.clear();
      return;
    }

    try {
      await db.execAsync(`DROP TABLE IF EXISTS ${ITEM_PRICES_TABLE};`);
      await db.execAsync(`DROP TABLE IF EXISTS ${ITEMS_TABLE};`);
    } catch (error) {
      // Continue anyway - the tables might not exist
    }

    // Create the tables with the new schema
    await createTables();

  } catch (error) {
    if (!isUsingInMemoryFallback) {
      isUsingInMemoryFallback = true;
      dbInstance = null;

      try {
        await getDatabase();
      } catch (fallbackError) {
        // Silent fail
      }
    }

    throw error;
  }
};

/**
 * Sync all data from API to database
 * @param forceRecreate If true, recreate the tables before syncing
 */
export const syncAllData = async (forceRecreate = false): Promise<{ itemCount: number, priceCount: number }> => {
  try {
    const [itemsResponse, pricesResponse] = await Promise.all([
      fetchItems(),
      fetchItemPrices()
    ]);

    const items = itemsResponse || [];
    const prices = pricesResponse || [];

    await getDatabase();

    if (forceRecreate) {
      await recreateTables();
    } else {
      await createTables();
    }

    await storeItems(items || [], forceRecreate);

    await new Promise(resolve => setTimeout(resolve, 50));

    const itemStats = await getDatabaseStats();
    if (itemStats.itemCount === 0 && items.length > 0) {
      throw new Error('Items were not stored successfully, cannot proceed with prices');
    }

    const itemCodes = new Set(items.map(item => item.item_code));
    const validPrices = prices.filter(price => itemCodes.has(price.item_code));

    await storeItemPrices(validPrices || []);

    return { itemCount: items?.length || 0, priceCount: prices?.length || 0 };
  } catch (error) {
    if (!isUsingInMemoryFallback) {
      try {
        isUsingInMemoryFallback = true;
        dbInstance = null;

        try {
          await closeDatabase();
        } catch (closeError) {
          // Ignore close errors
        }

        return await syncAllData(forceRecreate);
      } catch (fallbackError) {
        return { itemCount: 0, priceCount: 0 };
      }
    }

    return { itemCount: 0, priceCount: 0 };
  }
};

/**
 * Store items in the database
 * @param items The items to store
 * @param forceRecreate Whether to clear existing items before storing
 */
export const storeItems = async (items: {
  item_code: string;
  item_name: string;
  item_group?: string;
  brand?: string;
  stock_uom?: string;
  sales_uom?: string;
  default_supplier?: string;
  supplier_short_name?: string;
}[], forceRecreate: boolean = false): Promise<void> => {
  try {
    // Add null/undefined check for items
    if (!items || !items.length) {
      return;
    }

    const db = await getDatabase();

    // Check if we're using the in-memory database
    if ((db as any)._isInMemoryDb) {
      // Clear existing items if needed
      if (forceRecreate) {
        inMemoryDb.items.clear();
      }

      // Store items
      for (const item of items) {
        if (!item.item_code) continue;

        inMemoryDb.items.set(item.item_code, {
          item_code: item.item_code,
          item_name: item.item_name || `Item ${item.item_code}`,
          item_group: item.item_group || '',
          brand: item.brand || '',
          stock_uom: item.stock_uom || '',
          sales_uom: item.sales_uom || '',
          default_supplier: item.default_supplier || '',
          supplier_short_name: item.supplier_short_name || ''
        });
      }
    } else {
      // Using real SQLite database
      try {
        // Start transaction
        await db.execAsync('BEGIN TRANSACTION;');

        // Process in larger batches for better performance
        const BATCH_SIZE = 1000;

        for (let i = 0; i < items.length; i += BATCH_SIZE) {
          const batch = items.slice(i, i + BATCH_SIZE);

          // Build a multi-value insert
          let sql = `
            INSERT OR REPLACE INTO ${ITEMS_TABLE} (
              item_code, item_name, item_group, brand, stock_uom, sales_uom,
              default_supplier, supplier_short_name
            ) VALUES
          `;

          const values = batch.map(item => `(
            '${item.item_code.replace(/'/g, "''")}',
            '${item.item_name.replace(/'/g, "''")}',
            '${(item.item_group || '').replace(/'/g, "''")}',
            '${(item.brand || '').replace(/'/g, "''")}',
            '${(item.stock_uom || '').replace(/'/g, "''")}',
            '${(item.sales_uom || '').replace(/'/g, "''")}',
            '${(item.default_supplier || '').replace(/'/g, "''")}',
            '${(item.supplier_short_name || '').replace(/'/g, "''")}'
          )`).join(',');

          sql += values;

          // Execute the batch insert
          await db.execAsync(sql);
        }

        // Commit transaction and ensure it's fully written
        await db.execAsync('COMMIT;');

        // Force a checkpoint to ensure data is written to disk
        try {
          await db.execAsync('PRAGMA wal_checkpoint(TRUNCATE);');
        } catch (checkpointError) {
          // Silent fail
        }
      } catch (error) {
        // Try to rollback on error
        try {
          await db.execAsync('ROLLBACK;');
        } catch (rollbackError) {
          // Ignore rollback errors
        }
        throw error;
      }
    }
  } catch (error) {
    throw error;
  }
};

/**
 * Store item prices in the database
 */
export const storeItemPrices = async (prices: {
  item_code: string;
  uom: string;
  territory: string;
  customer_group: string;
  rate: number;
  conversion_factor: number;
}[]): Promise<void> => {
  try {
    // Add null/undefined check for prices
    if (!prices || !prices.length) {
      return;
    }

    const db = await getDatabase();

    // Check if we're using the in-memory database
    if ((db as any)._isInMemoryDb) {
      // Store prices
      let storedCount = 0;
      for (const price of prices) {
        // Ensure numeric values
        const rate = +price.rate || 0;
        const conversionFactor = +price.conversion_factor || 1;

        // Create composite key for lookup
        const compositeKey = `${price.territory}:${price.customer_group}:${price.item_code}:${price.uom}`;

        // Store price
        inMemoryDb.prices.set(compositeKey, {
          id: Date.now(), // Simple unique ID
          item_code: price.item_code,
          uom: price.uom,
          territory: price.territory,
          customer_group: price.customer_group,
          rate: rate,
          conversion_factor: conversionFactor
        });
        storedCount++;
      }


    } else {
      // Using real SQLite database
      try {
        // Start transaction
        await db.execAsync('BEGIN TRANSACTION;');

        // Temporarily disable foreign key constraints to prevent issues during bulk insert
        await db.execAsync('PRAGMA foreign_keys=OFF;');

        // Process in larger batches for better performance
        const BATCH_SIZE = 1000;

        for (let i = 0; i < prices.length; i += BATCH_SIZE) {
          const batch = prices.slice(i, i + BATCH_SIZE);

          // Build a multi-value insert
          let sql = `
            INSERT OR REPLACE INTO ${ITEM_PRICES_TABLE} (
              item_code, uom, territory, customer_group,
              rate, conversion_factor
            ) VALUES
          `;

          const values = batch.map(price => {
            // Ensure numeric values
            const rate = +price.rate || 0;
            const conversionFactor = +price.conversion_factor || 1;

            return `(
              '${price.item_code.replace(/'/g, "''")}',
              '${price.uom.replace(/'/g, "''")}',
              '${price.territory.replace(/'/g, "''")}',
              '${price.customer_group.replace(/'/g, "''")}',
              ${rate},
              ${conversionFactor}
            )`;
          }).join(',');

          sql += values;

          // Execute the batch insert
          await db.execAsync(sql);
        }

        // Re-enable foreign key constraints
        await db.execAsync('PRAGMA foreign_keys=ON;');

        // Commit transaction
        await db.execAsync('COMMIT;');
      } catch (error) {
        // Try to rollback on error and re-enable foreign keys
        try {
          await db.execAsync('PRAGMA foreign_keys=ON;');
          await db.execAsync('ROLLBACK;');
        } catch (rollbackError) {
          // Ignore rollback errors
        }
        throw error;
      }
    }
  } catch (error) {
    if (error instanceof Error && error.message.includes('FOREIGN KEY constraint failed')) {
      throw new Error('Cannot store prices: items not found in database. Please ensure items are stored first.');
    }
    throw error;
  }
};

/**
 * Get price for a specific item, UOM, territory and customer group
 */
export const getItemPrice = async (
  itemCode: string,
  uom: string,
  territory: string,
  customerGroup: string
): Promise<number | null> => {
  try {
    if (!itemCode || !uom || !territory || !customerGroup) return null;

    const db = await getDatabase();

    // Check if we're using the in-memory database
    if ((db as any)._isInMemoryDb) {
      try {
        // Create composite key for lookup
        const compositeKey = `${territory}:${customerGroup}:${itemCode}:${uom}`;

        // Direct lookup
        const price = inMemoryDb.prices.get(compositeKey);
        return price ? price.rate : null;
      } catch (error) {
        return null;
      }
    } else {
      // Using real SQLite database
      const result = await db.getFirstAsync<{ rate: number }>(`
        SELECT rate FROM ${ITEM_PRICES_TABLE}
        WHERE item_code = ? AND uom = ? AND territory = ? AND customer_group = ?
        LIMIT 1
      `, [itemCode, uom, territory, customerGroup]);

      return result ? result.rate : null;
    }
  } catch (error) {
    return null;
  }
};

export const getDatabaseStats = async (): Promise<{ itemCount: number, priceCount: number }> => {
  try {
    const db = await getDatabase();

    if ((db as any)._isInMemoryDb) {
      try {
        return inMemoryDb.getStats();
      } catch (error) {
        return { itemCount: 0, priceCount: 0 };
      }
    } else {
      try {
        const itemCount = await db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM ${ITEMS_TABLE}`);
        const priceCount = await db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM ${ITEM_PRICES_TABLE}`);

        return {
          itemCount: itemCount?.count || 0,
          priceCount: priceCount?.count || 0
        };
      } catch (error) {
        return { itemCount: 0, priceCount: 0 };
      }
    }
  } catch (error) {
    return { itemCount: 0, priceCount: 0 };
  }
};

/**
 * Process query results into grouped items with prices - optimized for performance
 */
const processQueryResults = (rows: any[]): ItemWithPrices[] => {
  if (!rows?.length) return [];

  // Group by item_code with optimized Map operations
  const itemMap = new Map<string, ItemWithPrices>();

  for (const row of rows) {
    const itemCode = row.item_code;

    // Create UOM object
    const uomObject = {
      uom: row.uom || '',
      conversion_factor: Number(row.conversion_factor) || 1,
      rate: Number(row.rate) || 0
    };

    if (itemMap.has(itemCode)) {
      // Add UOM to existing item
      itemMap.get(itemCode)!.uoms.push(uomObject);
    } else {
      // Create new item
      const newItem: ItemWithPrices = {
        item_code: itemCode,
        item_name: row.item_name || '',
        item_group: row.item_group || '',
        supplier_short_name: row.supplier_short_name || '',
        stock_uom: row.stock_uom || '',
        sales_uom: row.sales_uom || '',
        brand: row.brand || '',
        default_supplier: '',
        uoms: [uomObject]
      };

      itemMap.set(itemCode, newItem);
    }
  }

  return Array.from(itemMap.values());
};

// Enhanced in-memory cache for query results with better performance
const queryCache = new Map<string, { timestamp: number, data: ItemWithPrices[] }>();
const MAX_CACHE_SIZE = 20; // Increased cache size for better performance

/**
 * Get items with prices from database with simple caching (without actual quantities)
 * Use this function when you specifically don't need actual quantities for performance
 */
export const getItemsWithPricesOnly = async (
  territory: string,
  customerGroup: string
): Promise<ItemWithPrices[]> => {
  try {
    // Validate inputs
    if (!territory || !customerGroup) {
      throw new Error(`Territory and customer group are required`);
    }

    // Generate cache key
    const cacheKey = `${territory}:${customerGroup}`;

    // Debug: Log the query parameters
    // Check cache for existing data without TTL check
    // Item price cache doesn't expire with time - only cleared on manual sync
    const cached = queryCache.get(cacheKey);

    if (cached) {
      return cached.data;
    }

    const db = await getDatabase();
    let rows: any[];

    // Execute query
    const query = `
      SELECT
        i.item_code,
        i.item_name,
        i.item_group,
        i.brand,
        i.supplier_short_name,
        i.stock_uom,
        i.sales_uom,
        p.uom,
        p.rate,
        p.conversion_factor
      FROM ${ITEM_PRICES_TABLE} p
      INNER JOIN ${ITEMS_TABLE} i
      ON p.item_code = i.item_code
      WHERE p.territory = ? AND p.customer_group = ?
      ORDER BY i.item_name
    `;

    rows = await db.getAllAsync<any>(query, [territory, customerGroup]);

    if (!rows.length) {
      return [];
    }

    const results = processQueryResults(rows);

    queryCache.set(cacheKey, { timestamp: Date.now(), data: results });

    // Enhanced cache management with better performance
    if (queryCache.size > MAX_CACHE_SIZE) {
      const oldestKey = Array.from(queryCache.entries())
        .sort(([, a], [, b]) => a.timestamp - b.timestamp)[0][0];
      queryCache.delete(oldestKey);
    }

    return results;
  } catch (error) {
    throw error;
  }
};

/**
 * Get items with prices and actual quantities for a specific territory and customer group
 * This function merges item prices with actual quantity data from the bin service
 */
export const getItemsWithPricesAndQuantities = async (
  territory: string,
  customerGroup: string
): Promise<ItemWithPrices[]> => {
  try {
    // Get items with prices from cache/database (ultra-fast)
    const itemsWithPrices = await getItemsWithPricesOnly(territory, customerGroup);

    // Import the quantity cache utility dynamically to avoid circular dependencies
    const { getActualQuantityMapWithCache } = await import('./quantityCache');

    // Get actual quantities with smart caching (fallback to original approach)
    const quantityMap = await getActualQuantityMapWithCache();

    // Merge actual quantities with items
    const itemsWithQuantities = itemsWithPrices.map(item => ({
      ...item,
      actual_qty: quantityMap.get(item.item_code) || 0
    }));

    return itemsWithQuantities;
  } catch (error) {
    if (__DEV__) console.warn('Failed to get actual quantities, returning items without quantities:', error);
    // Fallback to items without quantities if quantity fetch fails
    return getItemsWithPricesOnly(territory, customerGroup);
  }
};

/**
 * Get items with prices from database with simple caching
 * This is the main function that now includes actual quantities by default
 */
export const getItemsWithPricesWithCache = async (
  territory: string,
  customerGroup: string
): Promise<ItemWithPrices[]> => {
  return getItemsWithPricesAndQuantities(territory, customerGroup);
};

/**
 * Get items with prices for a specific territory and customer group
 * Alias for getItemsWithPricesAndQuantities for backward compatibility
 * This now includes actual quantities by default
 */
export const getItemsWithPrices = getItemsWithPricesAndQuantities;

/**
 * Check if prices exist for a specific territory and customer group
 */
export const checkPricesExist = async (
  territory: string,
  customerGroup: string
): Promise<boolean> => {
  try {
    if (!territory || !customerGroup) return false;

    const db = await getDatabase();

    // Check if we're using the in-memory database
    if ((db as any)._isInMemoryDb) {
      try {
        // Check if any prices match this territory and customer group
        const tcPrefix = `${territory}:${customerGroup}:`;

        // Check if any keys start with this prefix
        for (const key of inMemoryDb.prices.keys()) {
          if (key.startsWith(tcPrefix)) {
            return true;
          }
        }

        return false;
      } catch (error) {
        return false;
      }
    } else {
      const result = await db.getFirstAsync<{ count: number }>(`
        SELECT 1 as count FROM ${ITEM_PRICES_TABLE}
        WHERE territory = ? AND customer_group = ?
        LIMIT 1
      `, [territory, customerGroup]);

      return !!result;
    }
  } catch (error) {
    return false;
  }
};

/**
 * Clear query cache and release memory
 *
 * @param specificKey Optional specific cache key to clear
 */
export const clearCache = (specificKey?: string): void => {
  if (specificKey) {
    queryCache.delete(specificKey);
  } else {
    queryCache.clear();

    // Clear in-memory database if we're using it
    if (isUsingInMemoryFallback) {
      inMemoryDb.clear();
    }
  }
};

/**
 * Initialize the database connection
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    await closeDatabase();
  } catch (error) {
    // Ignore errors during cleanup
  }

  try {
    dbInstance = null;
    isInitialized = false;

    await getDatabase();
    await createTables();
  } catch (error) {
    if (!isUsingInMemoryFallback) {
      isUsingInMemoryFallback = true;
      dbInstance = null;

      try {
        await getDatabase();
        await createTables();
      } catch (fallbackError) {
        // Silent fail
      }
    }
  }
};

// Track app initialization state
let isAppInitialized = false;
let appStateListener: ReturnType<typeof AppState.addEventListener> | null = null;

/**
 * Initialize the database on app startup
 * This should be called when the app starts
 */
export const initializeDatabaseOnStartup = async (): Promise<void> => {
  // If already initialized, don't do it again
  if (isAppInitialized) return;

  backgroundOperationsManager.startOperation(BackgroundOperationType.DATABASE_INIT);

  try {
    await initializeDatabase();
    isAppInitialized = true;

    if (!appStateListener) {
      appStateListener = AppState.addEventListener('change', handleAppStateChange);
    }

    backgroundOperationsManager.completeOperation();
  } catch (error) {
    backgroundOperationsManager.failOperation();
  }
};

/**
 * Handle app state changes
 * Reinitializes the database when the app comes back from background
 */
const handleAppStateChange = async (nextAppState: AppStateStatus): Promise<void> => {
  if (!isAppInitialized) {
    return;
  }

  if (nextAppState === 'active') {
    try {
      // Check if database is still valid
      if (dbInstance) {
        try {
          await dbInstance.execAsync('SELECT 1');
          return; // Database is still valid
        } catch (error) {
          // Connection lost, reinitialize
        }
      }

      isAppInitialized = false;
      await initializeDatabase();
    } catch (error) {
      // Silent fail
    }
  } else if (nextAppState === 'background') {
    // Clean up resources when app goes to background
    try {
      clearCache();
    } catch (error) {
      // Ignore errors during cleanup
    }
  }
};

/**
 * Delete the database file
 * This should be called during logout to ensure a clean separation between user sessions
 */
export const deleteDatabase = async (): Promise<void> => {
  try {
    await closeDatabase();

    dbInstance = null;
    isInitialized = false;
    isAppInitialized = false;
    isUsingInMemoryFallback = false;

    clearCache();

    const dbPath = FileSystem.documentDirectory + 'SQLite/' + DATABASE_NAME;

    const dbInfo = await FileSystem.getInfoAsync(dbPath);
    if (dbInfo.exists) {
      try {
        await FileSystem.deleteAsync(dbPath);
      } catch (deleteError) {
        // Silent fail
      }
    }
  } catch (error) {
    // Silent fail
  }
};

/**
 * Manually reinitialize the database
 * This can be called when database operations fail
 */
export const reinitializeDatabase = async (): Promise<void> => {
  try {
    await closeDatabase();

    dbInstance = null;
    isInitialized = false;
    isAppInitialized = false;

    clearCache();

    isUsingInMemoryFallback = false;

    await initializeDatabaseOnStartup();
  } catch (error) {
    isUsingInMemoryFallback = true;
    dbInstance = null;

    try {
      await initializeDatabaseOnStartup();
    } catch (fallbackError) {
      throw fallbackError;
    }
  }
};