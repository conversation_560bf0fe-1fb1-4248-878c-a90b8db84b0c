import { StyleSheet } from 'react-native';
import { BRAND, NEUTRAL, SEMANTIC } from '../constants/colors';
import MD from '../constants/design';
const itemCardStyles = StyleSheet.create({
  itemContainer: {
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.LARGE,
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
    width: '100%',
    alignSelf: 'stretch',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    marginBottom: MD.SPACING.XSMALL,
  },
  indexContainer: {
    width: 20, // Slightly smaller than transaction items
    height: 20,
    borderRadius: 10,
    backgroundColor: `${BRAND.PRIMARY}20`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: MD.SPACING.SMALL,
  },
  index: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    fontWeight: '600',
    color: BRAND.PRIMARY,
  },
  nameContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // Match transaction item font size
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
  },
  detailsRow: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    paddingLeft: 20 + MD.SPACING.SMALL, // circle width + margin (match transaction pattern)
  },
  detailItem: {
    flex: 1, // Equal width columns
    alignItems: 'center', // Center content horizontally
  },
  detailLabel: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
    color: NEUTRAL.TEXT_SECONDARY,
    marginBottom: MD.SPACING.XSMALL,
    textAlign: 'center', // Center label text
  },
  detailValue: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    textAlign: 'center', // Center value text
  },
  // Remove old control styles - we'll use the transaction-inspired layout
});

export const cartScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE, // Changed from BACKGROUND_LIGHT to WHITE for consistency
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: MD.SPACING.LARGE,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize, // Use consistent typography
    color: NEUTRAL.TEXT_SECONDARY,
  },
  listContainer: {
    flex: 1,
    backgroundColor: NEUTRAL.WHITE, // Changed from BACKGROUND_LIGHT to WHITE for consistency
  },
  flashListContainer: {
    flex: 1,
    paddingBottom: 0, // Let contentContainerStyle handle the bottom padding
  },
  // Cart container styles - matching customer and transaction screen flat design
  cartContainer: {
    backgroundColor: NEUTRAL.WHITE,
    marginBottom: 0,
    overflow: 'hidden',
    borderRadius: 0, // Flat design like customer and transaction screens
    borderBottomWidth: 1,
    borderBottomColor: MD.DIVIDER,
  },
  cartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: MD.SPACING.SMALL, // Match customer/transaction card padding
    paddingHorizontal: MD.SPACING.LARGE, // Match customer/transaction card padding
    backgroundColor: NEUTRAL.WHITE,
    borderBottomWidth: 0, // No border by default
    borderBottomColor: MD.DIVIDER,
  },
  cartHeaderExpanded: {
    backgroundColor: NEUTRAL.WHITE, // Keep white background for consistency with transaction screen
  },
  cartHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  cartIcon: {
    marginRight: MD.SPACING.MEDIUM,
  },
  cartCustomerName: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    marginBottom: 4,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
  },
  cartSubInfo: {
    flexDirection: 'column',
  },
  cartItemCount: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  cartTotal: {
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    color: BRAND.PRIMARY,
    fontWeight: '500',
    letterSpacing: MD.TYPOGRAPHY.BODY2.letterSpacing,
  },
  cartTimestamp: {
    fontSize: MD.TYPOGRAPHY.CAPTION.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    marginTop: 2,
    letterSpacing: MD.TYPOGRAPHY.CAPTION.letterSpacing,
  },
  cartContent: {
    paddingHorizontal: MD.SPACING.LARGE, // Match customer/transaction screen padding
    paddingTop: MD.SPACING.SMALL,
    paddingBottom: 0, // Remove bottom padding since we'll have the action buttons there
    backgroundColor: NEUTRAL.WHITE, // Match customer and transaction screen white background
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    flex: 1, // Allow content to expand
    display: 'flex', // Enable flexbox layout
    flexDirection: 'column', // Stack children vertically
    minHeight: 200, // Ensure minimum height for small carts
  },

  itemsContainer: {
    marginTop: 0,
  },
  bottomActionsContainer: {
    marginTop: 'auto', // Push to the bottom of the container
    borderTopWidth: 1,
    borderTopColor: MD.DIVIDER,
    backgroundColor: NEUTRAL.WHITE,
    paddingTop: MD.SPACING.SMALL,
  },
  cartActions: {
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingBottom: 16, // Fixed padding for Android
    flexDirection: 'row', // Changed to row for horizontal layout
    justifyContent: 'space-between', // Space buttons evenly
    alignItems: 'center',
    width: '100%', // Ensure full width
  },
  checkoutButton: {
    backgroundColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    height: 40, // Reduced from 48
    flex: 1, // Take available space
    marginRight: MD.SPACING.SMALL, // Add spacing between buttons
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 0, // Remove elevation for flat design consistency
  },
  checkoutButtonText: {
    color: NEUTRAL.WHITE,
    fontWeight: '500',
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // Reduced from BODY1
    letterSpacing: 0.25,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: MD.SPACING.SMALL, // Reduced from MEDIUM
    paddingHorizontal: MD.SPACING.MEDIUM, // Reduced from LARGE
    borderWidth: 1,
    borderColor: BRAND.PRIMARY,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    height: 40, // Reduced from 48
    flex: 1, // Take available space
    marginRight: MD.SPACING.SMALL, // Add spacing between buttons
  },
  buttonIcon: {
    marginRight: MD.SPACING.SMALL,
  },
  continueButtonText: {
    color: BRAND.PRIMARY,
    fontWeight: '500',
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize, // Reduced from BODY1
    letterSpacing: 0.25,
  },
  removeCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: MD.SPACING.SMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    height: 40, // Match other buttons
    borderRadius: MD.BORDER_RADIUS.SMALL,
    backgroundColor: `${SEMANTIC.ERROR}10`, // Light red background
    marginRight: MD.SPACING.SMALL, // Add spacing between delete and continue buttons
    flex: 1, // Take equal space as other buttons
  },
  removeCartText: {
    color: SEMANTIC.ERROR,
    marginLeft: MD.SPACING.SMALL,
    fontSize: MD.TYPOGRAPHY.BODY2.fontSize,
    fontWeight: '500',
    letterSpacing: 0.25,
  },
  emptyItemsText: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    padding: MD.SPACING.LARGE,
    letterSpacing: MD.TYPOGRAPHY.BODY1.letterSpacing,
    borderBottomWidth: 0, // Remove border for cleaner look
  },
  // Empty state styles
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: MD.TYPOGRAPHY.H1.fontSize, // Use consistent typography
    fontWeight: '600',
    color: NEUTRAL.TEXT_PRIMARY,
    marginTop: MD.SPACING.LARGE,
  },
  emptySubtext: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize, // Use consistent typography
    color: NEUTRAL.TEXT_SECONDARY,
    textAlign: 'center',
    marginTop: MD.SPACING.SMALL,
    marginBottom: MD.SPACING.XLARGE,
  },
  emptyButton: {
    marginTop: MD.SPACING.LARGE,
    backgroundColor: BRAND.PRIMARY,
    elevation: 0, // Remove elevation for flat design consistency
  },
  // Item card styles - transaction-inspired layout
  itemContainer: itemCardStyles.itemContainer,
  header: itemCardStyles.header,
  indexContainer: itemCardStyles.indexContainer,
  index: itemCardStyles.index,
  nameContainer: itemCardStyles.nameContainer,
  itemName: itemCardStyles.itemName,
  detailsRow: itemCardStyles.detailsRow,
  detailItem: itemCardStyles.detailItem,
  detailLabel: itemCardStyles.detailLabel,
  detailValue: itemCardStyles.detailValue,
});
