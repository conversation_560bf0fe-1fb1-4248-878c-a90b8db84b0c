import { useCallback } from 'react';
import { Alert } from 'react-native';
import { router } from 'expo-router';
import { Customer } from '../types/business';

export const useCustomerNavigation = () => {
  const handlePlaceOrder = useCallback((customer: Customer) => {
    // Ultra-fast navigation - use push for better performance
    router.push({
      pathname: '/create-order',
      params: {
        customerId: customer.name,
        customerName: customer.customer_name,
        territory: customer.territory,
        customerGroup: customer.customer_group
      }
    });
  }, []);

  const handleShowChart = useCallback((customer: Customer) => {
    Alert.alert(
      'Coming Soon',
      'Customer insights will be implemented later.',
      [{ text: 'OK' }],
      { cancelable: true }
    );
  }, []);

  const handleNavigateToInvoices = useCallback((customerId: string) => {
    router.replace({
      pathname: '/(tabs)/invoices',
      params: { customer: customerId }
    });
  }, []);

  return {
    handlePlaceOrder,
    handleShow<PERSON>hart,
    handleNavigateToInvoices
  };
};
