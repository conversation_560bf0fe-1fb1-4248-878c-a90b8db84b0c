import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const checkoutCartItemStyles = StyleSheet.create({
  itemContainer: {
    backgroundColor: NEUTRAL.WHITE,
    borderRadius: MD.BORDER_RADIUS.SMALL,
    marginBottom: MD.SPACING.XSMALL,
    marginHorizontal: MD.SPACING.XSMALL,
    paddingHorizontal: MD.SPACING.MEDIUM,
    paddingVertical: MD.SPACING.SMALL,
    elevation: MD.ELEVATION.LIGHT,
  },
  itemName: {
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    fontWeight: '500',
    color: NEUTRAL.TEXT_PRIMARY,
    marginBottom: MD.SPACING.XSMALL / 2,
    lineHeight: 18,
  },
  itemDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: MD.SPACING.XSMALL,
  },
  itemDetailColumn: {
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: 2,
    flex: 1,
  },
  itemDetailLabel: {
    fontSize: 10,
    color: NEUTRAL.TEXT_SECONDARY,
    fontWeight: '500',
    marginBottom: 2,
    letterSpacing: 0.4,
  },
  itemDetailValue: {
    fontSize: 12,
    color: NEUTRAL.TEXT_PRIMARY,
    fontWeight: '600',
    letterSpacing: 0.25,
  },
});
