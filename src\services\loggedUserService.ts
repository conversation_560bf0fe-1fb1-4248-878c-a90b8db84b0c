import { withErrorHandling } from '../utils/errors';
import { API } from '../constants/api';
import { apiClient } from './apiClient';

/**
 * Check if user is logged in by making a request to the logged user endpoint
 *
 * @returns true if the user is logged in, otherwise throws an error
 * @throws Error if the API call fails
 */
export const getLoggedUser = async (): Promise<boolean> => {
  return withErrorHandling(async () => {
    await apiClient.get(API.ENDPOINTS.LOGGED_USER);
    return true;
  });
};
