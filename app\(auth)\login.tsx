import React, { useEffect, memo, useCallback } from 'react';
import { KeyboardAvoidingView, ScrollView, Animated, BackHandler } from 'react-native';
import { LoginForm } from '../../src/components/LoginForm';
import { loginFormStyles as loginScreenStyles } from '../../src/styles/LoginScreen';
import { useLoginForm } from '../../src/hooks/useLoginForm';
import { useLoginAnimation } from '../../src/hooks/useLoginAnimation';
import { useCachedEmail } from '../../src/hooks/useCachedEmail';
import { initializeSecurityInBackground } from '../../src/utils/securityInitializer';

const LoginScreen: React.FC = memo(() => {
  const { email: cachedEmail, isLoading: emailLoading } = useCachedEmail();
  const { formState, setEmail, setPassword, toggleSecureEntry, onLoginPress } = useLoginForm(cachedEmail);
  const { animatedStyle } = useLoginAnimation();

  // Handle back button press on login screen - exit app
  const handleBackPress = useCallback(() => {
    BackHandler.exitApp();
    return true;
  }, []);

  useEffect(() => {
    initializeSecurityInBackground();
  }, []);

  useEffect(() => {
    if (cachedEmail && !emailLoading) {
      setEmail(cachedEmail);
    }
  }, [cachedEmail, emailLoading, setEmail]);

  // Set up back button handler for login screen
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);
    return () => backHandler.remove();
  }, [handleBackPress]);

  return (
    <KeyboardAvoidingView
      style={loginScreenStyles.container}
      behavior="height"
      keyboardVerticalOffset={20}
    >
      <ScrollView
        contentContainerStyle={loginScreenStyles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View style={animatedStyle}>
          <LoginForm
            formState={formState}
            onEmailChange={setEmail}
            onPasswordChange={setPassword}
            onToggleSecureEntry={toggleSecureEntry}
            onLoginPress={onLoginPress}
            onEmailSubmit={() => {}}
            onPasswordSubmit={onLoginPress}
          />
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
});

LoginScreen.displayName = 'LoginScreen';

export default LoginScreen;