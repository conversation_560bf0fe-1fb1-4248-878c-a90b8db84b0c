import AsyncStorage from '@react-native-async-storage/async-storage';
import { withErrorHandling } from './errors';
import { getActualQuantityMap } from '../services/binService';
import { STORAGE_KEYS, CACHE } from '../constants/storage';

// Background sync state
let backgroundSyncInterval: NodeJS.Timeout | null = null;
let isBackgroundSyncRunning = false;

/**
 * Cache actual quantities to AsyncStorage
 */
const cacheActualQuantities = async (quantityMap: Map<string, number>): Promise<void> => {
  try {
    const quantityObject = Object.fromEntries(quantityMap);
    await AsyncStorage.setItem(STORAGE_KEYS.ACTUAL_QUANTITIES_CACHE, JSON.stringify(quantityObject));
    await AsyncStorage.setItem(STORAGE_KEYS.ACTUAL_QUANTITIES_TIMESTAMP, Date.now().toString());
  } catch (error) {
    if (__DEV__) console.warn('Failed to cache actual quantities:', error);
  }
};

/**
 * Get cached actual quantities from AsyncStorage
 */
const getCachedActualQuantities = async (): Promise<Map<string, number> | null> => {
  try {
    const [cachedData, timestampStr] = await Promise.all([
      AsyncStorage.getItem(STORAGE_KEYS.ACTUAL_QUANTITIES_CACHE),
      AsyncStorage.getItem(STORAGE_KEYS.ACTUAL_QUANTITIES_TIMESTAMP)
    ]);

    if (!cachedData || !timestampStr) {
      return null;
    }

    const timestamp = parseInt(timestampStr, 10);
    const now = Date.now();

    // Check if cache is still valid
    if (now - timestamp > CACHE.QUANTITY_CACHE_DURATION) {
      return null;
    }

    const quantityObject = JSON.parse(cachedData);
    return new Map(Object.entries(quantityObject).map(([key, value]) => [key, Number(value)]));
  } catch (error) {
    if (__DEV__) console.warn('Failed to get cached actual quantities:', error);
    return null;
  }
};

/**
 * Get actual quantities with smart caching
 * This function first tries to get from cache, and if cache is invalid or missing,
 * it fetches fresh data and updates the cache
 */
export const getActualQuantityMapWithCache = async (): Promise<Map<string, number>> => {
  return withErrorHandling(async () => {
    try {
      // Try to get from cache first (ultra-fast path)
      const cachedQuantities = await getCachedActualQuantities();
      if (cachedQuantities) {
        return cachedQuantities;
      }
    } catch (error) {
      if (__DEV__) console.warn('Cache read failed, fetching fresh data:', error);
    }

    // Cache miss or expired, fetch fresh data
    const quantityMap = await getActualQuantityMap();

    // Cache the fresh data for next time (don't await to avoid blocking)
    cacheActualQuantities(quantityMap).catch(error => {
      if (__DEV__) console.warn('Failed to cache quantities:', error);
    });

    return quantityMap;
  });
};

/**
 * Background sync function to update actual quantities cache
 */
const backgroundSyncQuantities = async (): Promise<void> => {
  if (isBackgroundSyncRunning) {
    return;
  }

  isBackgroundSyncRunning = true;
  
  try {
    if (__DEV__) console.log('Background sync: Updating actual quantities cache...');
    const quantityMap = await getActualQuantityMap();
    await cacheActualQuantities(quantityMap);
    if (__DEV__) console.log('Background sync: Actual quantities cache updated successfully');
  } catch (error) {
    if (__DEV__) console.warn('Background sync: Failed to update actual quantities cache:', error);
  } finally {
    isBackgroundSyncRunning = false;
  }
};

/**
 * Start background sync for actual quantities
 * This will periodically update the cache to ensure quantities are always fresh
 */
export const startBackgroundQuantitySync = (): void => {
  // Don't start if already running
  if (backgroundSyncInterval) {
    return;
  }

  if (__DEV__) console.log('Starting background quantity sync...');

  // Start immediate sync
  backgroundSyncQuantities();
  
  // Set up periodic sync
  backgroundSyncInterval = setInterval(backgroundSyncQuantities, CACHE.QUANTITY_BACKGROUND_SYNC_INTERVAL);
};

/**
 * Stop background sync for actual quantities
 */
export const stopBackgroundQuantitySync = (): void => {
  if (backgroundSyncInterval) {
    if (__DEV__) console.log('Stopping background quantity sync...');
    clearInterval(backgroundSyncInterval);
    backgroundSyncInterval = null;
  }
};

/**
 * Force refresh the actual quantities cache
 * This bypasses the cache and fetches fresh data
 */
export const refreshActualQuantitiesCache = async (): Promise<Map<string, number>> => {
  return withErrorHandling(async () => {
    if (__DEV__) console.log('Force refreshing actual quantities cache...');
    const quantityMap = await getActualQuantityMap();
    await cacheActualQuantities(quantityMap);
    return quantityMap;
  });
};

/**
 * Clear the actual quantities cache
 * Useful for testing or when cache becomes corrupted
 */
export const clearActualQuantitiesCache = async (): Promise<void> => {
  try {
    await Promise.all([
      AsyncStorage.removeItem(STORAGE_KEYS.ACTUAL_QUANTITIES_CACHE),
      AsyncStorage.removeItem(STORAGE_KEYS.ACTUAL_QUANTITIES_TIMESTAMP)
    ]);
    if (__DEV__) console.log('Actual quantities cache cleared');
  } catch (error) {
    if (__DEV__) console.warn('Failed to clear actual quantities cache:', error);
  }
};

/**
 * Get cache status information
 * Useful for debugging and monitoring
 */
export const getCacheStatus = async (): Promise<{
  hasCache: boolean;
  cacheAge: number | null;
  isExpired: boolean;
  itemCount: number;
}> => {
  try {
    const [cachedData, timestampStr] = await Promise.all([
      AsyncStorage.getItem(STORAGE_KEYS.ACTUAL_QUANTITIES_CACHE),
      AsyncStorage.getItem(STORAGE_KEYS.ACTUAL_QUANTITIES_TIMESTAMP)
    ]);

    if (!cachedData || !timestampStr) {
      return {
        hasCache: false,
        cacheAge: null,
        isExpired: true,
        itemCount: 0
      };
    }

    const timestamp = parseInt(timestampStr, 10);
    const now = Date.now();
    const cacheAge = now - timestamp;
    const isExpired = cacheAge > CACHE.QUANTITY_CACHE_DURATION;

    const quantityObject = JSON.parse(cachedData);
    const itemCount = Object.keys(quantityObject).length;

    return {
      hasCache: true,
      cacheAge,
      isExpired,
      itemCount
    };
  } catch (error) {
    if (__DEV__) console.warn('Failed to get cache status:', error);
    return {
      hasCache: false,
      cacheAge: null,
      isExpired: true,
      itemCount: 0
    };
  }
};
