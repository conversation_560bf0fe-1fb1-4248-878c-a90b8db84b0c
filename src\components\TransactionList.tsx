import React, { useCallback, useMemo, memo } from 'react';
import { View, ActivityIndicator, RefreshControl } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Transaction } from '../types/business';
import TransactionCard from './TransactionCard';
import TransactionSkeleton from './TransactionSkeleton';
import { generateAccessibilityLabel } from '../utils/ui';
import { transactionListStyles } from '../styles/TransactionList';
import TransactionEmptyState from './TransactionEmptyState';

// Optimized performance configuration for FlashList
const PERFORMANCE_CONFIG = {
  ESTIMATED_ITEM_SIZE: 110, // Reduced for better memory efficiency
  DRAW_DISTANCE: 1500, // Reduced for better performance
  SCROLL_THRESHOLD: 0.5, // Increased for better scroll performance
  SCROLL_EVENT_THROTTLE: 16, // 60fps
  BLANK_AREA_THRESHOLD: 5, // Reduce blank areas
  CACHE_SIZE: 10, // Limit cache size for memory efficiency
} as const;

interface TransactionListProps {
  transactions: Transaction[];
  transactionType: 'order' | 'invoice';
  loading: boolean;
  refreshing: boolean;
  hasMore: boolean;
  total: number; // Used for accessibility and future features
  searchQuery: string;
  selectedStatuses: string[];
  flashListRef: React.RefObject<FlashList<Transaction> | null>;
  onTransactionPress: (id: string) => void;
  onStatusPress: (status: string) => void;
  onRefresh: () => void;
  onLoadMore: () => void;
  onClearFilters?: () => void;
}

const TransactionListBase: React.FC<TransactionListProps> = ({
  transactions,
  transactionType,
  loading,
  refreshing,
  hasMore,
  total,
  searchQuery,
  selectedStatuses,
  flashListRef,
  onTransactionPress,
  onStatusPress,
  onRefresh,
  onLoadMore,
  onClearFilters,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  // Optimized refresh handler with memoization
  const handleRefresh = useCallback(() => {
    onRefresh();
  }, [onRefresh]);

  // Optimized status press handler with better scroll performance
  const handleStatusPress = useCallback((status: string) => {
    onStatusPress(status);
    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      flashListRef.current?.scrollToOffset({ offset: 0, animated: true });
    });
  }, [onStatusPress, flashListRef]);

  // Memoized key extractor for better performance
  const keyExtractor = useCallback((item: Transaction, index: number) =>
    item?.name || `transaction-${index}`, []);

  // Optimized render item function
  const renderItem = useCallback(({ item }: { item: Transaction }) => (
    <TransactionCard
      item={item}
      transactionType={transactionType}
      onPress={onTransactionPress}
      onStatusPress={handleStatusPress}
    />
  ), [transactionType, onTransactionPress, handleStatusPress]);

  // Optimized footer component
  const footerComponent = useMemo(() => {
    if (!hasMore) return null;
    return (
      <View style={transactionListStyles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary} />
      </View>
    );
  }, [hasMore, theme.colors.primary]);

  // Memoized refresh control
  const refreshControl = useMemo(() => (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={handleRefresh}
      colors={[theme.colors.primary]}
      tintColor={theme.colors.primary}
      progressBackgroundColor="#fff"
      progressViewOffset={20}
      title="Pull to refresh"
      titleColor={theme.colors.onSurface}
    />
  ), [refreshing, handleRefresh, theme.colors.primary, theme.colors.onSurface]);

  // Memoized content container style
  const contentContainerStyle = useMemo(() => ({
    paddingBottom: insets.bottom + 80, // Simplified tab bar height calculation
  }), [insets]);

  // Optimized load more handler
  const handleLoadMore = useCallback(() => {
    onLoadMore();
  }, [onLoadMore]);

  // Show skeleton while loading initial data
  if (loading && (!transactions || transactions.length === 0)) {
    return <TransactionSkeleton count={8} />;
  }

  // Show empty state if no transactions
  if (!loading && (!transactions || transactions.length === 0)) {
    return (
      <TransactionEmptyState
        transactionType={transactionType}
        searchQuery={searchQuery}
        selectedStatuses={selectedStatuses}
        loading={loading}
        onClearFilters={onClearFilters}
      />
    );
  }

  // Generate accessibility label for the list
  const listAccessibilityLabel = generateAccessibilityLabel(
    `${transactionType === 'order' ? 'Orders' : 'Invoices'} list`,
    [
      `${transactions?.length || 0} items`,
      searchQuery ? `Filtered by: ${searchQuery}` : '',
      selectedStatuses.length > 0 ? `Status filters: ${selectedStatuses.join(', ')}` : ''
    ].filter(Boolean)
  );

  return (
    <View style={transactionListStyles.listContainer}>
      <FlashList
        ref={flashListRef}
        data={transactions || []}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        // Accessibility
        accessible={true}
        accessibilityRole="list"
        accessibilityLabel={listAccessibilityLabel}
        accessibilityHint="Swipe up and down to browse transactions. Pull down to refresh."
        // Performance optimizations
        estimatedItemSize={PERFORMANCE_CONFIG.ESTIMATED_ITEM_SIZE}
        drawDistance={PERFORMANCE_CONFIG.DRAW_DISTANCE}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={PERFORMANCE_CONFIG.SCROLL_THRESHOLD}
        // Advanced optimization flags
        removeClippedSubviews={true}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={PERFORMANCE_CONFIG.SCROLL_EVENT_THROTTLE}
        // Components
        refreshControl={refreshControl}
        ListFooterComponent={footerComponent}
        contentContainerStyle={contentContainerStyle}
        // Additional performance optimizations
        getItemType={() => 'transaction'} // Consistent item type for better recycling
        disableAutoLayout={false} // Let FlashList handle layout automatically
      />
    </View>
  );
};

// Memoize the component with custom comparison for better performance
const TransactionList = memo(TransactionListBase, (prevProps, nextProps) => {
  // Quick reference check
  if (prevProps === nextProps) return true;

  // Check critical props that affect rendering
  if (
    prevProps.loading !== nextProps.loading ||
    prevProps.refreshing !== nextProps.refreshing ||
    prevProps.hasMore !== nextProps.hasMore ||
    prevProps.total !== nextProps.total ||
    prevProps.searchQuery !== nextProps.searchQuery ||
    prevProps.transactionType !== nextProps.transactionType
  ) {
    return false;
  }

  // Check if transactions array changed
  if (prevProps.transactions !== nextProps.transactions) {
    // If arrays are different references, check if they have the same content
    if (!prevProps.transactions || !nextProps.transactions) {
      return prevProps.transactions === nextProps.transactions;
    }

    if (prevProps.transactions.length !== nextProps.transactions.length) {
      return false;
    }

    // Quick check of first and last items
    const prevFirst = prevProps.transactions[0];
    const nextFirst = nextProps.transactions[0];
    const prevLast = prevProps.transactions[prevProps.transactions.length - 1];
    const nextLast = nextProps.transactions[nextProps.transactions.length - 1];

    if (prevFirst?.name !== nextFirst?.name || prevLast?.name !== nextLast?.name) {
      return false;
    }
  }

  // Check selectedStatuses array
  if (prevProps.selectedStatuses !== nextProps.selectedStatuses) {
    if (prevProps.selectedStatuses.length !== nextProps.selectedStatuses.length) {
      return false;
    }

    for (let i = 0; i < prevProps.selectedStatuses.length; i++) {
      if (prevProps.selectedStatuses[i] !== nextProps.selectedStatuses[i]) {
        return false;
      }
    }
  }

  return true;
});

export default TransactionList;
