/**
 * Data Synchronization Orchestrator
 * 
 * Handles data synchronization between local storage and remote server.
 */

import {
  syncAllData as syncDatabaseData,
  closeDatabase,
  clearCache,
  getDatabaseStats,
  getItemsWithPricesWithCache
} from '../utils/sqliteService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';
import { backgroundOperationsManager, BackgroundOperationType } from './backgroundOperations';

// Constants
const STORAGE_KEYS = {
  LAST_SYNC: 'last_data_sync',
  SYNC_STATS: 'last_sync_stats'
};

const SYNC_CONFIG = {
  DB_CLOSE_DELAY: 100,       // ms to wait after closing database - reduced for faster cleanup
  AUTO_SYNC_INTERVAL: 24,    // hours between auto-syncs
  SYNC_TIMEOUT: 20 * 1000,   // 20 second timeout for sync operations - reduced from 60s
  RETRY_DELAY: 200           // ms between retries - reduced from 500ms
};

// Sync state management
let syncState = {
  isSyncing: false,
  lastSyncTime: null as Date | null,
  syncPromise: null as Promise<boolean> | null
};

/**
 * Check if sync is needed based on last sync time
 */
export const isSyncNeeded = async (): Promise<boolean> => {
  try {
    const lastSyncStr = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
    if (!lastSyncStr) return true;

    const lastSync = new Date(lastSyncStr);
    const now = new Date();
    const hoursSinceSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);

    return hoursSinceSync >= SYNC_CONFIG.AUTO_SYNC_INTERVAL;
  } catch (error) {
    if (__DEV__) console.warn('Error checking sync status:', error);
    return true; // Default to needing sync if we can't check
  }
};

/**
 * Get last sync time
 */
export const getLastSyncTime = async (): Promise<Date | null> => {
  try {
    const lastSyncStr = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);
    return lastSyncStr ? new Date(lastSyncStr) : null;
  } catch (error) {
    if (__DEV__) console.warn('Error getting last sync time:', error);
    return null;
  }
};

/**
 * Store sync completion time and stats
 */
const storeSyncCompletion = async (stats?: any): Promise<void> => {
  try {
    const now = new Date().toISOString();
    await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, now);
    
    if (stats) {
      await AsyncStorage.setItem(STORAGE_KEYS.SYNC_STATS, JSON.stringify(stats));
    }
    
    syncState.lastSyncTime = new Date(now);
  } catch (error) {
    if (__DEV__) console.warn('Error storing sync completion:', error);
  }
};

/**
 * Get sync statistics
 */
export const getSyncStats = async (): Promise<any> => {
  try {
    const statsStr = await AsyncStorage.getItem(STORAGE_KEYS.SYNC_STATS);
    return statsStr ? JSON.parse(statsStr) : null;
  } catch (error) {
    if (__DEV__) console.warn('Error getting sync stats:', error);
    return null;
  }
};

/**
 * Sync all data with timeout and retry logic
 */
export const syncAllData = async (): Promise<boolean> => {
  // Return existing promise if sync is already in progress
  if (syncState.isSyncing && syncState.syncPromise) {
    return syncState.syncPromise;
  }

  // Create new sync promise
  syncState.syncPromise = performSync();
  
  try {
    return await syncState.syncPromise;
  } finally {
    syncState.syncPromise = null;
  }
};

/**
 * Internal sync implementation with timeout and error handling
 */
const performSync = async (): Promise<boolean> => {
  if (syncState.isSyncing) {
    if (__DEV__) console.log('Sync already in progress, skipping');
    return true;
  }

  syncState.isSyncing = true;

  try {
    if (__DEV__) console.log('Starting data sync...');

    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Sync timeout')), SYNC_CONFIG.SYNC_TIMEOUT);
    });

    // Race between sync and timeout
    const syncPromise = syncDatabaseData();
    const result = await Promise.race([syncPromise, timeoutPromise]);

    // Get database stats after sync
    const stats = await getDatabaseStats().catch(() => null);

    // Store sync completion
    await storeSyncCompletion(stats);

    if (__DEV__) {
      console.log('Data sync completed successfully');
      if (stats) {
        console.log('Database stats:', stats);
      }
    }

    return true;
  } catch (error) {
    if (__DEV__) {
      console.error('Data sync failed:', error);
    }
    return false;
  } finally {
    syncState.isSyncing = false;

    // Close database after a short delay to ensure all operations complete
    setTimeout(() => {
      closeDatabase().catch(() => {
        // Ignore errors when closing database
      });
    }, SYNC_CONFIG.DB_CLOSE_DELAY);
  }
};

/**
 * Force sync regardless of last sync time
 */
export const forceSyncAllData = async (): Promise<boolean> => {
  if (__DEV__) console.log('Force sync requested');
  return syncAllData();
};

/**
 * Clear sync data and force next sync
 */
export const clearSyncData = async (): Promise<void> => {
  try {
    await AsyncStorage.multiRemove([STORAGE_KEYS.LAST_SYNC, STORAGE_KEYS.SYNC_STATS]);
    syncState.lastSyncTime = null;
    if (__DEV__) console.log('Sync data cleared');
  } catch (error) {
    if (__DEV__) console.warn('Error clearing sync data:', error);
  }
};

/**
 * Get current sync status
 */
export const getSyncStatus = () => {
  return {
    isSyncing: syncState.isSyncing,
    lastSyncTime: syncState.lastSyncTime
  };
};

/**
 * Initialize auto-sync on app state changes
 * Returns cleanup function to remove listeners
 */
export const initializeAutoSync = (): (() => void) => {
  const subscription = AppState.addEventListener('change', async (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      try {
        const needsSync = await isSyncNeeded();
        if (needsSync && !syncState.isSyncing) {
          syncAllData().catch(() => {
            // Ignore errors in background sync
          });
        }
      } catch (error) {
        // Ignore errors in background check
      }
    }
  });

  // Return cleanup function
  return () => {
    subscription.remove();
  };
};

/**
 * Preload data if needed (compatibility function)
 */
export const preloadDataIfNeeded = async (): Promise<boolean> => {
  try {
    const needsSync = await isSyncNeeded();
    if (needsSync) {
      return await syncAllData();
    }
    return true;
  } catch (error) {
    if (__DEV__) console.warn('Error in preload data check:', error);
    return false;
  }
};

/**
 * Get cached items with prices
 * @param territory Optional territory filter (defaults to 'All Territories')
 * @param customerGroup Optional customer group filter (defaults to 'All Customer Groups')
 */
export const getCachedItemsWithPrices = async (
  territory: string = 'All Territories',
  customerGroup: string = 'All Customer Groups'
) => {
  try {
    return await getItemsWithPricesWithCache(territory, customerGroup);
  } catch (error) {
    if (__DEV__) console.warn('Error getting cached items:', error);
    return [];
  }
};

/**
 * Clear all caches
 */
export const clearAllCaches = async (): Promise<void> => {
  try {
    clearCache();
    if (__DEV__) console.log('All caches cleared');
  } catch (error) {
    if (__DEV__) console.warn('Error clearing caches:', error);
  }
};
