import React, { useRef, useCallback, useMemo, useEffect, memo } from 'react';
import { View, RefreshControl, Keyboard } from 'react-native';
import { FlashList } from '@shopify/flash-list';
import { useTheme } from 'react-native-paper';

import { Customer } from '../types/business';
import CustomerCard from './CustomerCard';
import { CustomerSkeleton } from './CustomerSkeleton';
import CustomerEmptyState from './CustomerEmptyState';
import { customerListStyles } from '../styles/CustomerList';

const PERFORMANCE_CONFIG = {
  ESTIMATED_ITEM_SIZE: 70,
  DRAW_DISTANCE: 1500, // Aligned with transaction screen for consistency
  SCROLL_THRESHOLD: 0.5, // Aligned with transaction screen for consistency
  SCROLL_EVENT_THROTTLE: 16, // Added for consistency
  KEYBOARD_DISMISS_THRESHOLD: 30,
  BOTTOM_PADDING: 100, // Padding to ensure last item is accessible above bottom navigation
} as const;

interface CustomerListProps {
  customers: Customer[];
  loading: boolean;
  refreshing: boolean;
  onRefresh: () => void;
  onPlaceOrder: (customer: Customer) => void;
  onNavigateToInvoices: (customerId: string) => void;
  EmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
  searchQuery?: string;
  selectedFilters?: string[];
  selectedSort?: string;
  onClearFilters?: () => void;
}

export const CustomerList: React.FC<CustomerListProps> = memo(({
  customers,
  loading,
  refreshing,
  onRefresh,
  onPlaceOrder,
  onNavigateToInvoices,
  EmptyComponent,
  searchQuery,
  selectedFilters,
  selectedSort,
  onClearFilters
}) => {
  const flashListRef = useRef<FlashList<Customer>>(null);
  const theme = useTheme();
  const lastScrollY = useRef(0);

  const renderCustomerItem = useCallback(({ item }: { item: Customer }) => (
    <CustomerCard
      customer={item}
      onPlaceOrder={() => onPlaceOrder(item)}
      onNavigateToInvoices={() => onNavigateToInvoices(item.name)}
    />
  ), [onPlaceOrder, onNavigateToInvoices]);

  const keyExtractor = useCallback((item: Customer) => item.name, []);
  const handleScroll = useCallback((event: any) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    const scrollDelta = Math.abs(currentScrollY - lastScrollY.current);

    // Only dismiss keyboard if significant scroll movement
    if (scrollDelta > PERFORMANCE_CONFIG.KEYBOARD_DISMISS_THRESHOLD) {
      Keyboard.dismiss();
      lastScrollY.current = currentScrollY;
    }
  }, []);

  const scrollToTop = useCallback(() => {
    flashListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  useEffect(() => {
    (global as any).customerScreenScrollToTop = scrollToTop;
    return () => {
      (global as any).customerScreenScrollToTop = null;
    };
  }, [scrollToTop]);

  useEffect(() => {
    if (customers.length === 0) return;
    const timer = setTimeout(() => {
      flashListRef.current?.scrollToOffset({ offset: 0, animated: false });
    }, 100);
    return () => clearTimeout(timer);
  }, [customers.length]);

  const emptyListComponent = useMemo(() => {
    // 🚀 CRITICAL: NEVER show empty state while loading to prevent flash
    // This applies to ANY loading state - initial load, navigation, refresh, etc.
    if (loading) {
      return null;
    }

    // 🚀 ADDITIONAL SAFETY: Only show empty state if we have confirmed no data
    if (!customers || customers.length === 0) {
      return EmptyComponent || (
        <CustomerEmptyState
          searchQuery={searchQuery}
          selectedFilters={selectedFilters}
          selectedSort={selectedSort}
          onClearFilters={onClearFilters}
          loading={loading} // 🚀 Pass loading state to prevent empty state during loading
        />
      );
    }
    return null;
  }, [customers, EmptyComponent, searchQuery, selectedFilters, selectedSort, onClearFilters, loading]);

  const refreshControlComponent = useMemo(() => (
    <RefreshControl
      refreshing={refreshing}
      onRefresh={onRefresh}
      colors={[theme.colors.primary]}
      progressViewOffset={20}
      progressBackgroundColor="#fff"
      tintColor={theme.colors.primary}
    />
  ), [refreshing, onRefresh, theme.colors.primary]);

  // 🚀 CRITICAL LOADING PROTECTION: Show skeleton during ANY loading state
  // This includes: initial load, navigation transitions, focus changes, etc.
  // NEVER allow FlashList to render with empty data during loading
  if (loading) {
    return <CustomerSkeleton count={12} />;
  }

  return (
    <View style={customerListStyles.container}>
      <FlashList
        ref={flashListRef}
        data={customers || []}
        renderItem={renderCustomerItem}
        keyExtractor={keyExtractor}
        estimatedItemSize={PERFORMANCE_CONFIG.ESTIMATED_ITEM_SIZE}
        drawDistance={PERFORMANCE_CONFIG.DRAW_DISTANCE}
        onEndReachedThreshold={PERFORMANCE_CONFIG.SCROLL_THRESHOLD}
        showsVerticalScrollIndicator={true}
        removeClippedSubviews={true}
        onScroll={handleScroll}
        scrollEventThrottle={PERFORMANCE_CONFIG.SCROLL_EVENT_THROTTLE}
        refreshControl={refreshControlComponent}
        ListEmptyComponent={emptyListComponent}
        contentContainerStyle={customerListStyles.contentContainer}
        estimatedListSize={{ height: 800, width: 400 }}
        getItemType={() => 'customer'}
        disableAutoLayout={false}
        overrideItemLayout={(layout) => {
          // Ultra-precise layout for consistent performance
          layout.size = PERFORMANCE_CONFIG.ESTIMATED_ITEM_SIZE;
        }}
      />
    </View>
  );
}, (prevProps, nextProps) => {
  // 🚀 SMART MEMOIZATION - Only re-render when necessary
  return (
    prevProps.customers === nextProps.customers &&
    prevProps.loading === nextProps.loading &&
    prevProps.refreshing === nextProps.refreshing &&
    prevProps.searchQuery === nextProps.searchQuery &&
    JSON.stringify(prevProps.selectedFilters) === JSON.stringify(nextProps.selectedFilters) &&
    prevProps.selectedSort === nextProps.selectedSort
  );
});


