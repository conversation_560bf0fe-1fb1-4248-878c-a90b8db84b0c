import React from 'react';
import { EventEmitter } from 'events';

export enum BackgroundOperationType {
  LOGIN_SYNC = 'login_sync',
  PRELOAD = 'preload',
  DATABASE_INIT = 'database_init'
}

export interface BackgroundOperationsState {
  isBackgroundOperationRunning: boolean;
}

class BackgroundOperationsManager extends EventEmitter {
  private operationCount = 0;
  private emitTimeout: NodeJS.Timeout | null = null;

  isBackgroundOperationRunning(): boolean {
    return this.operationCount > 0;
  }

  startOperation(type: BackgroundOperationType): string {
    const id = `${type}_${Date.now()}`;
    this.operationCount++;
    this.debouncedEmit();
    return id;
  }

  completeOperation(): void {
    this.operationCount = Math.max(0, this.operationCount - 1);
    this.debouncedEmit();
  }

  failOperation(): void {
    this.operationCount = Math.max(0, this.operationCount - 1);
    this.debouncedEmit();
  }

  // Debounce state change emissions to prevent excessive re-renders
  private debouncedEmit(): void {
    if (this.emitTimeout) {
      clearTimeout(this.emitTimeout);
    }
    this.emitTimeout = setTimeout(() => {
      this.emit('stateChanged');
      this.emitTimeout = null;
    }, 50); // 50ms debounce
  }

  getState(): BackgroundOperationsState {
    return {
      isBackgroundOperationRunning: this.operationCount > 0
    };
  }
}

export const backgroundOperationsManager = new BackgroundOperationsManager();

export const useBackgroundOperations = () => {
  const [state, setState] = React.useState<BackgroundOperationsState>(
    backgroundOperationsManager.getState()
  );

  React.useEffect(() => {
    const handleStateChange = () => {
      setState(backgroundOperationsManager.getState());
    };

    backgroundOperationsManager.on('stateChanged', handleStateChange);

    return () => {
      backgroundOperationsManager.off('stateChanged', handleStateChange);
    };
  }, []);

  return state;
};
