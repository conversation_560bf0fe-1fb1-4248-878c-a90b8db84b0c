import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const searchComponentStyles = StyleSheet.create({
  // Main container styles
  container: {
    width: '100%',
    paddingVertical: MD.SPACING.XXSMALL,
  },

  // Input field styles
  input: {
    backgroundColor: 'rgba(0, 0, 0, 0.02)',
    fontSize: 16,
    height: 42,
    borderWidth: 0.3,
    borderRadius: 28,
    borderColor: MD.DIVIDER,
  },

  // Icon styles
  searchIcon: {
    marginRight: 0,
  },
  clearIcon: {
    marginLeft: 0,
  },

  // Outline style
  outlineStyle: {
    borderWidth: 0.3,
  },

  // Legacy styles (kept for backward compatibility if needed)
  legacyContainer: {
    backgroundColor: NEUTRAL.WHITE,
    paddingHorizontal: MD.SPACING.LARGE,
    paddingVertical: MD.SPACING.SMALL,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: NEUTRAL.BACKGROUND_LIGHT,
    borderRadius: MD.BORDER_RADIUS.LARGE,
    height: 40,
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: MD.SPACING.MEDIUM,
    fontSize: MD.TYPOGRAPHY.BODY1.fontSize,
    color: NEUTRAL.TEXT_PRIMARY,
  },
  clearButton: {
    paddingHorizontal: MD.SPACING.MEDIUM,
  },
});
