import React, { memo, useMemo } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { Redirect } from 'expo-router';
import { useTheme } from 'react-native-paper';
import { useAppInitialization } from '../src/hooks/useAppInitialization';

const Index: React.FC = memo(() => {
  const { redirectTo, isInitializing } = useAppInitialization();
  const theme = useTheme();

  const containerStyle = useMemo(() => [
    styles.container,
    { backgroundColor: theme.colors.background }
  ], [theme.colors.background]);

  if (redirectTo) {
    return <Redirect href={redirectTo} />;
  }

  if (!isInitializing) {
    return null;
  }

  return (
    <View style={containerStyle}>
      <ActivityIndicator
        size="large"
        color={theme.colors.primary}
        hidesWhenStopped={true}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

Index.displayName = 'Index';

export default Index;