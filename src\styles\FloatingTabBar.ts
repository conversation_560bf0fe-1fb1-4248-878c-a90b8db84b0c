import { StyleSheet } from 'react-native';
import { NEUTRAL, BRAND } from '../constants/colors';
import MD from '../constants/design';

export const floatingTabBarStyles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: '5%',
    right: '5%',
    height: 70,
    borderRadius: 35,
    overflow: 'hidden',
    elevation: 15,
  },
  solidContainer: {
    flex: 1,
    borderRadius: 35,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    backgroundColor: 'rgba(255, 255, 255, 1)',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  tabsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabLabel: {
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
    letterSpacing: 0.3,
    textAlign: 'center',
  },
});
