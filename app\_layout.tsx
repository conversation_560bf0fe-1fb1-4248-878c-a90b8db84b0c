import React from 'react';
import { Stack } from 'expo-router';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Provider as PaperProvider } from 'react-native-paper';
import { theme } from '../src/constants/theme';
import { useAppLifecycle } from '../src/orchestrators/appLifecycle';
import { memoryOptimizer } from '../src/utils/memoryOptimizer';

const RootLayout = () => {
  useAppLifecycle();

  React.useEffect(() => {
    // Simplified memory monitoring - no complex setup needed
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <PaperProvider theme={theme}>
        <Stack screenOptions={{ headerShown: false }}>
          <Stack.Screen name="index" />
          <Stack.Screen name="(auth)" />
          <Stack.Screen name="(tabs)" />
          <Stack.Screen name="create-order" />
          <Stack.Screen name="checkout" />
        </Stack>
      </PaperProvider>
    </GestureHandlerRootView>
  );
};

export default RootLayout;