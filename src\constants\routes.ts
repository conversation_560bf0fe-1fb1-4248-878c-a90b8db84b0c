/**
 * Navigation Routes and Sources
 * 
 * Consolidated navigation constants from across the application
 */

export const ROUTES = {
  // Tab routes
  HOME: '/home',
  CUSTOMERS: '/customers',
  ORDERS: '/orders',
  INVOICES: '/invoices',
  CARTS_TAB: '/carts',

  // Full tab routes (for navigation)
  HOME_FULL: '/(tabs)/home',
  CUSTOMERS_FULL: '/(tabs)/customers',
  ORDERS_FULL: '/(tabs)/orders',
  INVOICES_FULL: '/(tabs)/invoices',
  CARTS_TAB_FULL: '/(tabs)/carts',

  // Auth routes
  LOGIN: '/(auth)/login',

  // Modal/Stack routes
  CHECKOUT: '/checkout',
  CREATE_ORDER: '/create-order',
  CARTS: '/carts',
};

export const NAVIGATION_SOURCES = {
  CART: 'cart',
  CREATE_ORDER: 'create-order',
  CUSTOMER: 'customer',
};
